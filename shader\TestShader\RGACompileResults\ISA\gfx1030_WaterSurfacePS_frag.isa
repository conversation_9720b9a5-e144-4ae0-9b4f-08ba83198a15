_amdgpu_ps_main:
	s_mov_b64 s[34:35], exec                                   // 000000000000: BEA2047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s16, s1                                          // 000000000008: BE900301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s17, s1                                          // 000000000014: BE910301
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx4 s[48:51], s[16:17], 0x20                    // 00000000001C: F4080C08 FA000020
	s_load_dwordx8 s[0:7], s[16:17], null                      // 000000000024: F40C0008 FA000000
	v_interp_p1_f32_e32 v2, v0, attr5.x                        // 00000000002C: C8081400
	v_interp_p1_f32_e32 v3, v0, attr5.y                        // 000000000030: C80C1500
	v_interp_p1_f32_e32 v12, v0, attr1.x                       // 000000000034: C8300400
	v_interp_p1_f32_e32 v13, v0, attr1.y                       // 000000000038: C8340500
	v_interp_p1_f32_e32 v14, v0, attr0.y                       // 00000000003C: C8380100
	v_interp_p2_f32_e32 v2, v1, attr5.x                        // 000000000040: C8091401
	v_interp_p2_f32_e32 v3, v1, attr5.y                        // 000000000044: C80D1501
	v_interp_p2_f32_e32 v12, v1, attr1.x                       // 000000000048: C8310401
	v_interp_p2_f32_e32 v13, v1, attr1.y                       // 00000000004C: C8350501
	v_interp_p2_f32_e32 v14, v1, attr0.y                       // 000000000050: C8390101
	v_mul_f32_e32 v6, 0.5, v2                                  // 000000000054: 100C04F0
	v_mul_f32_e32 v7, 0.5, v3                                  // 000000000058: 100E06F0
	v_fma_f32 v8, 4.0, v12, v2                                 // 00000000005C: D54B0008 040A18F6
	v_fma_f32 v9, 4.0, v13, v3                                 // 000000000064: D54B0009 040E1AF6
	v_interp_p1_f32_e32 v15, v0, attr0.z                       // 00000000006C: C83C0200
	v_fma_f32 v10, v12, 2.0, -v6                               // 000000000070: D54B000A 8419E90C
	v_fma_f32 v11, v13, 2.0, -v7                               // 000000000078: D54B000B 841DE90D
	v_interp_p1_f32_e32 v18, v0, attr3.y                       // 000000000080: C8480D00
	s_waitcnt lgkmcnt(0)                                       // 000000000084: BF8CC07F
	s_clause 0x1                                               // 000000000088: BFA10001
	image_sample v[6:8], v[8:9], s[0:7], s[48:51] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000008C: F0800708 01800608
	image_sample v[9:11], v[10:11], s[0:7], s[48:51] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000094: F0800708 0180090A
	s_load_dwordx4 s[44:47], s[16:17], null                    // 00000000009C: F4080B08 FA000000
	v_interp_p2_f32_e32 v15, v1, attr0.z                       // 0000000000A4: C83D0201
	s_clause 0x1                                               // 0000000000A8: BFA10001
	s_load_dwordx16 s[0:15], s[16:17], 0x30                    // 0000000000AC: F4100008 FA000030
	s_load_dwordx16 s[16:31], s[16:17], 0x70                   // 0000000000B4: F4100408 FA000070
	v_interp_p2_f32_e32 v18, v1, attr3.y                       // 0000000000BC: C8490D01
	v_add_f32_e32 v2, v2, v2                                   // 0000000000C0: 06040502
	v_add_f32_e32 v3, v3, v3                                   // 0000000000C4: 06060703
	v_fmac_f32_e32 v2, 0x41000000, v12                         // 0000000000C8: 560418FF 41000000
	v_fmac_f32_e32 v3, 0x41000000, v13                         // 0000000000D0: 56061AFF 41000000
	s_waitcnt lgkmcnt(0)                                       // 0000000000D8: BF8CC07F
	s_buffer_load_dwordx8 s[36:43], s[44:47], null             // 0000000000DC: F42C0916 FA000000
	s_waitcnt vmcnt(0)                                         // 0000000000E4: BF8C3F70
	v_add_f32_e32 v7, v10, v7                                  // 0000000000E8: 060E0F0A
	v_interp_p1_f32_e32 v10, v0, attr0.x                       // 0000000000EC: C8280000
	v_add_f32_e32 v6, v9, v6                                   // 0000000000F0: 060C0D09
	v_mul_f32_e32 v9, v14, v14                                 // 0000000000F4: 10121D0E
	v_add_f32_e32 v8, v11, v8                                  // 0000000000F8: 0610110B
	v_fma_f32 v7, v7, 2.0, -2.0                                // 0000000000FC: D54B0007 03D5E907
	v_interp_p2_f32_e32 v10, v1, attr0.x                       // 000000000104: C8290001
	v_fma_f32 v6, v6, 2.0, -2.0                                // 000000000108: D54B0006 03D5E906
	v_fma_f32 v8, v8, 2.0, -2.0                                // 000000000110: D54B0008 03D5E908
	v_mul_f32_e32 v11, v7, v7                                  // 000000000118: 10160F07
	v_fmac_f32_e32 v9, v10, v10                                // 00000000011C: 5612150A
	v_fmac_f32_e32 v11, v6, v6                                 // 000000000120: 56160D06
	v_fmac_f32_e32 v9, v15, v15                                // 000000000124: 56121F0F
	v_fmac_f32_e32 v11, v8, v8                                 // 000000000128: 56161108
	v_rsq_f32_e32 v16, v9                                      // 00000000012C: 7E205D09
	v_cmp_neq_f32_e32 vcc_lo, 0, v9                            // 000000000130: 7C1A1280
	v_rsq_f32_e32 v17, v11                                     // 000000000134: 7E225D0B
	v_cndmask_b32_e32 v9, 0, v16, vcc_lo                       // 000000000138: 02122080
	v_cmp_neq_f32_e32 vcc_lo, 0, v11                           // 00000000013C: 7C1A1680
	v_interp_p1_f32_e32 v16, v0, attr4.y                       // 000000000140: C8401100
	v_mul_f32_e32 v14, v9, v14                                 // 000000000144: 101C1D09
	v_cndmask_b32_e32 v11, 0, v17, vcc_lo                      // 000000000148: 02162280
	v_mul_f32_e32 v10, v9, v10                                 // 00000000014C: 10141509
	v_interp_p1_f32_e32 v17, v0, attr3.x                       // 000000000150: C8440C00
	v_interp_p2_f32_e32 v16, v1, attr4.y                       // 000000000154: C8411101
	v_mul_f32_e32 v7, v11, v7                                  // 000000000158: 100E0F0B
	v_mul_f32_e32 v6, v11, v6                                  // 00000000015C: 100C0D0B
	v_mul_f32_e32 v8, v11, v8                                  // 000000000160: 1010110B
	v_interp_p1_f32_e32 v11, v0, attr4.x                       // 000000000164: C82C1000
	v_interp_p2_f32_e32 v17, v1, attr3.x                       // 000000000168: C8450C01
	s_waitcnt lgkmcnt(0)                                       // 00000000016C: BF8CC07F
	v_fmac_f32_e32 v14, s43, v7                                // 000000000170: 561C0E2B
	v_mul_f32_e32 v7, v9, v15                                  // 000000000174: 100E1F09
	v_fmac_f32_e32 v10, s43, v6                                // 000000000178: 56140C2B
	v_interp_p1_f32_e32 v9, v0, attr3.w                        // 00000000017C: C8240F00
	v_interp_p2_f32_e32 v11, v1, attr4.x                       // 000000000180: C82D1001
	v_mul_f32_e32 v6, v14, v14                                 // 000000000184: 100C1D0E
	v_fmac_f32_e32 v7, s43, v8                                 // 000000000188: 560E102B
	v_interp_p1_f32_e32 v8, v0, attr4.w                        // 00000000018C: C8201300
	v_interp_p2_f32_e32 v9, v1, attr3.w                        // 000000000190: C8250F01
	v_fmac_f32_e32 v6, v10, v10                                // 000000000194: 560C150A
	v_interp_p2_f32_e32 v8, v1, attr4.w                        // 000000000198: C8211301
	v_rcp_f32_e64 v9, v9 div:2                                 // 00000000019C: D5AA0009 18000109
	v_fmac_f32_e32 v6, v7, v7                                  // 0000000001A4: 560C0F07
	v_rcp_f32_e64 v8, v8 div:2                                 // 0000000001A8: D5AA0008 18000108
	v_rsq_f32_e32 v15, v6                                      // 0000000001B0: 7E1E5D06
	v_cmp_neq_f32_e32 vcc_lo, 0, v6                            // 0000000001B4: 7C1A0C80
	v_fma_f32 v22, v8, v11, 0.5                                // 0000000001B8: D54B0016 03C21708
	v_fma_f32 v23, v8, v16, 0.5                                // 0000000001C0: D54B0017 03C22108
	v_fma_f32 v11, v9, v18, 0.5                                // 0000000001C8: D54B000B 03C22509
	v_cndmask_b32_e32 v15, 0, v15, vcc_lo                      // 0000000001D0: 021E1E80
	v_mul_f32_e32 v20, v15, v10                                // 0000000001D4: 1028150F
	v_mul_f32_e32 v21, v15, v7                                 // 0000000001D8: 102A0F0F
	v_fma_f32 v10, v9, v17, 0.5                                // 0000000001DC: D54B000A 03C22309
	v_fmac_f32_e32 v22, 0x3c23d70a, v20                        // 0000000001E4: 562C28FF 3C23D70A
	v_fmac_f32_e32 v23, 0x3c23d70a, v21                        // 0000000001EC: 562E2AFF 3C23D70A
	v_fmac_f32_e32 v10, 0x3ca3d70a, v20                        // 0000000001F4: 561428FF 3CA3D70A
	v_fmac_f32_e32 v11, 0x3ca3d70a, v21                        // 0000000001FC: 56162AFF 3CA3D70A
	image_sample v[6:8], v[22:23], s[8:15], s[48:51] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000204: F0800708 01820616
	image_sample v[9:11], v[10:11], s[0:7], s[48:51] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000020C: F0800708 0180090A
	image_sample v16, v[22:23], s[24:31], s[48:51] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000214: F0800108 01861016
	s_and_b64 exec, exec, s[34:35]                             // 00000000021C: 87FE227E
	image_sample v2, v[2:3], s[16:23], s[48:51] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000220: F0800108 01840202
	v_interp_p1_f32_e32 v3, v0, attr2.y                        // 000000000228: C80C0900
	v_interp_p1_f32_e32 v12, v0, attr2.x                       // 00000000022C: C8300800
	v_interp_p1_f32_e32 v0, v0, attr2.z                        // 000000000230: C8000A00
	v_mul_f32_e32 v14, v15, v14                                // 000000000234: 101C1D0F
	s_buffer_load_dwordx8 s[0:7], s[44:47], 0x20               // 000000000238: F42C0016 FA000020
	v_interp_p2_f32_e32 v3, v1, attr2.y                        // 000000000240: C80D0901
	v_interp_p2_f32_e32 v12, v1, attr2.x                       // 000000000244: C8310801
	v_interp_p2_f32_e32 v0, v1, attr2.z                        // 000000000248: C8010A01
	v_mul_f32_e32 v13, v3, v3                                  // 00000000024C: 101A0703
	v_fmac_f32_e32 v13, v12, v12                               // 000000000250: 561A190C
	v_fmac_f32_e32 v13, v0, v0                                 // 000000000254: 561A0100
	v_rsq_f32_e32 v1, v13                                      // 000000000258: 7E025D0D
	v_cmp_neq_f32_e32 vcc_lo, 0, v13                           // 00000000025C: 7C1A1A80
	v_cndmask_b32_e32 v1, 0, v1, vcc_lo                        // 000000000260: 02020280
	v_fmaak_f32 v13, v1, v3, 0x3f5d267b                        // 000000000264: 5A1A0701 3F5D267B
	v_fmaak_f32 v17, v1, v12, 0x3edd267b                       // 00000000026C: 5A221901 3EDD267B
	v_fmaak_f32 v19, v1, v0, 0x3e84b0b1                        // 000000000274: 5A260101 3E84B0B1
	v_mul_f32_e32 v12, v1, v12                                 // 00000000027C: 10181901
	v_mul_f32_e32 v3, v1, v3                                   // 000000000280: 10060701
	v_mul_f32_e32 v18, v13, v13                                // 000000000284: 10241B0D
	v_mul_f32_e32 v0, v1, v0                                   // 000000000288: 10000101
	v_mul_f32_e32 v12, v20, v12                                // 00000000028C: 10181914
	v_fmac_f32_e32 v18, v17, v17                               // 000000000290: 56242311
	v_fmac_f32_e32 v12, v14, v3                                // 000000000294: 5618070E
	v_fmac_f32_e32 v18, v19, v19                               // 000000000298: 56242713
	v_fmac_f32_e32 v12, v21, v0                                // 00000000029C: 56180115
	v_rsq_f32_e32 v22, v18                                     // 0000000002A0: 7E2C5D12
	v_cmp_neq_f32_e32 vcc_lo, 0, v18                           // 0000000002A4: 7C1A2480
	v_cndmask_b32_e32 v18, 0, v22, vcc_lo                      // 0000000002A8: 02242C80
	v_mul_f32_e32 v13, v18, v13                                // 0000000002AC: 101A1B12
	v_mul_f32_e32 v1, v18, v17                                 // 0000000002B0: 10022312
	v_mul_f32_e32 v0, v18, v19                                 // 0000000002B4: 10002712
	v_mul_f32_e32 v3, v14, v13                                 // 0000000002B8: 10061B0E
	v_fmac_f32_e32 v3, v20, v1                                 // 0000000002BC: 56060314
	v_sub_f32_e64 v1, 1.0, v12 clamp                           // 0000000002C0: D5048001 000218F2
	v_fmac_f32_e32 v3, v21, v0                                 // 0000000002C8: 56060115
	v_log_f32_e32 v0, v1                                       // 0000000002CC: 7E004F01
	v_max_f32_e32 v1, 0, v3                                    // 0000000002D0: 20020680
	v_rcp_f32_e32 v3, v5                                       // 0000000002D4: 7E065505
	s_waitcnt vmcnt(3)                                         // 0000000002D8: BF8C3F73
	v_mul_f32_e32 v5, s42, v6                                  // 0000000002DC: 100A0C2A
	v_mul_f32_e32 v6, s42, v7                                  // 0000000002E0: 100C0E2A
	v_mul_f32_e32 v7, s42, v8                                  // 0000000002E4: 100E102A
	v_log_f32_e32 v1, v1                                       // 0000000002E8: 7E024F01
	v_mul_legacy_f32_e32 v0, s40, v0                           // 0000000002EC: 0E000028
	s_waitcnt lgkmcnt(0)                                       // 0000000002F0: BF8CC07F
	v_rcp_f32_e32 v8, s4                                       // 0000000002F4: 7E105404
	s_waitcnt vmcnt(2)                                         // 0000000002F8: BF8C3F72
	v_fma_f32 v9, v9, s41, -v5                                 // 0000000002FC: D54B0009 84145309
	v_fma_f32 v10, v10, s41, -v6                               // 000000000304: D54B000A 8418530A
	v_fma_f32 v11, v11, s41, -v7                               // 00000000030C: D54B000B 841C530B
	v_rcp_f32_e32 v3, v3                                       // 000000000314: 7E065503
	v_exp_f32_e32 v0, v0                                       // 000000000318: 7E004B00
	v_mul_legacy_f32_e32 v1, s0, v1                            // 00000000031C: 0E020200
	v_exp_f32_e32 v1, v1                                       // 000000000320: 7E024B01
	s_waitcnt vmcnt(1)                                         // 000000000324: BF8C3F71
	v_fma_f32 v3, -v4, v3, v16                                 // 000000000328: D54B0003 24420704
	v_fmac_f32_e32 v5, v0, v9                                  // 000000000330: 560A1300
	v_fmac_f32_e32 v6, v0, v10                                 // 000000000334: 560C1500
	v_fmac_f32_e32 v7, v0, v11                                 // 000000000338: 560E1700
	v_mul_f32_e64 v0, |v3|, v8 clamp                           // 00000000033C: D5088100 00021103
	v_sub_f32_e32 v3, s36, v5                                  // 000000000344: 08060A24
	v_sub_f32_e32 v4, s37, v6                                  // 000000000348: 08080C25
	v_sub_f32_e32 v8, s38, v7                                  // 00000000034C: 08100E26
	v_fmac_f32_e32 v5, s1, v1                                  // 000000000350: 560A0201
	v_fmac_f32_e32 v6, s2, v1                                  // 000000000354: 560C0202
	v_fmac_f32_e32 v7, s3, v1                                  // 000000000358: 560E0203
	v_sub_f32_e32 v0, 1.0, v0                                  // 00000000035C: 080000F2
	v_fmac_f32_e32 v5, 0x3e99999a, v3                          // 000000000360: 560A06FF 3E99999A
	v_fmac_f32_e32 v6, 0x3e99999a, v4                          // 000000000368: 560C08FF 3E99999A
	v_fmac_f32_e32 v7, 0x3e99999a, v8                          // 000000000370: 560E10FF 3E99999A
	v_sub_f32_e32 v1, s5, v5                                   // 000000000378: 08020A05
	v_sub_f32_e32 v3, s7, v7                                   // 00000000037C: 08060E07
	s_waitcnt vmcnt(0)                                         // 000000000380: BF8C3F70
	v_mul_f32_e32 v0, v0, v2                                   // 000000000384: 10000500
	v_sub_f32_e32 v2, s6, v6                                   // 000000000388: 08040C06
	v_fmac_f32_e32 v5, v1, v0                                  // 00000000038C: 560A0101
	v_fmac_f32_e32 v6, v2, v0                                  // 000000000390: 560C0102
	v_fmac_f32_e32 v7, v3, v0                                  // 000000000394: 560E0103
	v_fma_f32 v0, v0, 0.5, s39 clamp                           // 000000000398: D54B8000 009DE100
	exp mrt0 v5, v6, v7, v0 done vm                            // 0000000003A0: F800180F 00070605
	s_endpgm                                                   // 0000000003A8: BF810000

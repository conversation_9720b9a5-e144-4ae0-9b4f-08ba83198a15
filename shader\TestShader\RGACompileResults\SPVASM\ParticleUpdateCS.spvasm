; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 534
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID %gl_LocalInvocationID
               OpExecutionMode %main LocalSize 64 1 1
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_RWStructuredBuffer_Particle "type.RWStructuredBuffer.Particle"
               OpName %Particle "Particle"
               OpMemberName %Particle 0 "Position"
               OpMemberName %Particle 1 "Life"
               OpMemberName %Particle 2 "Velocity"
               OpMemberName %Particle 3 "Size"
               OpMemberName %Particle 4 "Color"
               OpMemberName %Particle 5 "Acceleration"
               OpMemberName %Particle 6 "Mass"
               OpMemberName %Particle 7 "Type"
               OpMemberName %Particle 8 "_padding"
               OpName %ParticleBuffer "ParticleBuffer"
               OpName %type_AppendStructuredBuffer_Particle "type.AppendStructuredBuffer.Particle"
               OpName %DeadParticleBuffer "DeadParticleBuffer"
               OpName %type_ACSBuffer_counter "type.ACSBuffer.counter"
               OpMemberName %type_ACSBuffer_counter 0 "counter"
               OpName %counter_var_DeadParticleBuffer "counter.var.DeadParticleBuffer"
               OpName %SharedPositions "SharedPositions"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %gl_LocalInvocationID BuiltIn LocalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %ParticleBuffer DescriptorSet 0
               OpDecorate %ParticleBuffer Binding 0
               OpDecorate %DeadParticleBuffer DescriptorSet 0
               OpDecorate %DeadParticleBuffer Binding 1
               OpDecorate %counter_var_DeadParticleBuffer DescriptorSet 0
               OpDecorate %counter_var_DeadParticleBuffer Binding 2
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
               OpMemberDecorate %Particle 0 Offset 0
               OpMemberDecorate %Particle 1 Offset 12
               OpMemberDecorate %Particle 2 Offset 16
               OpMemberDecorate %Particle 3 Offset 28
               OpMemberDecorate %Particle 4 Offset 32
               OpMemberDecorate %Particle 5 Offset 48
               OpMemberDecorate %Particle 6 Offset 60
               OpMemberDecorate %Particle 7 Offset 64
               OpMemberDecorate %Particle 8 Offset 68
               OpDecorate %_runtimearr_Particle ArrayStride 80
               OpMemberDecorate %type_RWStructuredBuffer_Particle 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_Particle BufferBlock
               OpMemberDecorate %type_AppendStructuredBuffer_Particle 0 Offset 0
               OpDecorate %type_AppendStructuredBuffer_Particle BufferBlock
               OpMemberDecorate %type_ACSBuffer_counter 0 Offset 0
               OpDecorate %type_ACSBuffer_counter BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_2 = OpConstant %int 2
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
      %int_4 = OpConstant %int 4
     %int_13 = OpConstant %int 13
      %int_3 = OpConstant %int 3
%float_0_100000001 = OpConstant %float 0.100000001
     %int_14 = OpConstant %int 14
      %int_5 = OpConstant %int 5
    %float_2 = OpConstant %float 2
     %int_11 = OpConstant %int 11
    %v3float = OpTypeVector %float 3
         %33 = OpConstantComposite %v3float %float_0 %float_0 %float_0
    %float_5 = OpConstant %float 5
    %uint_64 = OpConstant %uint 64
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
     %uint_1 = OpConstant %uint 1
%float_0_0500000007 = OpConstant %float 0.0500000007
         %40 = OpConstantComposite %v3float %float_0_100000001 %float_0 %float_0
         %41 = OpConstantComposite %v3float %float_0 %float_0_100000001 %float_0
         %42 = OpConstantComposite %v3float %float_0 %float_0 %float_0_100000001
    %float_3 = OpConstant %float 3
         %44 = OpConstantComposite %v3float %float_3 %float_3 %float_3
   %float_57 = OpConstant %float 57
  %float_113 = OpConstant %float 113
    %float_1 = OpConstant %float 1
   %float_58 = OpConstant %float 58
  %float_114 = OpConstant %float 114
  %float_170 = OpConstant %float 170
  %float_171 = OpConstant %float 171
%float_43758_5469 = OpConstant %float 43758.5469
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
    %v4float = OpTypeVector %float 4
   %Particle = OpTypeStruct %v3float %float %v3float %float %v4float %v3float %float %uint %v3float
%_runtimearr_Particle = OpTypeRuntimeArray %Particle
%type_RWStructuredBuffer_Particle = OpTypeStruct %_runtimearr_Particle
%_ptr_Uniform_type_RWStructuredBuffer_Particle = OpTypePointer Uniform %type_RWStructuredBuffer_Particle
%type_AppendStructuredBuffer_Particle = OpTypeStruct %_runtimearr_Particle
%_ptr_Uniform_type_AppendStructuredBuffer_Particle = OpTypePointer Uniform %type_AppendStructuredBuffer_Particle
%type_ACSBuffer_counter = OpTypeStruct %int
%_ptr_Uniform_type_ACSBuffer_counter = OpTypePointer Uniform %type_ACSBuffer_counter
%_arr_v3float_uint_64 = OpTypeArray %v3float %uint_64
%_ptr_Workgroup__arr_v3float_uint_64 = OpTypePointer Workgroup %_arr_v3float_uint_64
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %64 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%_ptr_Uniform_Particle = OpTypePointer Uniform %Particle
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Workgroup_v3float = OpTypePointer Workgroup %v3float
     %uint_2 = OpConstant %uint 2
   %uint_264 = OpConstant %uint 264
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%ParticleBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_Particle Uniform
%DeadParticleBuffer = OpVariable %_ptr_Uniform_type_AppendStructuredBuffer_Particle Uniform
%counter_var_DeadParticleBuffer = OpVariable %_ptr_Uniform_type_ACSBuffer_counter Uniform
%SharedPositions = OpVariable %_ptr_Workgroup__arr_v3float_uint_64 Workgroup
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%gl_LocalInvocationID = OpVariable %_ptr_Input_v3uint Input
         %73 = OpConstantComposite %v3float %float_5 %float_5 %float_5
  %float_0_5 = OpConstant %float 0.5
       %main = OpFunction %void None %64
         %75 = OpLabel
         %76 = OpLoad %v3uint %gl_GlobalInvocationID
         %77 = OpLoad %v3uint %gl_LocalInvocationID
               OpSelectionMerge %78 None
               OpSwitch %uint_0 %79
         %79 = OpLabel
         %80 = OpCompositeExtract %uint %76 0
         %81 = OpAccessChain %_ptr_Uniform_uint %ComputeParams %int_0
         %82 = OpLoad %uint %81
         %83 = OpUGreaterThanEqual %bool %80 %82
               OpSelectionMerge %84 None
               OpBranchConditional %83 %85 %84
         %85 = OpLabel
               OpBranch %78
         %84 = OpLabel
         %86 = OpAccessChain %_ptr_Uniform_Particle %ParticleBuffer %int_0 %80
         %87 = OpLoad %Particle %86
         %88 = OpCompositeExtract %v3float %87 0
         %89 = OpCompositeExtract %float %87 1
         %90 = OpCompositeExtract %v3float %87 2
         %91 = OpCompositeExtract %float %87 3
         %92 = OpCompositeExtract %v4float %87 4
         %93 = OpCompositeExtract %v3float %87 5
         %94 = OpCompositeExtract %float %87 6
         %95 = OpCompositeExtract %uint %87 7
         %96 = OpCompositeExtract %v3float %87 8
         %97 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_2
         %98 = OpLoad %float %97
         %99 = OpFSub %float %89 %98
        %100 = OpFOrdLessThanEqual %bool %99 %float_0
               OpSelectionMerge %101 None
               OpBranchConditional %100 %102 %101
        %102 = OpLabel
        %103 = OpAccessChain %_ptr_Uniform_int %counter_var_DeadParticleBuffer %uint_0
        %104 = OpAtomicIAdd %int %103 %uint_1 %uint_0 %int_1
        %105 = OpAccessChain %_ptr_Uniform_Particle %DeadParticleBuffer %uint_0 %104
        %106 = OpCompositeConstruct %Particle %88 %99 %90 %91 %92 %93 %94 %95 %96
               OpStore %105 %106
        %107 = OpCompositeConstruct %Particle %88 %99 %90 %91 %92 %93 %94 %uint_0 %96
               OpStore %86 %107
               OpBranch %78
        %101 = OpLabel
        %108 = OpAccessChain %_ptr_Uniform_v3float %ComputeParams %int_4
        %109 = OpLoad %v3float %108
        %110 = OpVectorTimesScalar %v3float %109 %94
        %111 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_13
        %112 = OpLoad %float %111
        %113 = OpVectorTimesScalar %v3float %88 %112
        %114 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_3
        %115 = OpLoad %float %114
        %116 = OpFMul %float %115 %float_0_100000001
        %117 = OpCompositeConstruct %v3float %116 %116 %116
        %118 = OpFAdd %v3float %113 %117
        %119 = OpFSub %v3float %118 %40
        %120 = OpExtInst %v3float %1 Floor %119
        %121 = OpExtInst %v3float %1 Fract %119
        %122 = OpFMul %v3float %121 %121
        %123 = OpVectorTimesScalar %v3float %121 %float_2
        %124 = OpFSub %v3float %44 %123
        %125 = OpFMul %v3float %122 %124
        %126 = OpCompositeExtract %float %120 0
        %127 = OpCompositeExtract %float %120 1
        %128 = OpFMul %float %127 %float_57
        %129 = OpFAdd %float %126 %128
        %130 = OpCompositeExtract %float %120 2
        %131 = OpFMul %float %float_113 %130
        %132 = OpFAdd %float %129 %131
        %133 = OpExtInst %float %1 Sin %132
        %134 = OpFMul %float %133 %float_43758_5469
        %135 = OpExtInst %float %1 Fract %134
        %136 = OpFAdd %float %132 %float_1
        %137 = OpExtInst %float %1 Sin %136
        %138 = OpFMul %float %137 %float_43758_5469
        %139 = OpExtInst %float %1 Fract %138
        %140 = OpCompositeExtract %float %125 0
        %141 = OpExtInst %float %1 FMix %135 %139 %140
        %142 = OpFAdd %float %132 %float_57
        %143 = OpExtInst %float %1 Sin %142
        %144 = OpFMul %float %143 %float_43758_5469
        %145 = OpExtInst %float %1 Fract %144
        %146 = OpFAdd %float %132 %float_58
        %147 = OpExtInst %float %1 Sin %146
        %148 = OpFMul %float %147 %float_43758_5469
        %149 = OpExtInst %float %1 Fract %148
        %150 = OpExtInst %float %1 FMix %145 %149 %140
        %151 = OpCompositeExtract %float %125 1
        %152 = OpExtInst %float %1 FMix %141 %150 %151
        %153 = OpFAdd %float %132 %float_113
        %154 = OpExtInst %float %1 Sin %153
        %155 = OpFMul %float %154 %float_43758_5469
        %156 = OpExtInst %float %1 Fract %155
        %157 = OpFAdd %float %132 %float_114
        %158 = OpExtInst %float %1 Sin %157
        %159 = OpFMul %float %158 %float_43758_5469
        %160 = OpExtInst %float %1 Fract %159
        %161 = OpExtInst %float %1 FMix %156 %160 %140
        %162 = OpFAdd %float %132 %float_170
        %163 = OpExtInst %float %1 Sin %162
        %164 = OpFMul %float %163 %float_43758_5469
        %165 = OpExtInst %float %1 Fract %164
        %166 = OpFAdd %float %132 %float_171
        %167 = OpExtInst %float %1 Sin %166
        %168 = OpFMul %float %167 %float_43758_5469
        %169 = OpExtInst %float %1 Fract %168
        %170 = OpExtInst %float %1 FMix %165 %169 %140
        %171 = OpExtInst %float %1 FMix %161 %170 %151
        %172 = OpCompositeExtract %float %125 2
        %173 = OpExtInst %float %1 FMix %152 %171 %172
        %174 = OpFAdd %v3float %118 %40
        %175 = OpExtInst %v3float %1 Floor %174
        %176 = OpExtInst %v3float %1 Fract %174
        %177 = OpFMul %v3float %176 %176
        %178 = OpVectorTimesScalar %v3float %176 %float_2
        %179 = OpFSub %v3float %44 %178
        %180 = OpFMul %v3float %177 %179
        %181 = OpCompositeExtract %float %175 0
        %182 = OpCompositeExtract %float %175 1
        %183 = OpFMul %float %182 %float_57
        %184 = OpFAdd %float %181 %183
        %185 = OpCompositeExtract %float %175 2
        %186 = OpFMul %float %float_113 %185
        %187 = OpFAdd %float %184 %186
        %188 = OpExtInst %float %1 Sin %187
        %189 = OpFMul %float %188 %float_43758_5469
        %190 = OpExtInst %float %1 Fract %189
        %191 = OpFAdd %float %187 %float_1
        %192 = OpExtInst %float %1 Sin %191
        %193 = OpFMul %float %192 %float_43758_5469
        %194 = OpExtInst %float %1 Fract %193
        %195 = OpCompositeExtract %float %180 0
        %196 = OpExtInst %float %1 FMix %190 %194 %195
        %197 = OpFAdd %float %187 %float_57
        %198 = OpExtInst %float %1 Sin %197
        %199 = OpFMul %float %198 %float_43758_5469
        %200 = OpExtInst %float %1 Fract %199
        %201 = OpFAdd %float %187 %float_58
        %202 = OpExtInst %float %1 Sin %201
        %203 = OpFMul %float %202 %float_43758_5469
        %204 = OpExtInst %float %1 Fract %203
        %205 = OpExtInst %float %1 FMix %200 %204 %195
        %206 = OpCompositeExtract %float %180 1
        %207 = OpExtInst %float %1 FMix %196 %205 %206
        %208 = OpFAdd %float %187 %float_113
        %209 = OpExtInst %float %1 Sin %208
        %210 = OpFMul %float %209 %float_43758_5469
        %211 = OpExtInst %float %1 Fract %210
        %212 = OpFAdd %float %187 %float_114
        %213 = OpExtInst %float %1 Sin %212
        %214 = OpFMul %float %213 %float_43758_5469
        %215 = OpExtInst %float %1 Fract %214
        %216 = OpExtInst %float %1 FMix %211 %215 %195
        %217 = OpFAdd %float %187 %float_170
        %218 = OpExtInst %float %1 Sin %217
        %219 = OpFMul %float %218 %float_43758_5469
        %220 = OpExtInst %float %1 Fract %219
        %221 = OpFAdd %float %187 %float_171
        %222 = OpExtInst %float %1 Sin %221
        %223 = OpFMul %float %222 %float_43758_5469
        %224 = OpExtInst %float %1 Fract %223
        %225 = OpExtInst %float %1 FMix %220 %224 %195
        %226 = OpExtInst %float %1 FMix %216 %225 %206
        %227 = OpCompositeExtract %float %180 2
        %228 = OpExtInst %float %1 FMix %207 %226 %227
        %229 = OpFSub %v3float %118 %41
        %230 = OpExtInst %v3float %1 Floor %229
        %231 = OpExtInst %v3float %1 Fract %229
        %232 = OpFMul %v3float %231 %231
        %233 = OpVectorTimesScalar %v3float %231 %float_2
        %234 = OpFSub %v3float %44 %233
        %235 = OpFMul %v3float %232 %234
        %236 = OpCompositeExtract %float %230 0
        %237 = OpCompositeExtract %float %230 1
        %238 = OpFMul %float %237 %float_57
        %239 = OpFAdd %float %236 %238
        %240 = OpCompositeExtract %float %230 2
        %241 = OpFMul %float %float_113 %240
        %242 = OpFAdd %float %239 %241
        %243 = OpExtInst %float %1 Sin %242
        %244 = OpFMul %float %243 %float_43758_5469
        %245 = OpExtInst %float %1 Fract %244
        %246 = OpFAdd %float %242 %float_1
        %247 = OpExtInst %float %1 Sin %246
        %248 = OpFMul %float %247 %float_43758_5469
        %249 = OpExtInst %float %1 Fract %248
        %250 = OpCompositeExtract %float %235 0
        %251 = OpExtInst %float %1 FMix %245 %249 %250
        %252 = OpFAdd %float %242 %float_57
        %253 = OpExtInst %float %1 Sin %252
        %254 = OpFMul %float %253 %float_43758_5469
        %255 = OpExtInst %float %1 Fract %254
        %256 = OpFAdd %float %242 %float_58
        %257 = OpExtInst %float %1 Sin %256
        %258 = OpFMul %float %257 %float_43758_5469
        %259 = OpExtInst %float %1 Fract %258
        %260 = OpExtInst %float %1 FMix %255 %259 %250
        %261 = OpCompositeExtract %float %235 1
        %262 = OpExtInst %float %1 FMix %251 %260 %261
        %263 = OpFAdd %float %242 %float_113
        %264 = OpExtInst %float %1 Sin %263
        %265 = OpFMul %float %264 %float_43758_5469
        %266 = OpExtInst %float %1 Fract %265
        %267 = OpFAdd %float %242 %float_114
        %268 = OpExtInst %float %1 Sin %267
        %269 = OpFMul %float %268 %float_43758_5469
        %270 = OpExtInst %float %1 Fract %269
        %271 = OpExtInst %float %1 FMix %266 %270 %250
        %272 = OpFAdd %float %242 %float_170
        %273 = OpExtInst %float %1 Sin %272
        %274 = OpFMul %float %273 %float_43758_5469
        %275 = OpExtInst %float %1 Fract %274
        %276 = OpFAdd %float %242 %float_171
        %277 = OpExtInst %float %1 Sin %276
        %278 = OpFMul %float %277 %float_43758_5469
        %279 = OpExtInst %float %1 Fract %278
        %280 = OpExtInst %float %1 FMix %275 %279 %250
        %281 = OpExtInst %float %1 FMix %271 %280 %261
        %282 = OpCompositeExtract %float %235 2
        %283 = OpExtInst %float %1 FMix %262 %281 %282
        %284 = OpFAdd %v3float %118 %41
        %285 = OpExtInst %v3float %1 Floor %284
        %286 = OpExtInst %v3float %1 Fract %284
        %287 = OpFMul %v3float %286 %286
        %288 = OpVectorTimesScalar %v3float %286 %float_2
        %289 = OpFSub %v3float %44 %288
        %290 = OpFMul %v3float %287 %289
        %291 = OpCompositeExtract %float %285 0
        %292 = OpCompositeExtract %float %285 1
        %293 = OpFMul %float %292 %float_57
        %294 = OpFAdd %float %291 %293
        %295 = OpCompositeExtract %float %285 2
        %296 = OpFMul %float %float_113 %295
        %297 = OpFAdd %float %294 %296
        %298 = OpExtInst %float %1 Sin %297
        %299 = OpFMul %float %298 %float_43758_5469
        %300 = OpExtInst %float %1 Fract %299
        %301 = OpFAdd %float %297 %float_1
        %302 = OpExtInst %float %1 Sin %301
        %303 = OpFMul %float %302 %float_43758_5469
        %304 = OpExtInst %float %1 Fract %303
        %305 = OpCompositeExtract %float %290 0
        %306 = OpExtInst %float %1 FMix %300 %304 %305
        %307 = OpFAdd %float %297 %float_57
        %308 = OpExtInst %float %1 Sin %307
        %309 = OpFMul %float %308 %float_43758_5469
        %310 = OpExtInst %float %1 Fract %309
        %311 = OpFAdd %float %297 %float_58
        %312 = OpExtInst %float %1 Sin %311
        %313 = OpFMul %float %312 %float_43758_5469
        %314 = OpExtInst %float %1 Fract %313
        %315 = OpExtInst %float %1 FMix %310 %314 %305
        %316 = OpCompositeExtract %float %290 1
        %317 = OpExtInst %float %1 FMix %306 %315 %316
        %318 = OpFAdd %float %297 %float_113
        %319 = OpExtInst %float %1 Sin %318
        %320 = OpFMul %float %319 %float_43758_5469
        %321 = OpExtInst %float %1 Fract %320
        %322 = OpFAdd %float %297 %float_114
        %323 = OpExtInst %float %1 Sin %322
        %324 = OpFMul %float %323 %float_43758_5469
        %325 = OpExtInst %float %1 Fract %324
        %326 = OpExtInst %float %1 FMix %321 %325 %305
        %327 = OpFAdd %float %297 %float_170
        %328 = OpExtInst %float %1 Sin %327
        %329 = OpFMul %float %328 %float_43758_5469
        %330 = OpExtInst %float %1 Fract %329
        %331 = OpFAdd %float %297 %float_171
        %332 = OpExtInst %float %1 Sin %331
        %333 = OpFMul %float %332 %float_43758_5469
        %334 = OpExtInst %float %1 Fract %333
        %335 = OpExtInst %float %1 FMix %330 %334 %305
        %336 = OpExtInst %float %1 FMix %326 %335 %316
        %337 = OpCompositeExtract %float %290 2
        %338 = OpExtInst %float %1 FMix %317 %336 %337
        %339 = OpFSub %v3float %118 %42
        %340 = OpExtInst %v3float %1 Floor %339
        %341 = OpExtInst %v3float %1 Fract %339
        %342 = OpFMul %v3float %341 %341
        %343 = OpVectorTimesScalar %v3float %341 %float_2
        %344 = OpFSub %v3float %44 %343
        %345 = OpFMul %v3float %342 %344
        %346 = OpCompositeExtract %float %340 0
        %347 = OpCompositeExtract %float %340 1
        %348 = OpFMul %float %347 %float_57
        %349 = OpFAdd %float %346 %348
        %350 = OpCompositeExtract %float %340 2
        %351 = OpFMul %float %float_113 %350
        %352 = OpFAdd %float %349 %351
        %353 = OpExtInst %float %1 Sin %352
        %354 = OpFMul %float %353 %float_43758_5469
        %355 = OpExtInst %float %1 Fract %354
        %356 = OpFAdd %float %352 %float_1
        %357 = OpExtInst %float %1 Sin %356
        %358 = OpFMul %float %357 %float_43758_5469
        %359 = OpExtInst %float %1 Fract %358
        %360 = OpCompositeExtract %float %345 0
        %361 = OpExtInst %float %1 FMix %355 %359 %360
        %362 = OpFAdd %float %352 %float_57
        %363 = OpExtInst %float %1 Sin %362
        %364 = OpFMul %float %363 %float_43758_5469
        %365 = OpExtInst %float %1 Fract %364
        %366 = OpFAdd %float %352 %float_58
        %367 = OpExtInst %float %1 Sin %366
        %368 = OpFMul %float %367 %float_43758_5469
        %369 = OpExtInst %float %1 Fract %368
        %370 = OpExtInst %float %1 FMix %365 %369 %360
        %371 = OpCompositeExtract %float %345 1
        %372 = OpExtInst %float %1 FMix %361 %370 %371
        %373 = OpFAdd %float %352 %float_113
        %374 = OpExtInst %float %1 Sin %373
        %375 = OpFMul %float %374 %float_43758_5469
        %376 = OpExtInst %float %1 Fract %375
        %377 = OpFAdd %float %352 %float_114
        %378 = OpExtInst %float %1 Sin %377
        %379 = OpFMul %float %378 %float_43758_5469
        %380 = OpExtInst %float %1 Fract %379
        %381 = OpExtInst %float %1 FMix %376 %380 %360
        %382 = OpFAdd %float %352 %float_170
        %383 = OpExtInst %float %1 Sin %382
        %384 = OpFMul %float %383 %float_43758_5469
        %385 = OpExtInst %float %1 Fract %384
        %386 = OpFAdd %float %352 %float_171
        %387 = OpExtInst %float %1 Sin %386
        %388 = OpFMul %float %387 %float_43758_5469
        %389 = OpExtInst %float %1 Fract %388
        %390 = OpExtInst %float %1 FMix %385 %389 %360
        %391 = OpExtInst %float %1 FMix %381 %390 %371
        %392 = OpCompositeExtract %float %345 2
        %393 = OpExtInst %float %1 FMix %372 %391 %392
        %394 = OpFAdd %v3float %118 %42
        %395 = OpExtInst %v3float %1 Floor %394
        %396 = OpExtInst %v3float %1 Fract %394
        %397 = OpFMul %v3float %396 %396
        %398 = OpVectorTimesScalar %v3float %396 %float_2
        %399 = OpFSub %v3float %44 %398
        %400 = OpFMul %v3float %397 %399
        %401 = OpCompositeExtract %float %395 0
        %402 = OpCompositeExtract %float %395 1
        %403 = OpFMul %float %402 %float_57
        %404 = OpFAdd %float %401 %403
        %405 = OpCompositeExtract %float %395 2
        %406 = OpFMul %float %float_113 %405
        %407 = OpFAdd %float %404 %406
        %408 = OpExtInst %float %1 Sin %407
        %409 = OpFMul %float %408 %float_43758_5469
        %410 = OpExtInst %float %1 Fract %409
        %411 = OpFAdd %float %407 %float_1
        %412 = OpExtInst %float %1 Sin %411
        %413 = OpFMul %float %412 %float_43758_5469
        %414 = OpExtInst %float %1 Fract %413
        %415 = OpCompositeExtract %float %400 0
        %416 = OpExtInst %float %1 FMix %410 %414 %415
        %417 = OpFAdd %float %407 %float_57
        %418 = OpExtInst %float %1 Sin %417
        %419 = OpFMul %float %418 %float_43758_5469
        %420 = OpExtInst %float %1 Fract %419
        %421 = OpFAdd %float %407 %float_58
        %422 = OpExtInst %float %1 Sin %421
        %423 = OpFMul %float %422 %float_43758_5469
        %424 = OpExtInst %float %1 Fract %423
        %425 = OpExtInst %float %1 FMix %420 %424 %415
        %426 = OpCompositeExtract %float %400 1
        %427 = OpExtInst %float %1 FMix %416 %425 %426
        %428 = OpFAdd %float %407 %float_113
        %429 = OpExtInst %float %1 Sin %428
        %430 = OpFMul %float %429 %float_43758_5469
        %431 = OpExtInst %float %1 Fract %430
        %432 = OpFAdd %float %407 %float_114
        %433 = OpExtInst %float %1 Sin %432
        %434 = OpFMul %float %433 %float_43758_5469
        %435 = OpExtInst %float %1 Fract %434
        %436 = OpExtInst %float %1 FMix %431 %435 %415
        %437 = OpFAdd %float %407 %float_170
        %438 = OpExtInst %float %1 Sin %437
        %439 = OpFMul %float %438 %float_43758_5469
        %440 = OpExtInst %float %1 Fract %439
        %441 = OpFAdd %float %407 %float_171
        %442 = OpExtInst %float %1 Sin %441
        %443 = OpFMul %float %442 %float_43758_5469
        %444 = OpExtInst %float %1 Fract %443
        %445 = OpExtInst %float %1 FMix %440 %444 %415
        %446 = OpExtInst %float %1 FMix %436 %445 %426
        %447 = OpCompositeExtract %float %400 2
        %448 = OpExtInst %float %1 FMix %427 %446 %447
        %449 = OpFSub %float %338 %283
        %450 = OpFSub %float %449 %448
        %451 = OpFAdd %float %450 %393
        %452 = OpFSub %float %448 %393
        %453 = OpFSub %float %452 %228
        %454 = OpFAdd %float %453 %173
        %455 = OpFSub %float %228 %173
        %456 = OpFSub %float %455 %338
        %457 = OpFAdd %float %456 %283
        %458 = OpCompositeConstruct %v3float %451 %454 %457
        %459 = OpFMul %v3float %458 %73
        %460 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_14
        %461 = OpLoad %float %460
        %462 = OpVectorTimesScalar %v3float %459 %461
        %463 = OpFAdd %v3float %110 %462
        %464 = OpCompositeConstruct %v3float %94 %94 %94
        %465 = OpFDiv %v3float %463 %464
        %466 = OpVectorTimesScalar %v3float %465 %98
        %467 = OpFAdd %v3float %90 %466
        %468 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_5
        %469 = OpLoad %float %468
        %470 = OpVectorTimesScalar %v3float %467 %469
        %471 = OpVectorTimesScalar %v3float %470 %98
        %472 = OpFAdd %v3float %88 %471
        %473 = OpFMul %float %99 %float_0_5
        %474 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_11 %int_0
        %475 = OpLoad %float %474
        %476 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_11 %int_1
        %477 = OpLoad %float %476
        %478 = OpExtInst %float %1 FMix %475 %477 %473
        %479 = OpCompositeInsert %v4float %473 %92 3
        %480 = OpCompositeExtract %uint %77 0
        %481 = OpAccessChain %_ptr_Workgroup_v3float %SharedPositions %480
               OpStore %481 %472
               OpControlBarrier %uint_2 %uint_2 %uint_264
               OpBranch %482
        %482 = OpLabel
        %483 = OpPhi %v3float %33 %101 %484 %485
        %486 = OpPhi %v3float %33 %101 %487 %485
        %488 = OpPhi %uint %uint_0 %101 %489 %485
        %490 = OpPhi %uint %uint_0 %101 %491 %485
        %492 = OpULessThan %bool %490 %uint_64
               OpLoopMerge %493 %485 None
               OpBranchConditional %492 %494 %493
        %494 = OpLabel
        %495 = OpIEqual %bool %490 %480
               OpSelectionMerge %496 None
               OpBranchConditional %495 %497 %496
        %497 = OpLabel
               OpBranch %485
        %496 = OpLabel
        %498 = OpAccessChain %_ptr_Workgroup_v3float %SharedPositions %490
        %499 = OpLoad %v3float %498
        %500 = OpFSub %v3float %472 %499
        %501 = OpExtInst %float %1 Length %500
        %502 = OpFOrdLessThan %bool %501 %float_5
               OpSelectionMerge %503 None
               OpBranchConditional %502 %504 %503
        %504 = OpLabel
        %505 = OpFOrdGreaterThan %bool %501 %float_0
               OpBranch %503
        %503 = OpLabel
        %506 = OpPhi %bool %false %496 %505 %504
               OpSelectionMerge %507 None
               OpBranchConditional %506 %508 %507
        %508 = OpLabel
        %509 = OpExtInst %v3float %1 Normalize %500
        %510 = OpCompositeConstruct %v3float %501 %501 %501
        %511 = OpFDiv %v3float %509 %510
        %512 = OpFAdd %v3float %483 %511
        %513 = OpFAdd %v3float %486 %499
        %514 = OpIAdd %uint %488 %uint_1
               OpBranch %507
        %507 = OpLabel
        %515 = OpPhi %v3float %483 %503 %512 %508
        %516 = OpPhi %v3float %486 %503 %513 %508
        %517 = OpPhi %uint %488 %503 %514 %508
               OpBranch %485
        %485 = OpLabel
        %484 = OpPhi %v3float %483 %497 %515 %507
        %487 = OpPhi %v3float %486 %497 %516 %507
        %489 = OpPhi %uint %488 %497 %517 %507
        %491 = OpIAdd %uint %490 %uint_1
               OpBranch %482
        %493 = OpLabel
        %518 = OpUGreaterThan %bool %488 %uint_0
               OpSelectionMerge %519 None
               OpBranchConditional %518 %520 %519
        %520 = OpLabel
        %521 = OpConvertUToF %float %488
        %522 = OpCompositeConstruct %v3float %521 %521 %521
        %523 = OpFDiv %v3float %486 %522
        %524 = OpFSub %v3float %523 %472
        %525 = OpExtInst %v3float %1 Normalize %524
        %526 = OpVectorTimesScalar %v3float %483 %float_0_100000001
        %527 = OpVectorTimesScalar %v3float %526 %98
        %528 = OpFAdd %v3float %470 %527
        %529 = OpVectorTimesScalar %v3float %525 %float_0_0500000007
        %530 = OpVectorTimesScalar %v3float %529 %98
        %531 = OpFAdd %v3float %528 %530
               OpBranch %519
        %519 = OpLabel
        %532 = OpPhi %v3float %470 %493 %531 %520
        %533 = OpCompositeConstruct %Particle %472 %99 %532 %478 %479 %465 %94 %95 %96
               OpStore %86 %533
               OpBranch %78
         %78 = OpLabel
               OpReturn
               OpFunctionEnd

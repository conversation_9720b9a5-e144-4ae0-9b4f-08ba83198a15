_amdgpu_ps_main:
	s_mov_b64 s[28:29], exec                                   // 000000000000: BE9C047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_getpc_b64 s[4:5]                                         // 000000000008: BE841F00
	s_mov_b32 s0, s1                                           // 00000000000C: BE800301
	s_mov_b32 s1, s5                                           // 000000000010: BE810305
	s_mov_b32 m0, s2                                           // 000000000014: BEFC0302
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx16 s[4:19], s[0:1], null                      // 00000000001C: F4100100 FA000000
	s_load_dwordx8 s[20:27], s[0:1], 0x30                      // 000000000024: F40C0500 FA000030
	v_interp_p1_f32_e32 v2, v0, attr4.x                        // 00000000002C: C8081000
	v_interp_p1_f32_e32 v3, v0, attr4.y                        // 000000000030: C80C1100
	v_interp_p1_f32_e32 v11, v0, attr3.y                       // 000000000034: C82C0D00
	v_interp_p1_f32_e32 v10, v0, attr3.x                       // 000000000038: C8280C00
	v_interp_p1_f32_e32 v14, v0, attr2.y                       // 00000000003C: C8380900
	v_interp_p2_f32_e32 v2, v1, attr4.x                        // 000000000040: C8091001
	v_interp_p2_f32_e32 v3, v1, attr4.y                        // 000000000044: C80D1101
	v_interp_p2_f32_e32 v11, v1, attr3.y                       // 000000000048: C82D0D01
	v_interp_p1_f32_e32 v12, v0, attr3.z                       // 00000000004C: C8300E00
	v_interp_p1_f32_e32 v13, v0, attr2.x                       // 000000000050: C8340800
	v_interp_p1_f32_e32 v17, v0, attr1.y                       // 000000000054: C8440500
	v_interp_p2_f32_e32 v10, v1, attr3.x                       // 000000000058: C8290C01
	v_interp_p2_f32_e32 v14, v1, attr2.y                       // 00000000005C: C8390901
	v_interp_p1_f32_e32 v15, v0, attr2.z                       // 000000000060: C83C0A00
	v_interp_p1_f32_e32 v16, v0, attr1.x                       // 000000000064: C8400400
	v_interp_p2_f32_e32 v12, v1, attr3.z                       // 000000000068: C8310E01
	v_interp_p2_f32_e32 v13, v1, attr2.x                       // 00000000006C: C8350801
	v_interp_p2_f32_e32 v17, v1, attr1.y                       // 000000000070: C8450501
	v_interp_p1_f32_e32 v18, v0, attr1.z                       // 000000000074: C8480600
	s_waitcnt lgkmcnt(0)                                       // 000000000078: BF8CC07F
	image_sample v[4:6], v[2:3], s[20:27], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000007C: F0800708 00650402
	image_sample v[7:9], v[2:3], s[4:11], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000084: F0800708 00610702
	s_load_dwordx4 s[4:7], s[0:1], null                        // 00000000008C: F4080100 FA000000
	v_interp_p2_f32_e32 v15, v1, attr2.z                       // 000000000094: C83D0A01
	v_interp_p2_f32_e32 v16, v1, attr1.x                       // 000000000098: C8410401
	v_interp_p2_f32_e32 v18, v1, attr1.z                       // 00000000009C: C8490601
	s_waitcnt lgkmcnt(0)                                       // 0000000000A0: BF8CC07F
	s_clause 0x1                                               // 0000000000A4: BFA10001
	s_buffer_load_dwordx2 s[8:9], s[4:7], null                 // 0000000000A8: F4240202 FA000000
	s_buffer_load_dword s3, s[4:7], 0x8                        // 0000000000B0: F42000C2 FA000008
	s_load_dwordx16 s[36:51], s[0:1], 0xa0                     // 0000000000B8: F4100900 FA0000A0
	s_waitcnt vmcnt(1)                                         // 0000000000C0: BF8C3F71
	v_fma_f32 v5, v5, 2.0, -1.0                                // 0000000000C4: D54B0005 03CDE905
	v_fma_f32 v4, v4, 2.0, -1.0                                // 0000000000CC: D54B0004 03CDE904
	v_fma_f32 v6, v6, 2.0, -1.0                                // 0000000000D4: D54B0006 03CDE906
	v_mul_f32_e32 v11, v5, v11                                 // 0000000000DC: 10161705
	v_mul_f32_e32 v10, v5, v10                                 // 0000000000E0: 10141505
	v_mul_f32_e32 v12, v5, v12                                 // 0000000000E4: 10181905
	s_waitcnt vmcnt(0) lgkmcnt(0)                              // 0000000000E8: BF8C0070
	v_mul_f32_e32 v5, s8, v7                                   // 0000000000EC: 100A0E08
	v_mul_f32_e32 v7, s3, v9                                   // 0000000000F0: 100E1203
	v_fmac_f32_e32 v11, v4, v14                                // 0000000000F4: 56161D04
	v_fmac_f32_e32 v10, v4, v13                                // 0000000000F8: 56141B04
	v_fmac_f32_e32 v12, v4, v15                                // 0000000000FC: 56181F04
	v_log_f32_e32 v5, v5                                       // 000000000100: 7E0A4F05
	v_log_f32_e32 v7, v7                                       // 000000000104: 7E0E4F07
	v_fmac_f32_e32 v11, v6, v17                                // 000000000108: 56162306
	v_fmac_f32_e32 v10, v6, v16                                // 00000000010C: 56142106
	v_fmac_f32_e32 v12, v6, v18                                // 000000000110: 56182506
	v_mul_f32_e32 v6, s9, v8                                   // 000000000114: 100C1009
	s_buffer_load_dword s3, s[16:19], 0xc0                     // 000000000118: F42000C8 FA0000C0
	v_mul_f32_e32 v4, v11, v11                                 // 000000000120: 1008170B
	v_mov_b32_e32 v15, 0                                       // 000000000124: 7E1E0280
	v_mov_b32_e32 v13, 0                                       // 000000000128: 7E1A0280
	v_log_f32_e32 v6, v6                                       // 00000000012C: 7E0C4F06
	v_mul_legacy_f32_e32 v5, 0x400ccccd, v5                    // 000000000130: 0E0A0AFF 400CCCCD
	v_fmac_f32_e32 v4, v10, v10                                // 000000000138: 5608150A
	v_mul_legacy_f32_e32 v7, 0x400ccccd, v7                    // 00000000013C: 0E0E0EFF 400CCCCD
	v_fmac_f32_e32 v4, v12, v12                                // 000000000144: 5608190C
	v_mul_legacy_f32_e32 v6, 0x400ccccd, v6                    // 000000000148: 0E0C0CFF 400CCCCD
	v_rsq_f32_e32 v8, v4                                       // 000000000150: 7E105D04
	v_cmp_neq_f32_e32 vcc_lo, 0, v4                            // 000000000154: 7C1A0880
	v_exp_f32_e32 v4, v5                                       // 000000000158: 7E084B05
	v_exp_f32_e32 v5, v6                                       // 00000000015C: 7E0A4B06
	v_exp_f32_e32 v6, v7                                       // 000000000160: 7E0C4B07
	s_waitcnt lgkmcnt(0)                                       // 000000000164: BF8CC07F
	s_cmp_lt_i32 s3, 1                                         // 000000000168: BF048103
	v_cndmask_b32_e32 v9, 0, v8, vcc_lo                        // 00000000016C: 02121080
	v_mul_f32_e32 v7, v9, v10                                  // 000000000170: 100E1509
	v_mul_f32_e32 v8, v9, v11                                  // 000000000174: 10101709
	v_mul_f32_e32 v9, v9, v12                                  // 000000000178: 10121909
	v_mov_b32_e32 v12, 0                                       // 00000000017C: 7E180280
	s_cbranch_scc1 _L0                                         // 000000000180: BF8500D1
	s_load_dwordx16 s[52:67], s[0:1], 0x60                     // 000000000184: F4100D00 FA000060
	s_mov_b32 m0, s2                                           // 00000000018C: BEFC0302
	s_buffer_load_dwordx2 s[8:9], s[4:7], 0xc                  // 000000000190: F4240202 FA00000C
	v_interp_p1_f32_e32 v10, v0, attr5.y                       // 000000000198: C8281500
	v_interp_p1_f32_e32 v11, v0, attr5.x                       // 00000000019C: C82C1400
	v_interp_p1_f32_e32 v12, v0, attr5.z                       // 0000000001A0: C8301600
	v_mov_b32_e32 v24, 1.0                                     // 0000000001A4: 7E3002F2
	v_add_f32_e32 v21, 0xbd23d70a, v4                          // 0000000001A8: 062A08FF BD23D70A
	v_interp_p2_f32_e32 v10, v1, attr5.y                       // 0000000001B0: C8291501
	v_interp_p2_f32_e32 v11, v1, attr5.x                       // 0000000001B4: C82D1401
	v_interp_p2_f32_e32 v12, v1, attr5.z                       // 0000000001B8: C8311601
	v_add_f32_e32 v22, 0xbd23d70a, v5                          // 0000000001BC: 062C0AFF BD23D70A
	v_add_f32_e32 v25, 0xbd23d70a, v6                          // 0000000001C4: 06320CFF BD23D70A
	v_mul_f32_e32 v13, v10, v10                                // 0000000001CC: 101A150A
	s_mov_b32 s2, 0xbea2f983                                   // 0000000001D0: BE8203FF BEA2F983
	v_interp_p1_f32_e32 v16, v0, attr0.x                       // 0000000001D8: C8400000
	v_interp_p1_f32_e32 v17, v0, attr0.y                       // 0000000001DC: C8440100
	v_interp_p1_f32_e32 v0, v0, attr0.z                        // 0000000001E0: C8000200
	v_fmac_f32_e32 v13, v11, v11                               // 0000000001E4: 561A170B
	s_waitcnt lgkmcnt(0)                                       // 0000000001E8: BF8CC07F
	image_sample v15, v[2:3], s[52:59], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000001EC: F0800108 006D0F02
	image_sample v18, v[2:3], s[60:67], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000001F4: F0800108 006F1202
	v_interp_p2_f32_e32 v16, v1, attr0.x                       // 0000000001FC: C8410001
	v_interp_p2_f32_e32 v17, v1, attr0.y                       // 000000000200: C8450101
	v_fmac_f32_e32 v13, v12, v12                               // 000000000204: 561A190C
	v_interp_p2_f32_e32 v0, v1, attr0.z                        // 000000000208: C8010201
	v_rsq_f32_e32 v14, v13                                     // 00000000020C: 7E1C5D0D
	v_cmp_neq_f32_e32 vcc_lo, 0, v13                           // 000000000210: 7C1A1A80
	v_cndmask_b32_e32 v13, 0, v14, vcc_lo                      // 000000000214: 021A1C80
	v_mul_f32_e32 v10, v13, v10                                // 000000000218: 1014150D
	v_mul_f32_e32 v11, v13, v11                                // 00000000021C: 1016170D
	v_mul_f32_e32 v14, v13, v12                                // 000000000220: 101C190D
	v_mov_b32_e32 v12, 0                                       // 000000000224: 7E180280
	v_mov_b32_e32 v13, 0                                       // 000000000228: 7E1A0280
	v_mul_f32_e32 v19, v8, v10                                 // 00000000022C: 10261508
	v_fmac_f32_e32 v19, v7, v11                                // 000000000230: 56261707
	v_fmac_f32_e32 v19, v9, v14                                // 000000000234: 56261D09
	v_max_f32_e32 v30, 0, v19                                  // 000000000238: 203C2680
	v_mul_f32_e32 v1, 4.0, v30                                 // 00000000023C: 10023CF6
	s_waitcnt vmcnt(1)                                         // 000000000240: BF8C3F71
	v_mul_f32_e32 v15, s8, v15                                 // 000000000244: 101E1E08
	s_waitcnt vmcnt(0)                                         // 000000000248: BF8C3F70
	v_fma_f32 v20, v18, s9, 1.0                                // 00000000024C: D54B0014 03C81312
	v_mul_f32_e32 v23, s9, v18                                 // 000000000254: 102E2409
	s_mov_b32 s8, 0                                            // 000000000258: BE880380
	v_fmaak_f32 v18, v15, v21, 0x3d23d70a                      // 00000000025C: 5A242B0F 3D23D70A
	v_mul_f32_e32 v26, v20, v20                                // 000000000264: 10342914
	v_mul_f32_e32 v27, v23, v23                                // 000000000268: 10362F17
	v_fmaak_f32 v19, v15, v22, 0x3d23d70a                      // 00000000026C: 5A262D0F 3D23D70A
	v_fmaak_f32 v20, v15, v25, 0x3d23d70a                      // 000000000274: 5A28330F 3D23D70A
	v_fma_f32 v21, -v15, v21, 0x3f75c28f                       // 00000000027C: D54B0015 23FE2B0F 3F75C28F
	v_mul_f32_e32 v23, 0x3e000000, v26                         // 000000000288: 102E34FF 3E000000
	v_fmamk_f32 v24, v26, 0xbe000000, v24                      // 000000000290: 5830311A BE000000
	v_fma_f32 v22, -v15, v22, 0x3f75c28f                       // 000000000298: D54B0016 23FE2D0F 3F75C28F
	v_fmaak_f32 v29, s2, v15, 0x3ea2f983                       // 0000000002A4: 5A3A1E02 3EA2F983
	v_fma_f32 v25, -v15, v25, 0x3f75c28f                       // 0000000002AC: D54B0019 23FE330F 3F75C28F
	v_mul_f32_e32 v15, v27, v27                                // 0000000002B8: 101E371B
	v_fma_f32 v31, v30, v24, v23                               // 0000000002BC: D54B001F 045E311E
	v_fma_f32 v26, v27, v27, -1.0                              // 0000000002C4: D54B001A 03CE371B
	v_mul_f32_e32 v27, v29, v4                                 // 0000000002CC: 1036091D
	v_mul_f32_e32 v28, v29, v5                                 // 0000000002D0: 10380B1D
	v_mul_f32_e32 v29, v29, v6                                 // 0000000002D4: 103A0D1D
	v_mul_f32_e32 v30, v30, v15                                // 0000000002D8: 103C1F1E
	v_mul_f32_e32 v31, 0x40490fdb, v31                         // 0000000002DC: 103E3EFF 40490FDB
	v_mov_b32_e32 v15, 0                                       // 0000000002E4: 7E1E0280
	s_movk_i32 s2, 0x80                                        // 0000000002E8: B0020080
_L1:
	s_add_i32 s10, s2, 0xffffff84                              // 0000000002EC: 810AFF02 FFFFFF84
	s_add_i32 s9, s2, 0xffffff80                               // 0000000002F4: 8109FF02 FFFFFF80
	s_add_i32 s11, s2, 0xffffff88                              // 0000000002FC: 810BFF02 FFFFFF88
	s_sub_i32 s22, s2, 56                                      // 000000000304: 8196B802
	s_sub_i32 s20, s2, 64                                      // 000000000308: 8194C002
	s_sub_i32 s21, s2, 60                                      // 00000000030C: 8195BC02
	s_clause 0x6                                               // 000000000310: BFA10006
	s_buffer_load_dword s10, s[16:19], s10                     // 000000000314: F4200288 14000000
	s_buffer_load_dword s9, s[16:19], s9                       // 00000000031C: F4200248 12000000
	s_buffer_load_dword s11, s[16:19], s11                     // 000000000324: F42002C8 16000000
	s_buffer_load_dword s24, s[16:19], s2                      // 00000000032C: F4200608 04000000
	s_buffer_load_dword s25, s[16:19], s20                     // 000000000334: F4200648 28000000
	s_buffer_load_dword s26, s[16:19], s21                     // 00000000033C: F4200688 2A000000
	s_buffer_load_dword s22, s[16:19], s22                     // 000000000344: F4200588 2C000000
	s_add_i32 s23, s8, 1                                       // 00000000034C: 81178108
	s_cmp_lt_u32 s23, s3                                       // 000000000350: BF0A0317
	s_waitcnt lgkmcnt(0)                                       // 000000000354: BF8CC07F
	v_sub_f32_e32 v32, s10, v17                                // 000000000358: 0840220A
	v_sub_f32_e32 v33, s9, v16                                 // 00000000035C: 08422009
	v_sub_f32_e32 v34, s11, v0                                 // 000000000360: 0844000B
	s_cselect_b64 s[10:11], -1, 0                              // 000000000364: 858A80C1
	s_cmp_lt_u32 s8, 3                                         // 000000000368: BF0A8308
	v_mul_f32_e32 v35, v32, v32                                // 00000000036C: 10464120
	s_cselect_b64 s[20:21], -1, 0                              // 000000000370: 859480C1
	s_mov_b32 s8, s23                                          // 000000000374: BE880317
	s_and_b64 s[10:11], s[10:11], s[20:21]                     // 000000000378: 878A140A
	s_add_i32 s2, s2, 16                                       // 00000000037C: 81029002
	v_fmac_f32_e32 v35, v33, v33                               // 000000000380: 56464321
	v_fmac_f32_e32 v35, v34, v34                               // 000000000384: 56464522
	v_rsq_f32_e32 v36, v35                                     // 000000000388: 7E485D23
	v_cmp_neq_f32_e32 vcc_lo, 0, v35                           // 00000000038C: 7C1A4680
	v_cndmask_b32_e32 v35, 0, v36, vcc_lo                      // 000000000390: 02464880
	v_mul_f32_e32 v36, v36, v36                                // 000000000394: 10484924
	v_fma_f32 v38, v35, v32, v10                               // 000000000398: D54B0026 042A4123
	v_mul_f32_e32 v37, v35, v33                                // 0000000003A0: 104A4323
	v_mul_f32_e32 v32, v35, v32                                // 0000000003A4: 10404123
	v_fma_f32 v33, v35, v33, v11                               // 0000000003A8: D54B0021 042E4323
	v_mul_f32_e32 v39, v35, v34                                // 0000000003B0: 104E4523
	v_fma_f32 v34, v35, v34, v14                               // 0000000003B4: D54B0022 043A4523
	v_mul_f32_e32 v35, v38, v38                                // 0000000003BC: 10464D26
	v_mul_f32_e32 v32, v32, v8                                 // 0000000003C0: 10401120
	v_fmac_f32_e32 v35, v33, v33                               // 0000000003C4: 56464321
	v_fmac_f32_e32 v32, v37, v7                                // 0000000003C8: 56400F25
	v_fmac_f32_e32 v35, v34, v34                               // 0000000003CC: 56464522
	v_fmac_f32_e32 v32, v39, v9                                // 0000000003D0: 56401327
	v_rsq_f32_e32 v37, v35                                     // 0000000003D4: 7E4A5D23
	v_cmp_neq_f32_e32 vcc_lo, 0, v35                           // 0000000003D8: 7C1A4680
	v_max_f32_e32 v32, 0, v32                                  // 0000000003DC: 20404080
	v_mul_f32_e32 v35, s24, v32                                // 0000000003E0: 10464018
	v_fma_f32 v39, v32, v24, v23                               // 0000000003E4: D54B0027 045E3120
	v_fmaak_f32 v40, v32, v1, 0x38d1b717                       // 0000000003EC: 5A500320 38D1B717
	v_cndmask_b32_e32 v37, 0, v37, vcc_lo                      // 0000000003F4: 024A4A80
	v_mul_f32_e32 v32, v32, v30                                // 0000000003F8: 10403D20
	v_mul_f32_e32 v35, v35, v36                                // 0000000003FC: 10464923
	v_mul_f32_e32 v36, v31, v39                                // 000000000400: 10484F1F
	s_and_b64 vcc, exec, s[10:11]                              // 000000000404: 87EA0A7E
	v_mul_f32_e32 v38, v37, v38                                // 000000000408: 104C4D25
	v_mul_f32_e32 v33, v37, v33                                // 00000000040C: 10424325
	v_mul_f32_e32 v34, v37, v34                                // 000000000410: 10444525
	v_mul_f32_e32 v39, s25, v35                                // 000000000414: 104E4619
	v_mul_f32_e32 v41, s26, v35                                // 000000000418: 1052461A
	v_mul_f32_e32 v37, v38, v8                                 // 00000000041C: 104A1126
	v_mul_f32_e32 v38, v38, v10                                // 000000000420: 104C1526
	v_fmac_f32_e32 v37, v33, v7                                // 000000000424: 564A0F21
	v_fmac_f32_e32 v38, v33, v11                               // 000000000428: 564C1721
	v_fmac_f32_e32 v37, v34, v9                                // 00000000042C: 564A1322
	v_fmac_f32_e32 v38, v34, v14                               // 000000000430: 564C1D22
	v_max_f32_e32 v37, 0, v37                                  // 000000000434: 204A4A80
	v_max_f32_e32 v34, 0, v38                                  // 000000000438: 20444C80
	v_mul_f32_e32 v33, v37, v37                                // 00000000043C: 10424B25
	v_sub_f32_e64 v34, 1.0, v34 clamp                          // 000000000440: D5048022 000244F2
	v_fma_f32 v33, v33, v26, 1.0                               // 000000000448: D54B0021 03CA3521
	v_mul_f32_e32 v33, v33, v33                                // 000000000450: 10424321
	v_mul_f32_e32 v33, v33, v36                                // 000000000454: 10424921
	v_mul_f32_e32 v36, v34, v34                                // 000000000458: 10484522
	v_mul_f32_e32 v33, v33, v40                                // 00000000045C: 10425121
	v_mul_f32_e32 v36, v36, v36                                // 000000000460: 10484924
	v_rcp_f32_e32 v33, v33                                     // 000000000464: 7E425521
	v_mul_f32_e32 v34, v34, v36                                // 000000000468: 10444922
	v_fma_f32 v36, v34, v21, v18                               // 00000000046C: D54B0024 044A2B22
	v_fma_f32 v37, v34, v22, v19                               // 000000000474: D54B0025 044E2D22
	v_fma_f32 v34, v34, v25, v20                               // 00000000047C: D54B0022 04523322
	v_fma_f32 v38, v32, v33, -v27                              // 000000000484: D54B0026 846E4320
	v_fma_f32 v40, v32, v33, -v28                              // 00000000048C: D54B0028 84724320
	v_fma_f32 v32, v32, v33, -v29                              // 000000000494: D54B0020 84764320
	v_mul_f32_e32 v33, s22, v35                                // 00000000049C: 10424616
	v_fma_f32 v35, v36, v38, v27                               // 0000000004A0: D54B0023 046E4D24
	v_fma_f32 v36, v37, v40, v28                               // 0000000004A8: D54B0024 04725125
	v_fma_f32 v32, v34, v32, v29                               // 0000000004B0: D54B0020 04764122
	v_fmac_f32_e32 v12, v39, v35                               // 0000000004B8: 56184727
	v_fmac_f32_e32 v13, v41, v36                               // 0000000004BC: 561A4929
	v_fmac_f32_e32 v15, v33, v32                               // 0000000004C0: 561E4121
	s_cbranch_vccnz _L1                                        // 0000000004C4: BF87FF89
_L0:
	image_sample v10, v[2:3], s[36:43], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000004C8: F0800108 00690A02
	image_sample v[0:2], v[2:3], s[44:51], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 0000000004D0: F0800708 006B0002
	s_load_dwordx8 s[16:23], s[0:1], 0xe0                      // 0000000004D8: F40C0400 FA0000E0
	v_cubema_f32 v3, v7, v8, v9                                // 0000000004E0: D5470003 04261107
	v_cubeid_f32 v11, v7, v8, v9                               // 0000000004E8: D544000B 04261107
	v_cubesc_f32 v14, v7, v8, v9                               // 0000000004F0: D545000E 04261107
	v_cubetc_f32 v7, v7, v8, v9                                // 0000000004F8: D5460007 04261107
	v_rcp_f32_e64 v3, |v3|                                     // 000000000500: D5AA0103 00000103
	v_rndne_f32_e32 v18, v11                                   // 000000000508: 7E24470B
	v_fmaak_f32 v16, v3, v14, 0x3fc00000                       // 00000000050C: 5A201D03 3FC00000
	v_fmaak_f32 v17, v3, v7, 0x3fc00000                        // 000000000514: 5A220F03 3FC00000
	s_and_b64 exec, exec, s[28:29]                             // 00000000051C: 87FE1C7E
	s_waitcnt lgkmcnt(0)                                       // 000000000520: BF8CC07F
	image_sample v[7:9], v[16:18], s[16:23], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_CUBE// 000000000524: F0800718 00640710
	s_clause 0x1                                               // 00000000052C: BFA10001
	s_buffer_load_dword s8, s[4:7], 0x14                       // 000000000530: F4200202 FA000014
	s_buffer_load_dwordx4 s[0:3], s[4:7], 0x20                 // 000000000538: F4280002 FA000020
	s_waitcnt vmcnt(2) lgkmcnt(0)                              // 000000000540: BF8C0072
	v_mul_f32_e32 v3, s8, v10                                  // 000000000544: 10061408
	s_waitcnt vmcnt(1)                                         // 000000000548: BF8C3F71
	v_mul_f32_e32 v0, s0, v0                                   // 00000000054C: 10000000
	v_mul_f32_e32 v1, s1, v1                                   // 000000000550: 10020201
	v_mul_f32_e32 v2, s2, v2                                   // 000000000554: 10040402
	v_mul_f32_e32 v4, v3, v4                                   // 000000000558: 10080903
	v_mul_f32_e32 v5, v3, v5                                   // 00000000055C: 100A0B03
	v_mul_f32_e32 v3, v3, v6                                   // 000000000560: 10060D03
	v_fmac_f32_e32 v12, s3, v0                                 // 000000000564: 56180003
	v_fmac_f32_e32 v13, s3, v1                                 // 000000000568: 561A0203
	v_fmac_f32_e32 v15, s3, v2                                 // 00000000056C: 561E0403
	s_waitcnt vmcnt(0)                                         // 000000000570: BF8C3F70
	v_fmac_f32_e32 v12, v4, v7                                 // 000000000574: 56180F04
	v_fmac_f32_e32 v13, v5, v8                                 // 000000000578: 561A1105
	v_fmac_f32_e32 v15, v3, v9                                 // 00000000057C: 561E1303
	v_mov_b32_e32 v3, 1.0                                      // 000000000580: 7E0602F2
	v_add_f32_e32 v0, 1.0, v12                                 // 000000000584: 060018F2
	v_add_f32_e32 v1, 1.0, v13                                 // 000000000588: 06021AF2
	v_add_f32_e32 v2, 1.0, v15                                 // 00000000058C: 06041EF2
	v_rcp_f32_e32 v0, v0                                       // 000000000590: 7E005500
	v_rcp_f32_e32 v1, v1                                       // 000000000594: 7E025501
	v_rcp_f32_e32 v2, v2                                       // 000000000598: 7E045502
	v_mul_f32_e32 v0, v12, v0                                  // 00000000059C: 1000010C
	v_mul_f32_e32 v1, v13, v1                                  // 0000000005A0: 1002030D
	v_mul_f32_e32 v2, v15, v2                                  // 0000000005A4: 1004050F
	v_log_f32_e32 v0, v0                                       // 0000000005A8: 7E004F00
	v_log_f32_e32 v1, v1                                       // 0000000005AC: 7E024F01
	v_log_f32_e32 v2, v2                                       // 0000000005B0: 7E044F02
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 0000000005B4: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 0000000005BC: 0E0202FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 0000000005C4: 0E0404FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 0000000005CC: 7E004B00
	v_exp_f32_e32 v1, v1                                       // 0000000005D0: 7E024B01
	v_exp_f32_e32 v2, v2                                       // 0000000005D4: 7E044B02
	exp mrt0 v0, v1, v2, v3 done vm                            // 0000000005D8: F800180F 03020100
	s_endpgm                                                   // 0000000005E0: BF810000

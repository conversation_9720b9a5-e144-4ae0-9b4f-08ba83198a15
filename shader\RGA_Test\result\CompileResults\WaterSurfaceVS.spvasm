; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 187
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_TEXCOORD0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6
               OpSource HLSL 600
               OpName %type_WaterParams "type.WaterParams"
               OpMemberName %type_WaterParams 0 "ViewProjectionMatrix"
               OpMemberName %type_WaterParams 1 "WorldMatrix"
               OpMemberName %type_WaterParams 2 "CameraPosition"
               OpMemberName %type_WaterParams 3 "Time"
               OpMemberName %type_WaterParams 4 "WaveAmplitude"
               OpMemberName %type_WaterParams 5 "WaveFrequency"
               OpMemberName %type_WaterParams 6 "WaveSpeed"
               OpMemberName %type_WaterParams 7 "WaveDirection1"
               OpMemberName %type_WaterParams 8 "WaveDirection2"
               OpMemberName %type_WaterParams 9 "WindDirection"
               OpMemberName %type_WaterParams 10 "WindStrength"
               OpName %WaterParams "WaterParams"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_TEXCOORD0 Location 2
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %WaterParams DescriptorSet 0
               OpDecorate %WaterParams Binding 0
               OpMemberDecorate %type_WaterParams 0 Offset 0
               OpMemberDecorate %type_WaterParams 0 MatrixStride 16
               OpMemberDecorate %type_WaterParams 0 RowMajor
               OpMemberDecorate %type_WaterParams 1 Offset 64
               OpMemberDecorate %type_WaterParams 1 MatrixStride 16
               OpMemberDecorate %type_WaterParams 1 RowMajor
               OpMemberDecorate %type_WaterParams 2 Offset 128
               OpMemberDecorate %type_WaterParams 3 Offset 140
               OpMemberDecorate %type_WaterParams 4 Offset 144
               OpMemberDecorate %type_WaterParams 5 Offset 148
               OpMemberDecorate %type_WaterParams 6 Offset 152
               OpMemberDecorate %type_WaterParams 7 Offset 160
               OpMemberDecorate %type_WaterParams 8 Offset 168
               OpMemberDecorate %type_WaterParams 9 Offset 176
               OpMemberDecorate %type_WaterParams 10 Offset 184
               OpDecorate %type_WaterParams Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_1 = OpConstant %int 1
      %int_7 = OpConstant %int 7
      %int_4 = OpConstant %int 4
      %int_5 = OpConstant %int 5
      %int_3 = OpConstant %int 3
      %int_8 = OpConstant %int 8
  %float_0_5 = OpConstant %float 0.5
    %float_2 = OpConstant %float 2
%float_0_100000001 = OpConstant %float 0.100000001
      %int_2 = OpConstant %int 2
     %int_10 = OpConstant %int 10
      %int_9 = OpConstant %int 9
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %33 = OpConstantComposite %v3float %float_1 %float_0 %float_0
         %34 = OpConstantComposite %v3float %float_0 %float_0 %float_1
%float_0_0500000007 = OpConstant %float 0.0500000007
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
    %v2float = OpTypeVector %float 2
%type_WaterParams = OpTypeStruct %mat4v4float %mat4v4float %v3float %float %float %float %float %v2float %v2float %v2float %float
%_ptr_Uniform_type_WaterParams = OpTypePointer Uniform %type_WaterParams
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
       %void = OpTypeVoid
         %46 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%WaterParams = OpVariable %_ptr_Uniform_type_WaterParams Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_v2float Output
%float_6_28318024 = OpConstant %float 6.28318024
         %52 = OpConstantNull %v3float
%float_1_55971968 = OpConstant %float 1.55971968
%float_9_42477036 = OpConstant %float 9.42477036
%float_1_03981316 = OpConstant %float 1.03981316
       %main = OpFunction %void None %46
         %56 = OpLabel
         %57 = OpLoad %v3float %in_var_POSITION
         %58 = OpLoad %v2float %in_var_TEXCOORD0
         %59 = OpCompositeExtract %float %57 0
         %60 = OpCompositeExtract %float %57 1
         %61 = OpCompositeExtract %float %57 2
         %62 = OpCompositeConstruct %v4float %59 %60 %61 %float_1
         %63 = OpAccessChain %_ptr_Uniform_mat4v4float %WaterParams %int_1
         %64 = OpLoad %mat4v4float %63
         %65 = OpMatrixTimesVector %v4float %64 %62
         %66 = OpVectorShuffle %v3float %65 %65 0 1 2
         %67 = OpVectorShuffle %v2float %65 %52 0 2
         %68 = OpAccessChain %_ptr_Uniform_v2float %WaterParams %int_7
         %69 = OpLoad %v2float %68
         %70 = OpAccessChain %_ptr_Uniform_float %WaterParams %int_4
         %71 = OpLoad %float %70
         %72 = OpAccessChain %_ptr_Uniform_float %WaterParams %int_5
         %73 = OpLoad %float %72
         %74 = OpAccessChain %_ptr_Uniform_float %WaterParams %int_3
         %75 = OpLoad %float %74
         %76 = OpExtInst %v2float %1 Normalize %69
         %77 = OpFMul %float %float_6_28318024 %73
         %78 = OpFDiv %float %float_1_55971968 %73
         %79 = OpExtInst %float %1 Sqrt %78
         %80 = OpDot %float %76 %67
         %81 = OpFMul %float %77 %80
         %82 = OpFMul %float %79 %75
         %83 = OpFSub %float %81 %82
         %84 = OpFDiv %float %71 %77
         %85 = OpCompositeExtract %float %76 0
         %86 = OpFNegate %float %85
         %87 = OpFMul %float %86 %85
         %88 = OpExtInst %float %1 Sin %83
         %89 = OpFMul %float %71 %88
         %90 = OpFMul %float %87 %89
         %91 = OpExtInst %float %1 Cos %83
         %92 = OpFMul %float %71 %91
         %93 = OpFMul %float %85 %92
         %94 = OpCompositeExtract %float %76 1
         %95 = OpFMul %float %86 %94
         %96 = OpFMul %float %95 %89
         %97 = OpCompositeConstruct %v3float %90 %93 %96
         %98 = OpFMul %float %94 %92
         %99 = OpFNegate %float %94
        %100 = OpFMul %float %99 %94
        %101 = OpFMul %float %100 %89
        %102 = OpCompositeConstruct %v3float %96 %98 %101
        %103 = OpFMul %float %84 %88
        %104 = OpFMul %float %85 %103
        %105 = OpFMul %float %84 %91
        %106 = OpFMul %float %94 %103
        %107 = OpCompositeConstruct %v3float %104 %105 %106
        %108 = OpAccessChain %_ptr_Uniform_v2float %WaterParams %int_8
        %109 = OpLoad %v2float %108
        %110 = OpFMul %float %71 %float_0_5
        %111 = OpExtInst %v2float %1 Normalize %109
        %112 = OpFMul %float %73 %float_9_42477036
        %113 = OpFDiv %float %float_1_03981316 %73
        %114 = OpExtInst %float %1 Sqrt %113
        %115 = OpDot %float %111 %67
        %116 = OpFMul %float %112 %115
        %117 = OpFMul %float %114 %75
        %118 = OpFSub %float %116 %117
        %119 = OpFDiv %float %110 %112
        %120 = OpCompositeExtract %float %111 0
        %121 = OpFNegate %float %120
        %122 = OpFMul %float %121 %120
        %123 = OpExtInst %float %1 Sin %118
        %124 = OpFMul %float %110 %123
        %125 = OpFMul %float %122 %124
        %126 = OpExtInst %float %1 Cos %118
        %127 = OpFMul %float %110 %126
        %128 = OpFMul %float %120 %127
        %129 = OpCompositeExtract %float %111 1
        %130 = OpFMul %float %121 %129
        %131 = OpFMul %float %130 %124
        %132 = OpCompositeConstruct %v3float %125 %128 %131
        %133 = OpFMul %float %129 %127
        %134 = OpFNegate %float %129
        %135 = OpFMul %float %134 %129
        %136 = OpFMul %float %135 %124
        %137 = OpCompositeConstruct %v3float %131 %133 %136
        %138 = OpFMul %float %119 %123
        %139 = OpFMul %float %120 %138
        %140 = OpFMul %float %119 %126
        %141 = OpFMul %float %129 %138
        %142 = OpCompositeConstruct %v3float %139 %140 %141
        %143 = OpFMul %float %75 %float_2
        %144 = OpCompositeExtract %float %65 0
        %145 = OpFMul %float %144 %float_0_100000001
        %146 = OpFAdd %float %143 %145
        %147 = OpCompositeExtract %float %65 2
        %148 = OpFMul %float %147 %float_0_100000001
        %149 = OpFAdd %float %146 %148
        %150 = OpExtInst %float %1 Sin %149
        %151 = OpAccessChain %_ptr_Uniform_float %WaterParams %int_10
        %152 = OpLoad %float %151
        %153 = OpFMul %float %150 %152
        %154 = OpAccessChain %_ptr_Uniform_v2float %WaterParams %int_9
        %155 = OpLoad %v2float %154
        %156 = OpVectorTimesScalar %v2float %155 %153
        %157 = OpVectorTimesScalar %v2float %156 %float_0_100000001
        %158 = OpFAdd %v3float %107 %142
        %159 = OpFAdd %v3float %66 %158
        %160 = OpVectorShuffle %v2float %159 %159 0 2
        %161 = OpFAdd %v2float %160 %157
        %162 = OpVectorShuffle %v3float %159 %161 3 1 4
        %163 = OpCompositeExtract %float %161 0
        %164 = OpCompositeExtract %float %159 1
        %165 = OpCompositeExtract %float %161 1
        %166 = OpCompositeConstruct %v4float %163 %164 %165 %float_1
        %167 = OpAccessChain %_ptr_Uniform_mat4v4float %WaterParams %int_0
        %168 = OpLoad %mat4v4float %167
        %169 = OpMatrixTimesVector %v4float %168 %166
        %170 = OpFAdd %v3float %97 %132
        %171 = OpFAdd %v3float %170 %33
        %172 = OpExtInst %v3float %1 Normalize %171
        %173 = OpFAdd %v3float %102 %137
        %174 = OpFAdd %v3float %173 %34
        %175 = OpExtInst %v3float %1 Normalize %174
        %176 = OpExtInst %v3float %1 Cross %172 %175
        %177 = OpExtInst %v3float %1 Normalize %176
        %178 = OpFMul %float %75 %float_0_100000001
        %179 = OpFMul %float %75 %float_0_0500000007
        %180 = OpCompositeConstruct %v2float %178 %179
        %181 = OpVectorTimesScalar %v2float %157 %float_0_5
        %182 = OpFAdd %v2float %180 %181
        %183 = OpAccessChain %_ptr_Uniform_v3float %WaterParams %int_2
        %184 = OpLoad %v3float %183
        %185 = OpFSub %v3float %184 %162
        %186 = OpExtInst %v3float %1 Normalize %185
               OpStore %gl_Position %169
               OpStore %out_var_TEXCOORD0 %162
               OpStore %out_var_TEXCOORD1 %177
               OpStore %out_var_TEXCOORD2 %58
               OpStore %out_var_TEXCOORD3 %186
               OpStore %out_var_TEXCOORD4 %169
               OpStore %out_var_TEXCOORD5 %169
               OpStore %out_var_TEXCOORD6 %182
               OpReturn
               OpFunctionEnd

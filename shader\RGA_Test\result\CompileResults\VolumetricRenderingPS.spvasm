; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 211
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD2 %in_var_TEXCOORD3 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_VolumeParams "type.VolumeParams"
               OpMemberName %type_VolumeParams 0 "VolumeMin"
               OpMemberName %type_VolumeParams 1 "StepSize"
               OpMemberName %type_VolumeParams 2 "VolumeMax"
               OpMemberName %type_VolumeParams 3 "MaxSteps"
               OpMemberName %type_VolumeParams 4 "LightDirection"
               OpMemberName %type_VolumeParams 5 "Density"
               OpMemberName %type_VolumeParams 6 "LightColor"
               OpMemberName %type_VolumeParams 7 "Absorption"
               OpMemberName %type_VolumeParams 8 "ScatteringColor"
               OpMemberName %type_VolumeParams 9 "Scattering"
               OpMemberName %type_VolumeParams 10 "Time"
               OpMemberName %type_VolumeParams 11 "NoiseScale"
               OpMemberName %type_VolumeParams 12 "NoiseStrength"
               OpName %VolumeParams "VolumeParams"
               OpName %type_3d_image "type.3d.image"
               OpName %VolumeTexture "VolumeTexture"
               OpName %NoiseTexture "NoiseTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %VolumeParams DescriptorSet 0
               OpDecorate %VolumeParams Binding 0
               OpDecorate %VolumeTexture DescriptorSet 0
               OpDecorate %VolumeTexture Binding 0
               OpDecorate %NoiseTexture DescriptorSet 0
               OpDecorate %NoiseTexture Binding 1
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_VolumeParams 0 Offset 0
               OpMemberDecorate %type_VolumeParams 1 Offset 12
               OpMemberDecorate %type_VolumeParams 2 Offset 16
               OpMemberDecorate %type_VolumeParams 3 Offset 28
               OpMemberDecorate %type_VolumeParams 4 Offset 32
               OpMemberDecorate %type_VolumeParams 5 Offset 44
               OpMemberDecorate %type_VolumeParams 6 Offset 48
               OpMemberDecorate %type_VolumeParams 7 Offset 60
               OpMemberDecorate %type_VolumeParams 8 Offset 64
               OpMemberDecorate %type_VolumeParams 9 Offset 76
               OpMemberDecorate %type_VolumeParams 10 Offset 80
               OpMemberDecorate %type_VolumeParams 11 Offset 84
               OpMemberDecorate %type_VolumeParams 12 Offset 88
               OpDecorate %type_VolumeParams Block
        %int = OpTypeInt 32 1
      %int_4 = OpConstant %int 4
      %int_3 = OpConstant %int 3
      %int_0 = OpConstant %int 0
      %int_2 = OpConstant %int 2
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %22 = OpConstantComposite %v3float %float_0 %float_0 %float_0
    %float_1 = OpConstant %float 1
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
%float_0_00100000005 = OpConstant %float 0.00100000005
      %int_6 = OpConstant %int 6
      %int_8 = OpConstant %int 8
      %int_9 = OpConstant %int 9
      %int_7 = OpConstant %int 7
      %int_1 = OpConstant %int 1
%float_0_00999999978 = OpConstant %float 0.00999999978
         %33 = OpConstantComposite %v3float %float_1 %float_1 %float_1
     %int_11 = OpConstant %int 11
     %int_10 = OpConstant %int 10
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_0500000007 = OpConstant %float 0.0500000007
%float_0_0799999982 = OpConstant %float 0.0799999982
     %int_12 = OpConstant %int 12
      %int_5 = OpConstant %int 5
    %float_2 = OpConstant %float 2
  %float_1_5 = OpConstant %float 1.5
  %float_0_5 = OpConstant %float 0.5
%type_VolumeParams = OpTypeStruct %v3float %float %v3float %int %v3float %float %v3float %float %v3float %float %float %float %float
%_ptr_Uniform_type_VolumeParams = OpTypePointer Uniform %type_VolumeParams
%type_3d_image = OpTypeImage %float 3D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_3d_image = OpTypePointer UniformConstant %type_3d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v4float = OpTypeVector %float 4
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %51 = OpTypeFunction %void
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image = OpTypeSampledImage %type_3d_image
%VolumeParams = OpVariable %_ptr_Uniform_type_VolumeParams Uniform
%VolumeTexture = OpVariable %_ptr_UniformConstant_type_3d_image UniformConstant
%NoiseTexture = OpVariable %_ptr_UniformConstant_type_3d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
%float_1_09000003 = OpConstant %float 1.09000003
%float_0_600000024 = OpConstant %float 0.600000024
%float_0_00716197258 = OpConstant %float 0.00716197258
       %main = OpFunction %void None %51
         %58 = OpLabel
         %59 = OpLoad %v3float %in_var_TEXCOORD2
         %60 = OpLoad %v3float %in_var_TEXCOORD3
         %61 = OpExtInst %v3float %1 Normalize %59
         %62 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_0
         %63 = OpLoad %v3float %62
         %64 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_2
         %65 = OpLoad %v3float %64
         %66 = OpFDiv %v3float %33 %61
         %67 = OpFMul %v3float %60 %66
         %68 = OpFSub %v3float %63 %67
         %69 = OpFSub %v3float %65 %67
         %70 = OpExtInst %v3float %1 NMin %68 %69
         %71 = OpExtInst %v3float %1 NMax %68 %69
         %72 = OpCompositeExtract %float %70 0
         %73 = OpCompositeExtract %float %70 1
         %74 = OpExtInst %float %1 NMax %72 %73
         %75 = OpCompositeExtract %float %70 2
         %76 = OpExtInst %float %1 NMax %74 %75
         %77 = OpCompositeExtract %float %71 0
         %78 = OpCompositeExtract %float %71 1
         %79 = OpExtInst %float %1 NMin %77 %78
         %80 = OpCompositeExtract %float %71 2
         %81 = OpExtInst %float %1 NMin %79 %80
         %82 = OpFOrdGreaterThan %bool %81 %76
               OpSelectionMerge %83 None
               OpBranchConditional %82 %84 %83
         %84 = OpLabel
         %85 = OpFOrdGreaterThan %bool %81 %float_0
               OpBranch %83
         %83 = OpLabel
         %86 = OpPhi %bool %false %58 %85 %84
         %87 = OpLogicalNot %bool %86
               OpSelectionMerge %88 None
               OpBranchConditional %87 %89 %88
         %89 = OpLabel
               OpKill
         %88 = OpLabel
         %90 = OpExtInst %float %1 NMax %float_0 %76
               OpBranch %91
         %91 = OpLabel
         %92 = OpPhi %v3float %22 %88 %93 %94
         %95 = OpPhi %float %float_1 %88 %96 %94
         %97 = OpPhi %float %90 %88 %98 %94
         %99 = OpPhi %int %int_0 %88 %100 %94
               OpLoopMerge %101 %94 None
               OpBranch %102
        %102 = OpLabel
        %103 = OpAccessChain %_ptr_Uniform_int %VolumeParams %int_3
        %104 = OpLoad %int %103
        %105 = OpSLessThan %bool %99 %104
               OpSelectionMerge %106 None
               OpBranchConditional %105 %107 %106
        %107 = OpLabel
        %108 = OpFOrdLessThan %bool %97 %81
               OpBranch %106
        %106 = OpLabel
        %109 = OpPhi %bool %false %102 %108 %107
               OpBranchConditional %109 %110 %101
        %110 = OpLabel
        %111 = OpVectorTimesScalar %v3float %61 %97
        %112 = OpFAdd %v3float %60 %111
        %113 = OpFDiv %v3float %63 %65
        %114 = OpFSub %v3float %112 %113
        %115 = OpFSub %v3float %114 %63
        %116 = OpLoad %type_3d_image %VolumeTexture
        %117 = OpLoad %type_sampler %LinearSampler
        %118 = OpSampledImage %type_sampled_image %116 %117
        %119 = OpImageSampleExplicitLod %v4float %118 %115 Lod %float_0
        %120 = OpCompositeExtract %float %119 0
        %121 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_11
        %122 = OpLoad %float %121
        %123 = OpVectorTimesScalar %v3float %112 %122
        %124 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_10
        %125 = OpLoad %float %124
        %126 = OpFMul %float %125 %float_0_100000001
        %127 = OpFMul %float %125 %float_0_0500000007
        %128 = OpFMul %float %125 %float_0_0799999982
        %129 = OpCompositeConstruct %v3float %126 %127 %128
        %130 = OpFAdd %v3float %123 %129
               OpBranch %131
        %131 = OpLabel
        %132 = OpPhi %float %float_1 %110 %133 %134
        %135 = OpPhi %float %float_0_5 %110 %136 %134
        %137 = OpPhi %float %float_0 %110 %138 %134
        %139 = OpPhi %int %int_0 %110 %140 %134
        %141 = OpSLessThan %bool %139 %int_4
               OpLoopMerge %142 %134 None
               OpBranchConditional %141 %134 %142
        %134 = OpLabel
        %143 = OpVectorTimesScalar %v3float %130 %132
        %144 = OpLoad %type_3d_image %NoiseTexture
        %145 = OpLoad %type_sampler %LinearSampler
        %146 = OpSampledImage %type_sampled_image %144 %145
        %147 = OpImageSampleExplicitLod %v4float %146 %143 Lod %float_0
        %148 = OpCompositeExtract %float %147 0
        %149 = OpFMul %float %135 %148
        %138 = OpFAdd %float %137 %149
        %136 = OpFMul %float %135 %float_0_5
        %133 = OpFMul %float %132 %float_2
        %140 = OpIAdd %int %139 %int_1
               OpBranch %131
        %142 = OpLabel
        %150 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_12
        %151 = OpLoad %float %150
        %152 = OpFMul %float %137 %151
        %153 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_5
        %154 = OpLoad %float %153
        %155 = OpFMul %float %152 %154
        %156 = OpFAdd %float %120 %155
        %157 = OpExtInst %float %1 NMax %float_0 %156
        %158 = OpFOrdGreaterThan %bool %157 %float_0_00100000005
               OpSelectionMerge %159 None
               OpBranchConditional %158 %160 %159
        %160 = OpLabel
        %161 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_4
        %162 = OpLoad %v3float %161
        %163 = OpFNegate %v3float %162
        %164 = OpExtInst %v3float %1 Normalize %163
        %165 = OpFNegate %v3float %61
        %166 = OpDot %float %164 %165
        %167 = OpFMul %float %float_0_600000024 %166
        %168 = OpFSub %float %float_1_09000003 %167
        %169 = OpExtInst %float %1 Pow %168 %float_1_5
        %170 = OpFDiv %float %float_0_00716197258 %169
        %171 = OpFSub %float %float_1 %170
        %172 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_6
        %173 = OpLoad %v3float %172
        %174 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_8
        %175 = OpLoad %v3float %174
        %176 = OpFMul %v3float %173 %175
        %177 = OpVectorTimesScalar %v3float %176 %157
        %178 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_9
        %179 = OpLoad %float %178
        %180 = OpVectorTimesScalar %v3float %177 %179
        %181 = OpVectorTimesScalar %v3float %180 %171
        %182 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_7
        %183 = OpLoad %float %182
        %184 = OpFMul %float %157 %183
        %185 = OpFAdd %float %184 %179
        %186 = OpFNegate %float %185
        %187 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_1
        %188 = OpLoad %float %187
        %189 = OpFMul %float %186 %188
        %190 = OpExtInst %float %1 Exp %189
        %191 = OpVectorTimesScalar %v3float %181 %95
        %192 = OpVectorTimesScalar %v3float %191 %float_1
        %193 = OpCompositeConstruct %v3float %190 %190 %190
        %194 = OpFSub %v3float %192 %193
        %195 = OpCompositeConstruct %v3float %185 %185 %185
        %196 = OpFDiv %v3float %194 %195
        %197 = OpFAdd %v3float %92 %196
        %198 = OpFMul %float %95 %190
        %199 = OpFOrdLessThan %bool %198 %float_0_00999999978
               OpSelectionMerge %200 None
               OpBranchConditional %199 %201 %200
        %201 = OpLabel
               OpBranch %101
        %200 = OpLabel
               OpBranch %159
        %159 = OpLabel
         %93 = OpPhi %v3float %92 %142 %197 %200
         %96 = OpPhi %float %95 %142 %198 %200
        %202 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_1
        %203 = OpLoad %float %202
         %98 = OpFAdd %float %97 %203
               OpBranch %94
         %94 = OpLabel
        %100 = OpIAdd %int %99 %int_1
               OpBranch %91
        %101 = OpLabel
        %204 = OpPhi %v3float %92 %106 %197 %201
        %205 = OpPhi %float %95 %106 %198 %201
        %206 = OpFSub %float %float_1 %205
        %207 = OpCompositeExtract %float %204 0
        %208 = OpCompositeExtract %float %204 1
        %209 = OpCompositeExtract %float %204 2
        %210 = OpCompositeConstruct %v4float %207 %208 %209 %206
               OpStore %out_var_SV_TARGET %210
               OpReturn
               OpFunctionEnd

; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 242
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_LightingParams "type.LightingParams"
               OpMemberName %type_LightingParams 0 "CameraPosition"
               OpMemberName %type_LightingParams 1 "NumLights"
               OpMemberName %type_LightingParams 2 "AmbientColor"
               OpMemberName %type_LightingParams 3 "AmbientStrength"
               OpName %LightingParams "LightingParams"
               OpName %type_LightData "type.LightData"
               OpMemberName %type_LightData 0 "LightPositions"
               OpMemberName %type_LightData 1 "LightColors"
               OpMemberName %type_LightData 2 "LightDirections"
               OpMemberName %type_LightData 3 "LightTypes"
               OpName %LightData "LightData"
               OpName %type_2d_image "type.2d.image"
               OpName %GBufferAlbedo "GBufferAlbedo"
               OpName %GBufferNormal "GBufferNormal"
               OpName %GBufferWorldPos "GBufferWorldPos"
               OpName %GBufferEmissive "GBufferEmissive"
               OpName %type_sampler "type.sampler"
               OpName %PointSampler "PointSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %LightingParams DescriptorSet 0
               OpDecorate %LightingParams Binding 0
               OpDecorate %LightData DescriptorSet 0
               OpDecorate %LightData Binding 1
               OpDecorate %GBufferAlbedo DescriptorSet 0
               OpDecorate %GBufferAlbedo Binding 0
               OpDecorate %GBufferNormal DescriptorSet 0
               OpDecorate %GBufferNormal Binding 1
               OpDecorate %GBufferWorldPos DescriptorSet 0
               OpDecorate %GBufferWorldPos Binding 2
               OpDecorate %GBufferEmissive DescriptorSet 0
               OpDecorate %GBufferEmissive Binding 3
               OpDecorate %PointSampler DescriptorSet 0
               OpDecorate %PointSampler Binding 0
               OpMemberDecorate %type_LightingParams 0 Offset 0
               OpMemberDecorate %type_LightingParams 1 Offset 12
               OpMemberDecorate %type_LightingParams 2 Offset 16
               OpMemberDecorate %type_LightingParams 3 Offset 28
               OpDecorate %type_LightingParams Block
               OpDecorate %_arr_v4float_uint_32 ArrayStride 16
               OpDecorate %_arr_v4int_uint_8 ArrayStride 16
               OpMemberDecorate %type_LightData 0 Offset 0
               OpMemberDecorate %type_LightData 1 Offset 512
               OpMemberDecorate %type_LightData 2 Offset 1024
               OpMemberDecorate %type_LightData 3 Offset 1536
               OpDecorate %type_LightData Block
      %float = OpTypeFloat 32
%float_3_14159274 = OpConstant %float 3.14159274
        %int = OpTypeInt 32 1
      %int_1 = OpConstant %int 1
      %int_3 = OpConstant %int 3
    %float_2 = OpConstant %float 2
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
         %27 = OpConstantComposite %v3float %float_1 %float_1 %float_1
      %int_0 = OpConstant %int 0
%float_0_0399999991 = OpConstant %float 0.0399999991
         %30 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
    %float_0 = OpConstant %float 0
         %32 = OpConstantComposite %v3float %float_0 %float_0 %float_0
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
     %int_32 = OpConstant %int 32
      %int_4 = OpConstant %int 4
      %int_2 = OpConstant %int 2
%float_1_20000005 = OpConstant %float 1.20000005
    %float_4 = OpConstant %float 4
%float_9_99999975en05 = OpConstant %float 9.99999975e-05
%float_0_454545468 = OpConstant %float 0.454545468
         %42 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
    %float_5 = OpConstant %float 5
%type_LightingParams = OpTypeStruct %v3float %int %v3float %float
%_ptr_Uniform_type_LightingParams = OpTypePointer Uniform %type_LightingParams
       %uint = OpTypeInt 32 0
    %uint_32 = OpConstant %uint 32
    %v4float = OpTypeVector %float 4
%_arr_v4float_uint_32 = OpTypeArray %v4float %uint_32
     %uint_8 = OpConstant %uint 8
      %v4int = OpTypeVector %int 4
%_arr_v4int_uint_8 = OpTypeArray %v4int %uint_8
%type_LightData = OpTypeStruct %_arr_v4float_uint_32 %_arr_v4float_uint_32 %_arr_v4float_uint_32 %_arr_v4int_uint_8
%_ptr_Uniform_type_LightData = OpTypePointer Uniform %type_LightData
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %57 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%LightingParams = OpVariable %_ptr_Uniform_type_LightingParams Uniform
  %LightData = OpVariable %_ptr_Uniform_type_LightData Uniform
%GBufferAlbedo = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%GBufferNormal = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%GBufferWorldPos = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%GBufferEmissive = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%PointSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
         %62 = OpUndef %v3float
%float_0_318309873 = OpConstant %float 0.318309873
         %64 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
       %main = OpFunction %void None %57
         %65 = OpLabel
         %66 = OpLoad %v2float %in_var_TEXCOORD0
         %67 = OpLoad %type_2d_image %GBufferAlbedo
         %68 = OpLoad %type_sampler %PointSampler
         %69 = OpSampledImage %type_sampled_image %67 %68
         %70 = OpImageSampleImplicitLod %v4float %69 %66 None
         %71 = OpLoad %type_2d_image %GBufferNormal
         %72 = OpLoad %type_sampler %PointSampler
         %73 = OpSampledImage %type_sampled_image %71 %72
         %74 = OpImageSampleImplicitLod %v4float %73 %66 None
         %75 = OpLoad %type_2d_image %GBufferWorldPos
         %76 = OpLoad %type_sampler %PointSampler
         %77 = OpSampledImage %type_sampled_image %75 %76
         %78 = OpImageSampleImplicitLod %v4float %77 %66 None
         %79 = OpLoad %type_2d_image %GBufferEmissive
         %80 = OpLoad %type_sampler %PointSampler
         %81 = OpSampledImage %type_sampled_image %79 %80
         %82 = OpImageSampleImplicitLod %v4float %81 %66 None
         %83 = OpVectorShuffle %v3float %70 %70 0 1 2
         %84 = OpCompositeExtract %float %70 3
         %85 = OpVectorShuffle %v3float %74 %74 0 1 2
         %86 = OpVectorTimesScalar %v3float %85 %float_2
         %87 = OpFSub %v3float %86 %27
         %88 = OpExtInst %v3float %1 Normalize %87
         %89 = OpCompositeExtract %float %74 3
         %90 = OpVectorShuffle %v3float %78 %78 0 1 2
         %91 = OpCompositeExtract %float %78 3
         %92 = OpVectorShuffle %v3float %82 %82 0 1 2
         %93 = OpAccessChain %_ptr_Uniform_v3float %LightingParams %int_0
         %94 = OpLoad %v3float %93
         %95 = OpFSub %v3float %94 %90
         %96 = OpExtInst %v3float %1 Normalize %95
         %97 = OpCompositeConstruct %v3float %84 %84 %84
         %98 = OpExtInst %v3float %1 FMix %30 %83 %97
               OpBranch %99
         %99 = OpLabel
        %100 = OpPhi %v3float %62 %65 %101 %102
        %103 = OpPhi %v3float %32 %65 %104 %102
        %105 = OpPhi %int %int_0 %65 %106 %102
               OpLoopMerge %107 %102 None
               OpBranch %108
        %108 = OpLabel
        %109 = OpAccessChain %_ptr_Uniform_int %LightingParams %int_1
        %110 = OpLoad %int %109
        %111 = OpSLessThan %bool %105 %110
               OpSelectionMerge %112 None
               OpBranchConditional %111 %113 %112
        %113 = OpLabel
        %114 = OpSLessThan %bool %105 %int_32
               OpBranch %112
        %112 = OpLabel
        %115 = OpPhi %bool %false %108 %114 %113
               OpBranchConditional %115 %116 %107
        %116 = OpLabel
        %117 = OpSDiv %int %105 %int_4
        %118 = OpSRem %int %105 %int_4
        %119 = OpBitcast %uint %118
        %120 = OpAccessChain %_ptr_Uniform_int %LightData %int_3 %117 %119
        %121 = OpLoad %int %120
        %122 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_0 %105
        %123 = OpLoad %v4float %122
        %124 = OpVectorShuffle %v3float %123 %123 0 1 2
        %125 = OpAccessChain %_ptr_Uniform_float %LightData %int_0 %105 %int_3
        %126 = OpLoad %float %125
        %127 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_1 %105
        %128 = OpLoad %v4float %127
        %129 = OpVectorShuffle %v3float %128 %128 0 1 2
        %130 = OpAccessChain %_ptr_Uniform_float %LightData %int_1 %105 %int_3
        %131 = OpLoad %float %130
        %132 = OpIEqual %bool %121 %int_0
               OpSelectionMerge %133 None
               OpBranchConditional %132 %134 %135
        %134 = OpLabel
        %136 = OpFSub %v3float %124 %90
        %137 = OpExtInst %v3float %1 Normalize %136
        %138 = OpExtInst %float %1 Length %136
        %139 = OpFMul %float %138 %138
        %140 = OpFMul %float %126 %126
        %141 = OpFDiv %float %139 %140
        %142 = OpFAdd %float %float_1 %141
               OpBranch %133
        %135 = OpLabel
        %143 = OpIEqual %bool %121 %int_1
               OpSelectionMerge %144 None
               OpBranchConditional %143 %145 %146
        %145 = OpLabel
        %147 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_2 %105
        %148 = OpLoad %v4float %147
        %149 = OpVectorShuffle %v3float %148 %148 0 1 2
        %150 = OpFNegate %v3float %149
        %151 = OpExtInst %v3float %1 Normalize %150
               OpBranch %144
        %146 = OpLabel
        %152 = OpIEqual %bool %121 %int_2
               OpSelectionMerge %153 None
               OpBranchConditional %152 %154 %153
        %154 = OpLabel
        %155 = OpFSub %v3float %124 %90
        %156 = OpExtInst %v3float %1 Normalize %155
        %157 = OpExtInst %float %1 Length %155
        %158 = OpFMul %float %157 %157
        %159 = OpFMul %float %126 %126
        %160 = OpFDiv %float %158 %159
        %161 = OpFAdd %float %float_1 %160
        %162 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_2 %105
        %163 = OpLoad %v4float %162
        %164 = OpVectorShuffle %v3float %163 %163 0 1 2
        %165 = OpExtInst %v3float %1 Normalize %164
        %166 = OpAccessChain %_ptr_Uniform_float %LightData %int_2 %105 %int_3
        %167 = OpLoad %float %166
        %168 = OpFNegate %v3float %165
        %169 = OpDot %float %156 %168
        %170 = OpExtInst %float %1 Cos %167
        %171 = OpFMul %float %167 %float_1_20000005
        %172 = OpExtInst %float %1 Cos %171
        %173 = OpFSub %float %170 %172
        %174 = OpFDiv %float %172 %173
        %175 = OpFSub %float %169 %174
        %176 = OpExtInst %float %1 FClamp %175 %float_0 %float_1
        %177 = OpFMul %float %161 %176
               OpBranch %153
        %153 = OpLabel
        %178 = OpPhi %float %float_1 %146 %177 %154
        %179 = OpPhi %v3float %100 %146 %156 %154
               OpBranch %144
        %144 = OpLabel
        %180 = OpPhi %float %float_1 %145 %178 %153
        %181 = OpPhi %v3float %151 %145 %179 %153
               OpBranch %133
        %133 = OpLabel
        %182 = OpPhi %float %142 %134 %180 %144
        %101 = OpPhi %v3float %137 %134 %181 %144
        %183 = OpFAdd %v3float %96 %101
        %184 = OpExtInst %v3float %1 Normalize %183
        %185 = OpVectorTimesScalar %v3float %129 %131
        %186 = OpVectorTimesScalar %v3float %185 %182
        %187 = OpFMul %float %89 %89
        %188 = OpFMul %float %187 %187
        %189 = OpDot %float %88 %184
        %190 = OpExtInst %float %1 NMax %189 %float_0
        %191 = OpFMul %float %190 %190
        %192 = OpFMul %float %191 %188
        %193 = OpFMul %float %float_3_14159274 %192
        %194 = OpFMul %float %193 %192
        %195 = OpFDiv %float %188 %194
        %196 = OpDot %float %88 %96
        %197 = OpExtInst %float %1 NMax %196 %float_0
        %198 = OpDot %float %88 %101
        %199 = OpExtInst %float %1 NMax %198 %float_0
        %200 = OpFDiv %float %197 %197
        %201 = OpFDiv %float %199 %199
        %202 = OpFMul %float %201 %200
        %203 = OpDot %float %184 %96
        %204 = OpExtInst %float %1 NMax %203 %float_0
        %205 = OpFSub %float %float_1 %204
        %206 = OpExtInst %float %1 FClamp %205 %float_0 %float_1
        %207 = OpExtInst %float %1 Pow %206 %float_5
        %208 = OpVectorTimesScalar %v3float %98 %207
        %209 = OpFSub %v3float %27 %208
        %210 = OpFAdd %v3float %98 %209
        %211 = OpFSub %v3float %27 %210
        %212 = OpFSub %float %float_1 %84
        %213 = OpVectorTimesScalar %v3float %211 %212
        %214 = OpFMul %float %195 %202
        %215 = OpVectorTimesScalar %v3float %210 %214
        %216 = OpFMul %float %float_4 %197
        %217 = OpFMul %float %216 %199
        %218 = OpFAdd %float %217 %float_9_99999975en05
        %219 = OpCompositeConstruct %v3float %218 %218 %218
        %220 = OpFDiv %v3float %215 %219
        %221 = OpFMul %v3float %213 %83
        %222 = OpFMul %v3float %221 %64
        %223 = OpFMul %v3float %220 %186
        %224 = OpFAdd %v3float %222 %223
        %225 = OpVectorTimesScalar %v3float %224 %199
        %104 = OpFAdd %v3float %103 %225
               OpBranch %102
        %102 = OpLabel
        %106 = OpIAdd %int %105 %int_1
               OpBranch %99
        %107 = OpLabel
        %226 = OpAccessChain %_ptr_Uniform_v3float %LightingParams %int_2
        %227 = OpLoad %v3float %226
        %228 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_3
        %229 = OpLoad %float %228
        %230 = OpVectorTimesScalar %v3float %227 %229
        %231 = OpFMul %v3float %230 %83
        %232 = OpVectorTimesScalar %v3float %231 %91
        %233 = OpFAdd %v3float %232 %103
        %234 = OpFAdd %v3float %233 %92
        %235 = OpFDiv %v3float %234 %234
        %236 = OpFAdd %v3float %235 %27
        %237 = OpExtInst %v3float %1 Pow %236 %42
        %238 = OpCompositeExtract %float %237 0
        %239 = OpCompositeExtract %float %237 1
        %240 = OpCompositeExtract %float %237 2
        %241 = OpCompositeConstruct %v4float %238 %239 %240 %float_1
               OpStore %out_var_SV_TARGET %241
               OpReturn
               OpFunctionEnd

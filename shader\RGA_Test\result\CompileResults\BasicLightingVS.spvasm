; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 75
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TEXCOORD0 %in_var_COLOR0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 1 "WorldMatrix"
               OpMemberName %type_PerFrame 2 "NormalMatrix"
               OpMemberName %type_PerFrame 3 "LightDirection"
               OpMemberName %type_PerFrame 4 "CameraPosition"
               OpMemberName %type_PerFrame 5 "Time"
               OpName %PerFrame "PerFrame"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TEXCOORD0 Location 2
               OpDecorate %in_var_COLOR0 Location 3
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 208
               OpMemberDecorate %type_PerFrame 5 Offset 220
               OpDecorate %type_PerFrame Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
    %v3float = OpTypeVector %float 3
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %v3float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
       %void = OpTypeVoid
         %36 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v3float Output
       %main = OpFunction %void None %36
         %40 = OpLabel
         %41 = OpLoad %v3float %in_var_POSITION
         %42 = OpLoad %v3float %in_var_NORMAL
         %43 = OpLoad %v2float %in_var_TEXCOORD0
         %44 = OpLoad %v4float %in_var_COLOR0
         %45 = OpCompositeExtract %float %41 0
         %46 = OpCompositeExtract %float %41 1
         %47 = OpCompositeExtract %float %41 2
         %48 = OpCompositeConstruct %v4float %45 %46 %47 %float_1
         %49 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_1
         %50 = OpLoad %mat4v4float %49
         %51 = OpMatrixTimesVector %v4float %50 %48
         %52 = OpVectorShuffle %v3float %51 %51 0 1 2
         %53 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_0
         %54 = OpLoad %mat4v4float %53
         %55 = OpMatrixTimesVector %v4float %54 %51
         %56 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
         %57 = OpLoad %mat4v4float %56
         %58 = OpCompositeExtract %v4float %57 0
         %59 = OpVectorShuffle %v3float %58 %58 0 1 2
         %60 = OpCompositeExtract %v4float %57 1
         %61 = OpVectorShuffle %v3float %60 %60 0 1 2
         %62 = OpCompositeExtract %v4float %57 2
         %63 = OpVectorShuffle %v3float %62 %62 0 1 2
         %64 = OpCompositeConstruct %mat3v3float %59 %61 %63
         %65 = OpMatrixTimesVector %v3float %64 %42
         %66 = OpExtInst %v3float %1 Normalize %65
         %67 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_4
         %68 = OpLoad %v3float %67
         %69 = OpFSub %v3float %68 %52
         %70 = OpExtInst %v3float %1 Normalize %69
         %71 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_3
         %72 = OpLoad %v3float %71
         %73 = OpFNegate %v3float %72
         %74 = OpExtInst %v3float %1 Normalize %73
               OpStore %gl_Position %55
               OpStore %out_var_TEXCOORD0 %52
               OpStore %out_var_TEXCOORD1 %66
               OpStore %out_var_TEXCOORD2 %43
               OpStore %out_var_TEXCOORD3 %44
               OpStore %out_var_TEXCOORD4 %70
               OpStore %out_var_TEXCOORD5 %74
               OpReturn
               OpFunctionEnd

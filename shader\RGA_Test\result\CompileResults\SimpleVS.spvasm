; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 18
; Schema: 0
               OpCapability Shader
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %gl_Position
               OpSource HLSL 600
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v4float = OpTypeVector %float 4
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %11 = OpTypeFunction %void
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
       %main = OpFunction %void None %11
         %12 = OpLabel
         %13 = OpLoad %v3float %in_var_POSITION
         %14 = OpCompositeExtract %float %13 0
         %15 = OpCompositeExtract %float %13 1
         %16 = OpCompositeExtract %float %13 2
         %17 = OpCompositeConstruct %v4float %14 %15 %16 %float_1
               OpStore %gl_Position %17
               OpReturn
               OpFunctionEnd

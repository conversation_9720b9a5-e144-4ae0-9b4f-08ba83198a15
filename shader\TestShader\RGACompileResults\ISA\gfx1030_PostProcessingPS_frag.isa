_amdgpu_ps_main:
	s_mov_b64 s[16:17], exec                                   // 000000000000: BE90047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s18, s1                                          // 000000000008: BE920301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s19, s1                                          // 000000000014: BE930301
	v_interp_p1_f32_e32 v17, v0, attr1.x                       // 000000000018: C8440400
	s_load_dwordx4 s[8:11], s[18:19], null                     // 00000000001C: F4080209 FA000000
	v_interp_p1_f32_e32 v18, v0, attr1.y                       // 000000000024: C8480500
	v_interp_p1_f32_e32 v13, v0, attr2.x                       // 000000000028: C8340800
	v_interp_p1_f32_e32 v16, v0, attr2.y                       // 00000000002C: C8400900
	v_interp_p1_f32_e32 v11, v0, attr3.x                       // 000000000030: C82C0C00
	v_interp_p1_f32_e32 v14, v0, attr3.y                       // 000000000034: C8380D00
	v_interp_p1_f32_e32 v6, v0, attr4.x                        // 000000000038: C8181000
	v_interp_p1_f32_e32 v8, v0, attr4.y                        // 00000000003C: C8201100
	v_interp_p1_f32_e32 v7, v0, attr5.x                        // 000000000040: C81C1400
	v_interp_p1_f32_e32 v9, v0, attr5.y                        // 000000000044: C8241500
	v_interp_p1_f32_e32 v20, v0, attr6.x                       // 000000000048: C8501800
	v_interp_p1_f32_e32 v21, v0, attr6.y                       // 00000000004C: C8541900
	v_interp_p1_f32_e32 v15, v0, attr7.x                       // 000000000050: C83C1C00
	v_interp_p1_f32_e32 v19, v0, attr7.y                       // 000000000054: C84C1D00
	v_interp_p1_f32_e32 v10, v0, attr8.x                       // 000000000058: C8282000
	v_interp_p1_f32_e32 v12, v0, attr8.y                       // 00000000005C: C8302100
	v_interp_p1_f32_e32 v4, v0, attr0.x                        // 000000000060: C8100000
	v_interp_p1_f32_e32 v5, v0, attr0.y                        // 000000000064: C8140100
	v_interp_p2_f32_e32 v17, v1, attr1.x                       // 000000000068: C8450401
	v_interp_p2_f32_e32 v18, v1, attr1.y                       // 00000000006C: C8490501
	s_waitcnt lgkmcnt(0)                                       // 000000000070: BF8CC07F
	s_buffer_load_dword s0, s[8:11], 0x2c                      // 000000000074: F4200004 FA00002C
	v_interp_p2_f32_e32 v13, v1, attr2.x                       // 00000000007C: C8350801
	v_interp_p2_f32_e32 v16, v1, attr2.y                       // 000000000080: C8410901
	v_interp_p2_f32_e32 v11, v1, attr3.x                       // 000000000084: C82D0C01
	v_interp_p2_f32_e32 v14, v1, attr3.y                       // 000000000088: C8390D01
	v_interp_p2_f32_e32 v6, v1, attr4.x                        // 00000000008C: C8191001
	v_interp_p2_f32_e32 v8, v1, attr4.y                        // 000000000090: C8211101
	v_interp_p2_f32_e32 v7, v1, attr5.x                        // 000000000094: C81D1401
	v_interp_p2_f32_e32 v9, v1, attr5.y                        // 000000000098: C8251501
	v_interp_p2_f32_e32 v20, v1, attr6.x                       // 00000000009C: C8511801
	v_interp_p2_f32_e32 v21, v1, attr6.y                       // 0000000000A0: C8551901
	v_interp_p2_f32_e32 v15, v1, attr7.x                       // 0000000000A4: C83D1C01
	v_interp_p2_f32_e32 v19, v1, attr7.y                       // 0000000000A8: C84D1D01
	v_interp_p2_f32_e32 v10, v1, attr8.x                       // 0000000000AC: C8292001
	v_interp_p2_f32_e32 v12, v1, attr8.y                       // 0000000000B0: C8312101
	v_interp_p2_f32_e32 v4, v1, attr0.x                        // 0000000000B4: C8110001
	v_interp_p2_f32_e32 v5, v1, attr0.y                        // 0000000000B8: C8150101
	s_waitcnt lgkmcnt(0)                                       // 0000000000BC: BF8CC07F
	s_cmp_lg_u32 s0, 1                                         // 0000000000C0: BF078100
	s_cbranch_scc0 _L0                                         // 0000000000C4: BF840010
	s_cmp_lg_u32 s0, 2                                         // 0000000000C8: BF078200
	s_cbranch_scc0 _L1                                         // 0000000000CC: BF84000F
	s_cmp_lg_u32 s0, 3                                         // 0000000000D0: BF078300
	s_cbranch_scc0 _L2                                         // 0000000000D4: BF84000E
	s_cmp_lg_u32 s0, 4                                         // 0000000000D8: BF078400
	s_cbranch_scc0 _L3                                         // 0000000000DC: BF84000D
	s_clause 0x1                                               // 0000000000E0: BFA10001
	s_load_dwordx8 s[0:7], s[18:19], null                      // 0000000000E4: F40C0009 FA000000
	s_load_dwordx4 s[12:15], s[18:19], 0x20                    // 0000000000EC: F4080309 FA000020
	s_waitcnt lgkmcnt(0)                                       // 0000000000F4: BF8CC07F
	image_sample v[0:3], v[4:5], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000F8: F0800F08 00600004
	s_cbranch_execz _L3                                        // 000000000100: BF880004
	s_branch _L4                                               // 000000000104: BF820023
_L0:
	s_branch _L5                                               // 000000000108: BF8200C1
_L1:
	s_branch _L6                                               // 00000000010C: BF8200AA
_L2:
	s_branch _L7                                               // 000000000110: BF820021
_L3:
	s_waitcnt vmcnt(0)                                         // 000000000114: BF8C3F70
	v_add_f32_e32 v0, -0.5, v5                                 // 000000000118: 06000AF1
	v_add_f32_e32 v1, -0.5, v4                                 // 00000000011C: 060208F1
	v_mov_b32_e32 v3, 1.0                                      // 000000000120: 7E0602F2
	v_mul_f32_e32 v2, v0, v0                                   // 000000000124: 10040100
	v_fmac_f32_e32 v2, v1, v1                                  // 000000000128: 56040301
	v_fmamk_f32 v2, v2, 0x3e4ccccd, v3                         // 00000000012C: 58040702 3E4CCCCD
	v_mul_f32_e32 v1, v2, v1                                   // 000000000134: 10020302
	v_mul_f32_e32 v0, v2, v0                                   // 000000000138: 10000102
	v_mov_b32_e32 v2, 0                                        // 00000000013C: 7E040280
	v_add_f32_e32 v22, 0.5, v1                                 // 000000000140: 062C02F0
	v_add_f32_e32 v23, 0.5, v0                                 // 000000000144: 062E00F0
	v_min_f32_e32 v0, v22, v23                                 // 000000000148: 1E002F16
	v_max_f32_e32 v1, v22, v23                                 // 00000000014C: 20022F16
	v_cmp_ngt_f32_e32 vcc_lo, 0, v0                            // 000000000150: 7C160080
	v_cmp_nlt_f32_e64 s0, 1.0, v1                              // 000000000154: D40E0000 000202F2
	v_mov_b32_e32 v1, 0                                        // 00000000015C: 7E020280
	v_mov_b32_e32 v0, 0                                        // 000000000160: 7E000280
	s_and_b64 s[2:3], s[0:1], vcc                              // 000000000164: 87826A00
	s_and_saveexec_b64 s[0:1], s[2:3]                          // 000000000168: BE802402
	s_cbranch_execz _L8                                        // 00000000016C: BF880008
	s_clause 0x1                                               // 000000000170: BFA10001
	s_load_dwordx8 s[20:27], s[18:19], null                    // 000000000174: F40C0509 FA000000
	s_load_dwordx4 s[4:7], s[18:19], 0x20                      // 00000000017C: F4080109 FA000020
	s_waitcnt lgkmcnt(0)                                       // 000000000184: BF8CC07F
	image_sample v[0:3], v[22:23], s[20:27], s[4:7] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000188: F0800F08 00250016
_L8:
	s_or_b64 exec, exec, s[0:1]                                // 000000000190: 88FE007E
_L4:
	s_cbranch_execnz _L9                                       // 000000000194: BF890087
_L7:
	s_clause 0x1                                               // 000000000198: BFA10001
	s_load_dwordx8 s[0:7], s[18:19], 0x60                      // 00000000019C: F40C0009 FA000060
	s_load_dwordx4 s[12:15], s[18:19], 0x50                    // 0000000001A4: F4080309 FA000050
	s_waitcnt lgkmcnt(0)                                       // 0000000001AC: BF8CC07F
	image_sample v22, v[4:5], s[0:7], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000001B0: F0800108 00601604
	s_clause 0x1                                               // 0000000001B8: BFA10001
	s_load_dwordx8 s[0:7], s[18:19], null                      // 0000000001BC: F40C0009 FA000000
	s_load_dwordx4 s[12:15], s[18:19], 0x20                    // 0000000001C4: F4080309 FA000020
	s_waitcnt lgkmcnt(0)                                       // 0000000001CC: BF8CC07F
	image_sample v[0:3], v[4:5], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000001D0: F0800F08 00600004
	s_waitcnt vmcnt(1)                                         // 0000000001D8: BF8C3F71
	v_add_f32_e32 v22, -0.5, v22                               // 0000000001DC: 062C2CF1
	v_mul_f32_e64 v30, 0x40a00000, |v22| clamp                 // 0000000001E0: D508821E 00022CFF 40A00000
	image_sample v[22:25], v[17:18], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000001EC: F0800F08 00601611
	s_waitcnt vmcnt(1)                                         // 0000000001F4: BF8C3F71
	v_mul_f32_e32 v26, 0x3e800000, v3                          // 0000000001F8: 103406FF 3E800000
	v_mul_f32_e32 v27, 0x3e800000, v2                          // 000000000200: 103604FF 3E800000
	v_mul_f32_e32 v28, 0x3e800000, v1                          // 000000000208: 103802FF 3E800000
	v_mul_f32_e32 v29, 0x3e800000, v0                          // 000000000210: 103A00FF 3E800000
	s_waitcnt vmcnt(0)                                         // 000000000218: BF8C3F70
	v_fmac_f32_e32 v29, 0x3d800000, v22                        // 00000000021C: 563A2CFF 3D800000
	v_fmac_f32_e32 v28, 0x3d800000, v23                        // 000000000224: 56382EFF 3D800000
	v_fmac_f32_e32 v27, 0x3d800000, v24                        // 00000000022C: 563630FF 3D800000
	v_fmac_f32_e32 v26, 0x3d800000, v25                        // 000000000234: 563432FF 3D800000
	image_sample  v[22:25], [v13, v16], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000023C: F0800F0A 0060160D 00000010
	s_waitcnt vmcnt(0)                                         // 000000000248: BF8C3F70
	v_fmac_f32_e32 v29, 0x3e000000, v22                        // 00000000024C: 563A2CFF 3E000000
	v_fmac_f32_e32 v28, 0x3e000000, v23                        // 000000000254: 56382EFF 3E000000
	v_fmac_f32_e32 v27, 0x3e000000, v24                        // 00000000025C: 563630FF 3E000000
	v_fmac_f32_e32 v26, 0x3e000000, v25                        // 000000000264: 563432FF 3E000000
	image_sample  v[22:25], [v11, v14], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000026C: F0800F0A 0060160B 0000000E
	s_waitcnt vmcnt(0)                                         // 000000000278: BF8C3F70
	v_fmac_f32_e32 v29, 0x3d800000, v22                        // 00000000027C: 563A2CFF 3D800000
	v_fmac_f32_e32 v28, 0x3d800000, v23                        // 000000000284: 56382EFF 3D800000
	v_fmac_f32_e32 v27, 0x3d800000, v24                        // 00000000028C: 563630FF 3D800000
	v_fmac_f32_e32 v26, 0x3d800000, v25                        // 000000000294: 563432FF 3D800000
	image_sample  v[22:25], [v6, v8], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000029C: F0800F0A 00601606 00000008
	s_waitcnt vmcnt(0)                                         // 0000000002A8: BF8C3F70
	v_fmac_f32_e32 v29, 0x3e000000, v22                        // 0000000002AC: 563A2CFF 3E000000
	v_fmac_f32_e32 v28, 0x3e000000, v23                        // 0000000002B4: 56382EFF 3E000000
	v_fmac_f32_e32 v27, 0x3e000000, v24                        // 0000000002BC: 563630FF 3E000000
	v_fmac_f32_e32 v26, 0x3e000000, v25                        // 0000000002C4: 563432FF 3E000000
	image_sample  v[22:25], [v7, v9], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000002CC: F0800F0A 00601607 00000009
	s_waitcnt vmcnt(0)                                         // 0000000002D8: BF8C3F70
	v_fmac_f32_e32 v29, 0x3e800000, v22                        // 0000000002DC: 563A2CFF 3E800000
	v_fmac_f32_e32 v28, 0x3e800000, v23                        // 0000000002E4: 56382EFF 3E800000
	v_fmac_f32_e32 v27, 0x3e800000, v24                        // 0000000002EC: 563630FF 3E800000
	v_fmac_f32_e32 v26, 0x3e800000, v25                        // 0000000002F4: 563432FF 3E800000
	image_sample v[22:25], v[20:21], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000002FC: F0800F08 00601614
	s_waitcnt vmcnt(0)                                         // 000000000304: BF8C3F70
	v_fmac_f32_e32 v29, 0x3e000000, v22                        // 000000000308: 563A2CFF 3E000000
	v_fmac_f32_e32 v28, 0x3e000000, v23                        // 000000000310: 56382EFF 3E000000
	v_fmac_f32_e32 v27, 0x3e000000, v24                        // 000000000318: 563630FF 3E000000
	v_fmac_f32_e32 v26, 0x3e000000, v25                        // 000000000320: 563432FF 3E000000
	image_sample  v[22:25], [v15, v19], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000328: F0800F0A 0060160F 00000013
	s_waitcnt vmcnt(0)                                         // 000000000334: BF8C3F70
	v_fmac_f32_e32 v29, 0x3d800000, v22                        // 000000000338: 563A2CFF 3D800000
	v_fmac_f32_e32 v28, 0x3d800000, v23                        // 000000000340: 56382EFF 3D800000
	v_fmac_f32_e32 v27, 0x3d800000, v24                        // 000000000348: 563630FF 3D800000
	v_fmac_f32_e32 v26, 0x3d800000, v25                        // 000000000350: 563432FF 3D800000
	image_sample  v[22:25], [v10, v12], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000358: F0800F0A 0060160A 0000000C
	s_waitcnt vmcnt(0)                                         // 000000000364: BF8C3F70
	v_fmac_f32_e32 v29, 0x3e000000, v22                        // 000000000368: 563A2CFF 3E000000
	v_fmac_f32_e32 v28, 0x3e000000, v23                        // 000000000370: 56382EFF 3E000000
	v_fmac_f32_e32 v27, 0x3e000000, v24                        // 000000000378: 563630FF 3E000000
	v_fmac_f32_e32 v26, 0x3e000000, v25                        // 000000000380: 563432FF 3E000000
	v_sub_f32_e32 v22, v29, v0                                 // 000000000388: 082C011D
	v_sub_f32_e32 v23, v28, v1                                 // 00000000038C: 082E031C
	v_sub_f32_e32 v24, v27, v2                                 // 000000000390: 0830051B
	v_sub_f32_e32 v25, v26, v3                                 // 000000000394: 0832071A
	v_fma_f32 v0, v22, v30, v0                                 // 000000000398: D54B0000 04023D16
	v_fma_f32 v1, v23, v30, v1                                 // 0000000003A0: D54B0001 04063D17
	v_fma_f32 v2, v24, v30, v2                                 // 0000000003A8: D54B0002 040A3D18
	v_fmac_f32_e32 v3, v25, v30                                // 0000000003B0: 56063D19
_L9:
	s_cbranch_execnz _L10                                      // 0000000003B4: BF890015
_L6:
	s_clause 0x2                                               // 0000000003B8: BFA10002
	s_load_dwordx8 s[0:7], s[18:19], null                      // 0000000003BC: F40C0009 FA000000
	s_load_dwordx4 s[12:15], s[18:19], 0x20                    // 0000000003C4: F4080309 FA000020
	s_load_dwordx8 s[20:27], s[18:19], 0x30                    // 0000000003CC: F40C0509 FA000030
	s_waitcnt lgkmcnt(0)                                       // 0000000003D4: BF8CC07F
	image_sample v[0:3], v[4:5], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000003D8: F0800F08 00600004
	image_sample v[22:25], v[4:5], s[20:27], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000003E0: F0800F08 00651604
	s_waitcnt vmcnt(0)                                         // 0000000003E8: BF8C3F70
	v_fmamk_f32 v0, v22, 0x3e99999a, v0                        // 0000000003EC: 58000116 3E99999A
	v_fmamk_f32 v1, v23, 0x3e99999a, v1                        // 0000000003F4: 58020317 3E99999A
	v_fmamk_f32 v2, v24, 0x3e99999a, v2                        // 0000000003FC: 58040518 3E99999A
	v_fmac_f32_e32 v3, 0x3e99999a, v25                         // 000000000404: 560632FF 3E99999A
_L10:
	s_cbranch_execnz _L11                                      // 00000000040C: BF890072
_L5:
	s_clause 0x1                                               // 000000000410: BFA10001
	s_load_dwordx8 s[0:7], s[18:19], null                      // 000000000414: F40C0009 FA000000
	s_load_dwordx4 s[12:15], s[18:19], 0x20                    // 00000000041C: F4080309 FA000020
	s_waitcnt lgkmcnt(0)                                       // 000000000424: BF8CC07F
	s_clause 0x3                                               // 000000000428: BFA10003
	image_sample v[0:3], v[4:5], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000042C: F0800F08 00600004
	image_sample v[22:25], v[17:18], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000434: F0800F08 00601611
	image_sample  v[26:29], [v13, v16], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000043C: F0800F0A 00601A0D 00000010
	image_sample  v[30:33], [v11, v14], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000448: F0800F0A 00601E0B 0000000E
	s_waitcnt vmcnt(3)                                         // 000000000454: BF8C3F73
	v_mul_f32_e32 v3, 0x3e800000, v3                           // 000000000458: 100606FF 3E800000
	v_mul_f32_e32 v2, 0x3e800000, v2                           // 000000000460: 100404FF 3E800000
	v_mul_f32_e32 v1, 0x3e800000, v1                           // 000000000468: 100202FF 3E800000
	v_mul_f32_e32 v0, 0x3e800000, v0                           // 000000000470: 100000FF 3E800000
	s_waitcnt vmcnt(2)                                         // 000000000478: BF8C3F72
	v_fmac_f32_e32 v3, 0x3d800000, v25                         // 00000000047C: 560632FF 3D800000
	v_fmac_f32_e32 v2, 0x3d800000, v24                         // 000000000484: 560430FF 3D800000
	v_fmac_f32_e32 v1, 0x3d800000, v23                         // 00000000048C: 56022EFF 3D800000
	v_fmac_f32_e32 v0, 0x3d800000, v22                         // 000000000494: 56002CFF 3D800000
	s_clause 0x1                                               // 00000000049C: BFA10001
	image_sample  v[22:25], [v6, v8], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004A0: F0800F0A 00601606 00000008
	image_sample  v[6:9], [v7, v9], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004AC: F0800F0A 00600607 00000009
	s_waitcnt vmcnt(3)                                         // 0000000004B8: BF8C3F73
	v_fmac_f32_e32 v3, 0x3e000000, v29                         // 0000000004BC: 56063AFF 3E000000
	v_fmac_f32_e32 v2, 0x3e000000, v28                         // 0000000004C4: 560438FF 3E000000
	v_fmac_f32_e32 v1, 0x3e000000, v27                         // 0000000004CC: 560236FF 3E000000
	v_fmac_f32_e32 v0, 0x3e000000, v26                         // 0000000004D4: 560034FF 3E000000
	s_clause 0x2                                               // 0000000004DC: BFA10002
	image_sample v[26:29], v[20:21], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004E0: F0800F08 00601A14
	image_sample  v[13:16], [v15, v19], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004E8: F0800F0A 00600D0F 00000013
	image_sample  v[17:20], [v10, v12], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004F4: F0800F0A 0060110A 0000000C
	s_waitcnt vmcnt(5)                                         // 000000000500: BF8C3F75
	v_fmac_f32_e32 v2, 0x3d800000, v32                         // 000000000504: 560440FF 3D800000
	v_fmac_f32_e32 v1, 0x3d800000, v31                         // 00000000050C: 56023EFF 3D800000
	v_fmac_f32_e32 v0, 0x3d800000, v30                         // 000000000514: 56003CFF 3D800000
	v_fmac_f32_e32 v3, 0x3d800000, v33                         // 00000000051C: 560642FF 3D800000
	s_waitcnt vmcnt(4)                                         // 000000000524: BF8C3F74
	v_fmac_f32_e32 v0, 0x3e000000, v22                         // 000000000528: 56002CFF 3E000000
	v_fmac_f32_e32 v1, 0x3e000000, v23                         // 000000000530: 56022EFF 3E000000
	v_fmac_f32_e32 v2, 0x3e000000, v24                         // 000000000538: 560430FF 3E000000
	v_fmac_f32_e32 v3, 0x3e000000, v25                         // 000000000540: 560632FF 3E000000
	s_waitcnt vmcnt(3)                                         // 000000000548: BF8C3F73
	v_fmac_f32_e32 v0, 0x3e800000, v6                          // 00000000054C: 56000CFF 3E800000
	v_fmac_f32_e32 v1, 0x3e800000, v7                          // 000000000554: 56020EFF 3E800000
	v_fmac_f32_e32 v2, 0x3e800000, v8                          // 00000000055C: 560410FF 3E800000
	v_fmac_f32_e32 v3, 0x3e800000, v9                          // 000000000564: 560612FF 3E800000
	s_waitcnt vmcnt(2)                                         // 00000000056C: BF8C3F72
	v_fmac_f32_e32 v0, 0x3e000000, v26                         // 000000000570: 560034FF 3E000000
	v_fmac_f32_e32 v1, 0x3e000000, v27                         // 000000000578: 560236FF 3E000000
	v_fmac_f32_e32 v2, 0x3e000000, v28                         // 000000000580: 560438FF 3E000000
	v_fmac_f32_e32 v3, 0x3e000000, v29                         // 000000000588: 56063AFF 3E000000
	s_waitcnt vmcnt(1)                                         // 000000000590: BF8C3F71
	v_fmac_f32_e32 v0, 0x3d800000, v13                         // 000000000594: 56001AFF 3D800000
	v_fmac_f32_e32 v1, 0x3d800000, v14                         // 00000000059C: 56021CFF 3D800000
	v_fmac_f32_e32 v2, 0x3d800000, v15                         // 0000000005A4: 56041EFF 3D800000
	v_fmac_f32_e32 v3, 0x3d800000, v16                         // 0000000005AC: 560620FF 3D800000
	s_waitcnt vmcnt(0)                                         // 0000000005B4: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e000000, v17                         // 0000000005B8: 560022FF 3E000000
	v_fmac_f32_e32 v1, 0x3e000000, v18                         // 0000000005C0: 560224FF 3E000000
	v_fmac_f32_e32 v2, 0x3e000000, v19                         // 0000000005C8: 560426FF 3E000000
	v_fmac_f32_e32 v3, 0x3e000000, v20                         // 0000000005D0: 560628FF 3E000000
_L11:
	s_buffer_load_dword s0, s[8:11], 0x24                      // 0000000005D8: F4200004 FA000024
	v_add_f32_e32 v6, -0.5, v4                                 // 0000000005E0: 060C08F1
	v_add_f32_e32 v7, -0.5, v5                                 // 0000000005E4: 060E0AF1
	s_waitcnt lgkmcnt(0)                                       // 0000000005E8: BF8CC07F
	v_cmp_ngt_f32_e64 s2, s0, 0                                // 0000000005EC: D40B0002 00010000
	s_and_b64 vcc, exec, s[2:3]                                // 0000000005F4: 87EA027E
	s_cbranch_vccnz _L12                                       // 0000000005F8: BF87001C
	s_waitcnt vmcnt(0)                                         // 0000000005FC: BF8C3F70
	v_mul_f32_e32 v0, v7, v7                                   // 000000000600: 10000F07
	s_clause 0x1                                               // 000000000604: BFA10001
	s_load_dwordx8 s[20:27], s[18:19], null                    // 000000000608: F40C0509 FA000000
	s_load_dwordx4 s[4:7], s[18:19], 0x20                      // 000000000610: F4080109 FA000020
	v_fmac_f32_e32 v0, v6, v6                                  // 000000000618: 56000D06
	v_sqrt_f32_e32 v0, v0                                      // 00000000061C: 7E006700
	v_mul_f32_e32 v0, s0, v0                                   // 000000000620: 10000000
	v_mul_f32_e32 v1, v0, v6                                   // 000000000624: 10020D00
	v_mul_f32_e32 v2, v0, v7                                   // 000000000628: 10040F00
	v_fma_f32 v8, v0, v6, v4                                   // 00000000062C: D54B0008 04120D00
	v_fma_f32 v9, v0, v7, v5                                   // 000000000634: D54B0009 04160F00
	v_fma_f32 v1, 0.5, v1, v4                                  // 00000000063C: D54B0001 041202F0
	v_fma_f32 v2, 0.5, v2, v5                                  // 000000000644: D54B0002 041604F0
	s_waitcnt lgkmcnt(0)                                       // 00000000064C: BF8CC07F
	s_clause 0x2                                               // 000000000650: BFA10002
	image_sample v1, v[1:2], s[20:27], s[4:7] dmask:0x2 dim:SQ_RSRC_IMG_2D// 000000000654: F0800208 00250101
	image_sample v0, v[8:9], s[20:27], s[4:7] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000065C: F0800108 00250008
	image_sample v[2:3], v[4:5], s[20:27], s[4:7] dmask:0xc dim:SQ_RSRC_IMG_2D// 000000000664: F0800C08 00250204
_L12:
	s_clause 0x3                                               // 00000000066C: BFA10003
	s_buffer_load_dwordx2 s[2:3], s[8:11], 0x10                // 000000000670: F4240084 FA000010
	s_buffer_load_dword s1, s[8:11], 0x18                      // 000000000678: F4200044 FA000018
	s_buffer_load_dword s4, s[8:11], 0x20                      // 000000000680: F4200104 FA000020
	s_buffer_load_dword s0, s[8:11], 0x28                      // 000000000688: F4200004 FA000028
	v_mul_f32_e32 v7, v7, v7                                   // 000000000690: 100E0F07
	v_fmac_f32_e32 v7, v6, v6                                  // 000000000694: 560E0D06
	v_sqrt_f32_e64 v7, v7 mul:2                                // 000000000698: D5B30007 08000107
	s_waitcnt vmcnt(0) lgkmcnt(0)                              // 0000000006A0: BF8C0070
	v_fma_f32 v1, v1, s2, -0.5                                 // 0000000006A4: D54B0001 03C40501
	v_fma_f32 v2, v2, s2, -0.5                                 // 0000000006AC: D54B0002 03C40502
	v_fma_f32 v0, v0, s2, -0.5                                 // 0000000006B4: D54B0000 03C40500
	s_mov_b32 s2, 0x40c00000                                   // 0000000006BC: BE8203FF 40C00000
	v_fma_f32 v8, v1, s3, 0.5                                  // 0000000006C4: D54B0008 03C00701
	v_fma_f32 v9, v2, s3, 0.5                                  // 0000000006CC: D54B0009 03C00702
	v_mul_f32_e32 v1, s3, v1                                   // 0000000006D4: 10020203
	v_fma_f32 v0, v0, s3, 0.5                                  // 0000000006D8: D54B0000 03C00700
	v_cmp_lt_f32_e32 vcc_lo, v8, v9                            // 0000000006E0: 7C021308
	v_fma_f32 v11, -v2, s3, v1                                 // 0000000006E4: D54B000B 24040702
	v_fma_f32 v1, v2, s3, -v1                                  // 0000000006EC: D54B0001 84040702
	v_cndmask_b32_e64 v10, 1.0, 0, vcc_lo                      // 0000000006F4: D501000A 01A900F2
	v_fmac_f32_e32 v9, v10, v11                                // 0000000006FC: 5612170A
	v_fmac_f32_e32 v8, v10, v1                                 // 000000000700: 5610030A
	v_cmp_lt_f32_e32 vcc_lo, v0, v9                            // 000000000704: 7C021300
	v_sub_f32_e32 v11, v9, v0                                  // 000000000708: 08160109
	v_sub_f32_e32 v1, v0, v9                                   // 00000000070C: 08021300
	v_cndmask_b32_e64 v2, 1.0, 0, vcc_lo                       // 000000000710: D5010002 01A900F2
	v_fmac_f32_e32 v0, v2, v11                                 // 000000000718: 56001702
	v_fmac_f32_e32 v9, v2, v1                                  // 00000000071C: 56120302
	v_add_f32_e32 v11, -1.0, v10                               // 000000000720: 061614F3
	v_sub_f32_e32 v10, 0x3f2aaaab, v10                         // 000000000724: 081414FF 3F2AAAAB
	v_min_f32_e32 v1, v0, v8                                   // 00000000072C: 1E021100
	v_sub_f32_e32 v0, v0, v8                                   // 000000000730: 08001100
	v_sub_f32_e32 v6, v11, v10                                 // 000000000734: 080C150B
	v_sub_f32_e32 v1, v9, v1                                   // 000000000738: 08020309
	v_fmac_f32_e32 v10, v2, v6                                 // 00000000073C: 56140D02
	v_mov_b32_e32 v2, 0xc0400000                               // 000000000740: 7E0402FF C0400000
	v_fmaak_f32 v12, s2, v1, 0x2edbe6ff                        // 000000000748: 5A180202 2EDBE6FF
	v_mul_f32_e32 v1, s1, v1                                   // 000000000750: 10020201
	v_cmp_ngt_f32_e64 s2, s0, 0                                // 000000000754: D40B0002 00010000
	v_rcp_f32_e32 v11, v12                                     // 00000000075C: 7E16550C
	s_and_b64 vcc, exec, s[2:3]                                // 000000000760: 87EA027E
	v_fmac_f32_e32 v10, v0, v11                                // 000000000764: 56141700
	v_fmaak_f32 v0, s4, v7, 0xbf19999a                         // 000000000768: 5A000E04 BF19999A
	v_add_f32_e64 v6, |v10|, 1.0                               // 000000000770: D5030106 0001E50A
	v_add_f32_e64 v7, 0x3f2aaaab, |v10|                        // 000000000778: D5030207 000214FF 3F2AAAAB
	v_add_f32_e64 v8, 0x3eaaaaab, |v10|                        // 000000000784: D5030208 000214FF 3EAAAAAB
	v_add_f32_e32 v10, 0x2edbe6ff, v9                          // 000000000790: 061412FF 2EDBE6FF
	v_max_f32_e64 v0, v0, v0 clamp                             // 000000000798: D5108000 00020100
	v_fract_f32_e32 v6, v6                                     // 0000000007A0: 7E0C4106
	v_fract_f32_e32 v7, v7                                     // 0000000007A4: 7E0E4107
	v_fract_f32_e32 v8, v8                                     // 0000000007A8: 7E104108
	v_rcp_f32_e32 v10, v10                                     // 0000000007AC: 7E14550A
	v_mul_f32_e32 v11, v0, v0                                  // 0000000007B0: 10160100
	v_fmamk_f32 v6, v6, 0x40c00000, v2                         // 0000000007B4: 580C0506 40C00000
	v_fmamk_f32 v7, v7, 0x40c00000, v2                         // 0000000007BC: 580E0507 40C00000
	v_fmac_f32_e32 v2, 0x40c00000, v8                          // 0000000007C4: 560410FF 40C00000
	v_fmaak_f32 v0, 2.0, v0, 0xc0400000                        // 0000000007CC: 5A0000F4 C0400000
	v_add_f32_e64 v6, |v6|, -1.0 clamp                         // 0000000007D4: D5038106 0001E706
	v_add_f32_e64 v7, |v7|, -1.0 clamp                         // 0000000007DC: D5038107 0001E707
	v_add_f32_e64 v2, |v2|, -1.0 clamp                         // 0000000007E4: D5038102 0001E702
	v_fma_f32 v0, v11, v0, 1.0                                 // 0000000007EC: D54B0000 03CA010B
	v_mul_f32_e32 v1, v1, v10                                  // 0000000007F4: 10021501
	v_add_f32_e32 v6, -1.0, v6                                 // 0000000007F8: 060C0CF3
	v_add_f32_e32 v7, -1.0, v7                                 // 0000000007FC: 060E0EF3
	v_add_f32_e32 v2, -1.0, v2                                 // 000000000800: 060404F3
	v_mul_f32_e32 v0, v9, v0                                   // 000000000804: 10000109
	v_fma_f32 v6, v6, v1, 1.0                                  // 000000000808: D54B0006 03CA0306
	v_fma_f32 v7, v7, v1, 1.0                                  // 000000000810: D54B0007 03CA0307
	v_fma_f32 v8, v2, v1, 1.0                                  // 000000000818: D54B0008 03CA0302
	v_mul_f32_e32 v2, v6, v0                                   // 000000000820: 10040106
	v_mul_f32_e32 v1, v7, v0                                   // 000000000824: 10020107
	v_mul_f32_e32 v0, v8, v0                                   // 000000000828: 10000108
	s_cbranch_vccnz _L13                                       // 00000000082C: BF870018
	s_buffer_load_dword s1, s[8:11], 0xc                       // 000000000830: F4200044 FA00000C
	s_clause 0x1                                               // 000000000838: BFA10001
	s_load_dwordx8 s[20:27], s[18:19], 0x80                    // 00000000083C: F40C0509 FA000080
	s_load_dwordx4 s[4:7], s[18:19], 0x20                      // 000000000844: F4080109 FA000020
	s_waitcnt lgkmcnt(0)                                       // 00000000084C: BF8CC07F
	v_mul_f32_e64 v7, 0x3dcccccd, s1                           // 000000000850: D5080007 000002FF 3DCCCCCD
	v_fmamk_f32 v6, v4, 0x41200000, v7                         // 00000000085C: 580C0F04 41200000
	v_fmac_f32_e32 v7, 0x41200000, v5                          // 000000000864: 560E0AFF 41200000
	s_and_b64 exec, exec, s[16:17]                             // 00000000086C: 87FE107E
	image_sample v4, v[6:7], s[20:27], s[4:7] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000870: F0800108 00250406
	s_waitcnt vmcnt(0)                                         // 000000000878: BF8C3F70
	v_add_f32_e32 v4, -0.5, v4                                 // 00000000087C: 060808F1
	v_fmac_f32_e32 v2, s0, v4                                  // 000000000880: 56040800
	v_fmac_f32_e32 v1, s0, v4                                  // 000000000884: 56020800
	v_fmac_f32_e32 v0, s0, v4                                  // 000000000888: 56000800
	s_branch _L14                                              // 00000000088C: BF820001
_L13:
	s_and_b64 exec, exec, s[16:17]                             // 000000000890: 87FE107E
_L14:
	s_buffer_load_dword s0, s[8:11], 0x1c                      // 000000000894: F4200004 FA00001C
	v_log_f32_e64 v2, |v2|                                     // 00000000089C: D5A70102 00000102
	v_log_f32_e64 v1, |v1|                                     // 0000000008A4: D5A70101 00000101
	v_log_f32_e64 v0, |v0|                                     // 0000000008AC: D5A70100 00000100
	v_max_f32_e64 v3, v3, v3 clamp                             // 0000000008B4: D5108003 00020703
	s_waitcnt lgkmcnt(0)                                       // 0000000008BC: BF8CC07F
	v_mul_legacy_f32_e32 v2, s0, v2                            // 0000000008C0: 0E040400
	v_mul_legacy_f32_e32 v1, s0, v1                            // 0000000008C4: 0E020200
	v_mul_legacy_f32_e32 v0, s0, v0                            // 0000000008C8: 0E000000
	v_exp_f32_e64 v2, v2 clamp                                 // 0000000008CC: D5A58002 00000102
	v_exp_f32_e64 v1, v1 clamp                                 // 0000000008D4: D5A58001 00000101
	v_exp_f32_e64 v0, v0 clamp                                 // 0000000008DC: D5A58000 00000100
	exp mrt0 v2, v1, v0, v3 done vm                            // 0000000008E4: F800180F 03000102
	s_endpgm                                                   // 0000000008EC: BF810000

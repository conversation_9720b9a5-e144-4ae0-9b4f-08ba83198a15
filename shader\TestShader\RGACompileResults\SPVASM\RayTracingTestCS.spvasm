; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 668
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_RayTracingParams "type.RayTracingParams"
               OpMemberName %type_RayTracingParams 0 "InverseViewMatrix"
               OpMemberName %type_RayTracingParams 1 "InverseProjectionMatrix"
               OpMemberName %type_RayTracingParams 2 "CameraPosition"
               OpMemberName %type_RayTracingParams 3 "Time"
               OpMemberName %type_RayTracingParams 4 "FrameCount"
               OpMemberName %type_RayTracingParams 5 "MaxBounces"
               OpMemberName %type_RayTracingParams 6 "SamplesPerPixel"
               OpMemberName %type_RayTracingParams 7 "RandomSeed"
               OpMemberName %type_RayTracingParams 8 "ScreenResolution"
               OpMemberName %type_RayTracingParams 9 "JitterOffset"
               OpMemberName %type_RayTracingParams 10 "NumSpheres"
               OpMemberName %type_RayTracingParams 11 "NumTriangles"
               OpMemberName %type_RayTracingParams 12 "NumMaterials"
               OpMemberName %type_RayTracingParams 13 "_padding"
               OpName %RayTracingParams "RayTracingParams"
               OpName %type_StructuredBuffer_Sphere "type.StructuredBuffer.Sphere"
               OpName %Sphere "Sphere"
               OpMemberName %Sphere 0 "Center"
               OpMemberName %Sphere 1 "Radius"
               OpMemberName %Sphere 2 "MaterialID"
               OpMemberName %Sphere 3 "_padding"
               OpName %Spheres "Spheres"
               OpName %type_StructuredBuffer_Triangle "type.StructuredBuffer.Triangle"
               OpName %Triangle "Triangle"
               OpMemberName %Triangle 0 "V0"
               OpMemberName %Triangle 1 "V1"
               OpMemberName %Triangle 2 "V2"
               OpMemberName %Triangle 3 "N0"
               OpMemberName %Triangle 4 "N1"
               OpMemberName %Triangle 5 "N2"
               OpMemberName %Triangle 6 "UV0"
               OpMemberName %Triangle 7 "UV1"
               OpMemberName %Triangle 8 "UV2"
               OpMemberName %Triangle 9 "MaterialID"
               OpMemberName %Triangle 10 "_padding"
               OpName %Triangles "Triangles"
               OpName %type_StructuredBuffer_Material "type.StructuredBuffer.Material"
               OpName %Material "Material"
               OpMemberName %Material 0 "Albedo"
               OpMemberName %Material 1 "Metallic"
               OpMemberName %Material 2 "Emission"
               OpMemberName %Material 3 "Roughness"
               OpMemberName %Material 4 "IOR"
               OpMemberName %Material 5 "Transparency"
               OpMemberName %Material 6 "_padding"
               OpName %Materials "Materials"
               OpName %type_2d_image "type.2d.image"
               OpName %OutputTexture "OutputTexture"
               OpName %AccumulationTexture "AccumulationTexture"
               OpName %type_2d_image_0 "type.2d.image"
               OpName %EnvironmentMap "EnvironmentMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %RayTracingParams DescriptorSet 0
               OpDecorate %RayTracingParams Binding 0
               OpDecorate %Spheres DescriptorSet 0
               OpDecorate %Spheres Binding 0
               OpDecorate %Triangles DescriptorSet 0
               OpDecorate %Triangles Binding 1
               OpDecorate %Materials DescriptorSet 0
               OpDecorate %Materials Binding 2
               OpDecorate %OutputTexture DescriptorSet 0
               OpDecorate %OutputTexture Binding 0
               OpDecorate %AccumulationTexture DescriptorSet 0
               OpDecorate %AccumulationTexture Binding 1
               OpDecorate %EnvironmentMap DescriptorSet 0
               OpDecorate %EnvironmentMap Binding 4
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_RayTracingParams 0 Offset 0
               OpMemberDecorate %type_RayTracingParams 0 MatrixStride 16
               OpMemberDecorate %type_RayTracingParams 0 RowMajor
               OpMemberDecorate %type_RayTracingParams 1 Offset 64
               OpMemberDecorate %type_RayTracingParams 1 MatrixStride 16
               OpMemberDecorate %type_RayTracingParams 1 RowMajor
               OpMemberDecorate %type_RayTracingParams 2 Offset 128
               OpMemberDecorate %type_RayTracingParams 3 Offset 140
               OpMemberDecorate %type_RayTracingParams 4 Offset 144
               OpMemberDecorate %type_RayTracingParams 5 Offset 148
               OpMemberDecorate %type_RayTracingParams 6 Offset 152
               OpMemberDecorate %type_RayTracingParams 7 Offset 156
               OpMemberDecorate %type_RayTracingParams 8 Offset 160
               OpMemberDecorate %type_RayTracingParams 9 Offset 168
               OpMemberDecorate %type_RayTracingParams 10 Offset 176
               OpMemberDecorate %type_RayTracingParams 11 Offset 180
               OpMemberDecorate %type_RayTracingParams 12 Offset 184
               OpMemberDecorate %type_RayTracingParams 13 Offset 188
               OpDecorate %type_RayTracingParams Block
               OpMemberDecorate %Sphere 0 Offset 0
               OpMemberDecorate %Sphere 1 Offset 12
               OpMemberDecorate %Sphere 2 Offset 16
               OpMemberDecorate %Sphere 3 Offset 20
               OpDecorate %_runtimearr_Sphere ArrayStride 32
               OpMemberDecorate %type_StructuredBuffer_Sphere 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Sphere 0 NonWritable
               OpDecorate %type_StructuredBuffer_Sphere BufferBlock
               OpMemberDecorate %Triangle 0 Offset 0
               OpMemberDecorate %Triangle 1 Offset 16
               OpMemberDecorate %Triangle 2 Offset 32
               OpMemberDecorate %Triangle 3 Offset 48
               OpMemberDecorate %Triangle 4 Offset 64
               OpMemberDecorate %Triangle 5 Offset 80
               OpMemberDecorate %Triangle 6 Offset 96
               OpMemberDecorate %Triangle 7 Offset 104
               OpMemberDecorate %Triangle 8 Offset 112
               OpMemberDecorate %Triangle 9 Offset 120
               OpMemberDecorate %Triangle 10 Offset 128
               OpDecorate %_runtimearr_Triangle ArrayStride 144
               OpMemberDecorate %type_StructuredBuffer_Triangle 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Triangle 0 NonWritable
               OpDecorate %type_StructuredBuffer_Triangle BufferBlock
               OpMemberDecorate %Material 0 Offset 0
               OpMemberDecorate %Material 1 Offset 12
               OpMemberDecorate %Material 2 Offset 16
               OpMemberDecorate %Material 3 Offset 28
               OpMemberDecorate %Material 4 Offset 32
               OpMemberDecorate %Material 5 Offset 36
               OpMemberDecorate %Material 6 Offset 40
               OpDecorate %_runtimearr_Material ArrayStride 48
               OpMemberDecorate %type_StructuredBuffer_Material 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Material 0 NonWritable
               OpDecorate %type_StructuredBuffer_Material BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_8 = OpConstant %int 8
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
      %int_1 = OpConstant %int 1
      %int_7 = OpConstant %int 7
      %int_4 = OpConstant %int 4
       %uint = OpTypeInt 32 0
%uint_719393 = OpConstant %uint 719393
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %39 = OpConstantComposite %v3float %float_0 %float_0 %float_0
     %uint_0 = OpConstant %uint 0
      %int_6 = OpConstant %int 6
  %float_0_5 = OpConstant %float 0.5
    %v2float = OpTypeVector %float 2
         %44 = OpConstantComposite %v2float %float_0_5 %float_0_5
     %uint_1 = OpConstant %uint 1
    %float_1 = OpConstant %float 1
         %47 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%float_0_454545468 = OpConstant %float 0.454545468
         %49 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
    %float_2 = OpConstant %float 2
         %51 = OpConstantComposite %v2float %float_1 %float_1
      %int_2 = OpConstant %int 2
%float_0_00100000005 = OpConstant %float 0.00100000005
 %float_1000 = OpConstant %float 1000
      %int_5 = OpConstant %int 5
%float_0_100000001 = OpConstant %float 0.100000001
      %false = OpConstantFalse %bool
     %uint_3 = OpConstant %uint 3
     %int_10 = OpConstant %int 10
     %int_11 = OpConstant %int 11
%float_3_14159274 = OpConstant %float 3.14159274
%float_0_0399999991 = OpConstant %float 0.0399999991
         %63 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
    %float_5 = OpConstant %float 5
    %uint_61 = OpConstant %uint 61
    %uint_16 = OpConstant %uint 16
     %uint_9 = OpConstant %uint 9
     %uint_4 = OpConstant %uint 4
%uint_668265261 = OpConstant %uint 668265261
    %uint_15 = OpConstant %uint 15
    %float_4 = OpConstant %float 4
%float_n9_99999975en06 = OpConstant %float -9.99999975e-06
%float_9_99999975en06 = OpConstant %float 9.99999975e-06
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_RayTracingParams = OpTypeStruct %mat4v4float %mat4v4float %v3float %float %uint %uint %uint %uint %v2float %v2float %uint %uint %uint %uint
%_ptr_Uniform_type_RayTracingParams = OpTypePointer Uniform %type_RayTracingParams
     %Sphere = OpTypeStruct %v3float %float %uint %float
%_runtimearr_Sphere = OpTypeRuntimeArray %Sphere
%type_StructuredBuffer_Sphere = OpTypeStruct %_runtimearr_Sphere
%_ptr_Uniform_type_StructuredBuffer_Sphere = OpTypePointer Uniform %type_StructuredBuffer_Sphere
   %Triangle = OpTypeStruct %v3float %v3float %v3float %v3float %v3float %v3float %v2float %v2float %v2float %uint %v3float
%_runtimearr_Triangle = OpTypeRuntimeArray %Triangle
%type_StructuredBuffer_Triangle = OpTypeStruct %_runtimearr_Triangle
%_ptr_Uniform_type_StructuredBuffer_Triangle = OpTypePointer Uniform %type_StructuredBuffer_Triangle
   %Material = OpTypeStruct %v3float %float %v3float %float %float %float %v2float
%_runtimearr_Material = OpTypeRuntimeArray %Material
%type_StructuredBuffer_Material = OpTypeStruct %_runtimearr_Material
%_ptr_Uniform_type_StructuredBuffer_Material = OpTypePointer Uniform %type_StructuredBuffer_Material
     %v2uint = OpTypeVector %uint 2
%type_2d_image = OpTypeImage %float 2D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_2d_image_0 = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_0 = OpTypePointer UniformConstant %type_2d_image_0
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %87 = OpTypeFunction %void
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
     %uint_2 = OpConstant %uint 2
   %uint_264 = OpConstant %uint 264
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_Material = OpTypePointer Uniform %Material
%_ptr_Uniform_Sphere = OpTypePointer Uniform %Sphere
%_ptr_Uniform_Triangle = OpTypePointer Uniform %Triangle
%type_sampled_image = OpTypeSampledImage %type_2d_image_0
%RayTracingParams = OpVariable %_ptr_Uniform_type_RayTracingParams Uniform
    %Spheres = OpVariable %_ptr_Uniform_type_StructuredBuffer_Sphere Uniform
  %Triangles = OpVariable %_ptr_Uniform_type_StructuredBuffer_Triangle Uniform
  %Materials = OpVariable %_ptr_Uniform_type_StructuredBuffer_Material Uniform
%OutputTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%AccumulationTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EnvironmentMap = OpVariable %_ptr_UniformConstant_type_2d_image_0 UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
         %98 = OpUndef %uint
         %99 = OpUndef %v3float
        %100 = OpUndef %float
%float_2_32830644en10 = OpConstant %float 2.32830644e-10
%float_0_318309873 = OpConstant %float 0.318309873
        %103 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
        %104 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
%float_0_159154937 = OpConstant %float 0.159154937
   %float_n2 = OpConstant %float -2
     %v3bool = OpTypeVector %bool 3
     %uint_5 = OpConstant %uint 5
       %main = OpFunction %void None %87
        %109 = OpLabel
        %110 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %111 None
               OpSwitch %uint_0 %112
        %112 = OpLabel
        %113 = OpCompositeExtract %uint %110 0
        %114 = OpAccessChain %_ptr_Uniform_v2float %RayTracingParams %int_8
        %115 = OpAccessChain %_ptr_Uniform_float %RayTracingParams %int_8 %int_0
        %116 = OpLoad %float %115
        %117 = OpConvertFToU %uint %116
        %118 = OpUGreaterThanEqual %bool %113 %117
        %119 = OpLogicalNot %bool %118
               OpSelectionMerge %120 None
               OpBranchConditional %119 %121 %120
        %121 = OpLabel
        %122 = OpCompositeExtract %uint %110 1
        %123 = OpAccessChain %_ptr_Uniform_float %RayTracingParams %int_8 %int_1
        %124 = OpLoad %float %123
        %125 = OpConvertFToU %uint %124
        %126 = OpUGreaterThanEqual %bool %122 %125
               OpBranch %120
        %120 = OpLabel
        %127 = OpPhi %bool %true %112 %126 %121
               OpSelectionMerge %128 None
               OpBranchConditional %127 %129 %128
        %129 = OpLabel
               OpBranch %111
        %128 = OpLabel
        %130 = OpCompositeExtract %uint %110 1
        %131 = OpIMul %uint %130 %117
        %132 = OpIAdd %uint %131 %113
        %133 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_7
        %134 = OpLoad %uint %133
        %135 = OpIAdd %uint %134 %132
        %136 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_4
        %137 = OpLoad %uint %136
        %138 = OpIMul %uint %137 %uint_719393
        %139 = OpIAdd %uint %135 %138
               OpBranch %140
        %140 = OpLabel
        %141 = OpPhi %v3float %99 %128 %142 %143
        %144 = OpPhi %v3float %99 %128 %145 %143
        %146 = OpPhi %uint %98 %128 %147 %143
        %148 = OpPhi %uint %139 %128 %149 %143
        %150 = OpPhi %v3float %39 %128 %151 %143
        %152 = OpPhi %uint %uint_0 %128 %153 %143
        %154 = OpPhi %v3float %99 %128 %155 %143
        %156 = OpPhi %v3float %99 %128 %157 %143
        %158 = OpPhi %uint %98 %128 %159 %143
        %160 = OpPhi %float %100 %128 %161 %143
        %162 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_6
        %163 = OpLoad %uint %162
        %164 = OpULessThan %bool %152 %163
               OpLoopMerge %165 %143 None
               OpBranchConditional %164 %166 %165
        %166 = OpLabel
        %167 = OpBitwiseXor %uint %148 %uint_61
        %168 = OpShiftRightLogical %uint %148 %uint_16
        %169 = OpBitwiseXor %uint %167 %168
        %170 = OpIMul %uint %169 %uint_9
        %171 = OpShiftRightLogical %uint %170 %uint_4
        %172 = OpBitwiseXor %uint %170 %171
        %173 = OpIMul %uint %172 %uint_668265261
        %174 = OpShiftRightLogical %uint %173 %uint_15
        %175 = OpBitwiseXor %uint %173 %174
        %176 = OpConvertUToF %float %175
        %177 = OpFMul %float %176 %float_2_32830644en10
        %178 = OpBitwiseXor %uint %175 %uint_61
        %179 = OpShiftRightLogical %uint %175 %uint_16
        %180 = OpBitwiseXor %uint %178 %179
        %181 = OpIMul %uint %180 %uint_9
        %182 = OpShiftRightLogical %uint %181 %uint_4
        %183 = OpBitwiseXor %uint %181 %182
        %184 = OpIMul %uint %183 %uint_668265261
        %185 = OpShiftRightLogical %uint %184 %uint_15
        %186 = OpBitwiseXor %uint %184 %185
        %187 = OpConvertUToF %float %186
        %188 = OpFMul %float %187 %float_2_32830644en10
        %189 = OpCompositeConstruct %v2float %177 %188
        %190 = OpFSub %v2float %189 %44
        %191 = OpVectorShuffle %v2uint %110 %110 0 1
        %192 = OpConvertUToF %v2float %191
        %193 = OpFAdd %v2float %192 %190
        %194 = OpLoad %v2float %114
        %195 = OpFDiv %v2float %193 %194
        %196 = OpVectorTimesScalar %v2float %195 %float_2
        %197 = OpFSub %v2float %196 %51
        %198 = OpCompositeExtract %float %197 1
        %199 = OpFNegate %float %198
        %200 = OpCompositeExtract %float %197 0
        %201 = OpCompositeConstruct %v4float %200 %199 %float_1 %float_1
        %202 = OpAccessChain %_ptr_Uniform_mat4v4float %RayTracingParams %int_1
        %203 = OpLoad %mat4v4float %202
        %204 = OpMatrixTimesVector %v4float %203 %201
        %205 = OpCompositeExtract %float %204 3
        %206 = OpCompositeConstruct %v4float %205 %205 %205 %205
        %207 = OpFDiv %v4float %204 %206
        %208 = OpCompositeExtract %float %207 0
        %209 = OpCompositeExtract %float %207 1
        %210 = OpCompositeExtract %float %207 2
        %211 = OpCompositeConstruct %v4float %208 %209 %210 %float_1
        %212 = OpAccessChain %_ptr_Uniform_mat4v4float %RayTracingParams %int_0
        %213 = OpLoad %mat4v4float %212
        %214 = OpMatrixTimesVector %v4float %213 %211
        %215 = OpVectorShuffle %v3float %214 %214 0 1 2
        %216 = OpAccessChain %_ptr_Uniform_v3float %RayTracingParams %int_2
        %217 = OpLoad %v3float %216
        %218 = OpFSub %v3float %215 %217
        %219 = OpExtInst %v3float %1 Normalize %218
               OpBranch %220
        %220 = OpLabel
        %221 = OpPhi %v3float %141 %166 %222 %223
        %224 = OpPhi %uint %186 %166 %225 %223
        %226 = OpPhi %v3float %144 %166 %227 %223
        %228 = OpPhi %v3float %39 %166 %229 %223
        %230 = OpPhi %v3float %47 %166 %231 %223
        %232 = OpPhi %uint %146 %166 %233 %223
        %234 = OpPhi %v3float %217 %166 %235 %223
        %236 = OpPhi %v3float %219 %166 %237 %223
        %238 = OpPhi %uint %uint_0 %166 %239 %223
        %240 = OpPhi %v3float %154 %166 %241 %223
        %242 = OpPhi %v3float %156 %166 %243 %223
        %244 = OpPhi %uint %158 %166 %245 %223
        %246 = OpPhi %float %160 %166 %247 %223
        %248 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_5
        %249 = OpLoad %uint %248
        %250 = OpULessThan %bool %238 %249
               OpLoopMerge %251 %223 None
               OpBranchConditional %250 %252 %251
        %252 = OpLabel
               OpBranch %253
        %253 = OpLabel
        %254 = OpPhi %v3float %240 %252 %255 %256
        %257 = OpPhi %v3float %242 %252 %258 %256
        %259 = OpPhi %uint %244 %252 %260 %256
        %261 = OpPhi %float %float_1000 %252 %262 %256
        %263 = OpPhi %float %246 %252 %264 %256
        %265 = OpPhi %float %float_1000 %252 %266 %256
        %267 = OpPhi %v3float %221 %252 %268 %256
        %269 = OpPhi %v3float %226 %252 %270 %256
        %271 = OpPhi %uint %232 %252 %272 %256
        %273 = OpPhi %bool %false %252 %274 %256
        %275 = OpPhi %uint %uint_0 %252 %276 %256
        %277 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_10
        %278 = OpLoad %uint %277
        %279 = OpULessThan %bool %275 %278
               OpLoopMerge %280 %256 None
               OpBranchConditional %279 %281 %280
        %281 = OpLabel
        %282 = OpAccessChain %_ptr_Uniform_Sphere %Spheres %int_0 %275
        %283 = OpAccessChain %_ptr_Uniform_v3float %282 %uint_0
        %284 = OpLoad %v3float %283
        %285 = OpAccessChain %_ptr_Uniform_float %282 %uint_1
        %286 = OpLoad %float %285
        %287 = OpAccessChain %_ptr_Uniform_uint %282 %uint_2
        %288 = OpLoad %uint %287
               OpSelectionMerge %289 None
               OpSwitch %uint_0 %290
        %290 = OpLabel
        %291 = OpFSub %v3float %234 %284
        %292 = OpDot %float %236 %236
        %293 = OpDot %float %291 %236
        %294 = OpFMul %float %float_2 %293
        %295 = OpDot %float %291 %291
        %296 = OpFMul %float %286 %286
        %297 = OpFSub %float %295 %296
        %298 = OpFMul %float %294 %294
        %299 = OpFMul %float %float_4 %292
        %300 = OpFMul %float %299 %297
        %301 = OpFSub %float %298 %300
        %302 = OpFOrdLessThan %bool %301 %float_0
               OpSelectionMerge %303 None
               OpBranchConditional %302 %304 %303
        %304 = OpLabel
               OpBranch %289
        %303 = OpLabel
        %305 = OpExtInst %float %1 Sqrt %301
        %306 = OpFMul %float %293 %float_n2
        %307 = OpFSub %float %306 %305
        %308 = OpFMul %float %float_2 %292
        %309 = OpFDiv %float %307 %308
        %310 = OpFAdd %float %306 %305
        %311 = OpFDiv %float %310 %308
        %312 = OpFOrdGreaterThan %bool %309 %float_0_00100000005
               OpSelectionMerge %313 None
               OpBranchConditional %312 %314 %313
        %314 = OpLabel
        %315 = OpFOrdLessThan %bool %309 %265
               OpBranch %313
        %313 = OpLabel
        %316 = OpPhi %bool %false %303 %315 %314
        %317 = OpSelect %float %316 %309 %311
        %318 = OpFOrdLessThan %bool %317 %float_0_00100000005
        %319 = OpLogicalNot %bool %318
               OpSelectionMerge %320 None
               OpBranchConditional %319 %321 %320
        %321 = OpLabel
        %322 = OpFOrdGreaterThan %bool %317 %265
               OpBranch %320
        %320 = OpLabel
        %323 = OpPhi %bool %true %313 %322 %321
               OpSelectionMerge %324 None
               OpBranchConditional %323 %325 %324
        %325 = OpLabel
               OpBranch %289
        %324 = OpLabel
        %326 = OpVectorTimesScalar %v3float %236 %317
        %327 = OpFAdd %v3float %234 %326
        %328 = OpFSub %v3float %327 %284
        %329 = OpExtInst %v3float %1 Normalize %328
               OpBranch %289
        %289 = OpLabel
        %330 = OpPhi %bool %false %304 %false %325 %true %324
        %255 = OpPhi %v3float %254 %304 %254 %325 %327 %324
        %258 = OpPhi %v3float %257 %304 %257 %325 %329 %324
        %260 = OpPhi %uint %259 %304 %259 %325 %288 %324
        %264 = OpPhi %float %263 %304 %263 %325 %317 %324
               OpSelectionMerge %331 None
               OpBranchConditional %330 %332 %331
        %332 = OpLabel
        %333 = OpFOrdLessThan %bool %264 %261
        %334 = OpSelect %float %333 %264 %261
        %335 = OpSelect %float %333 %264 %265
        %336 = OpCompositeConstruct %v3bool %333 %333 %333
        %337 = OpSelect %v3float %336 %255 %267
        %338 = OpSelect %v3float %336 %258 %269
        %339 = OpSelect %uint %333 %260 %271
        %340 = OpSelect %bool %333 %330 %273
               OpBranch %331
        %331 = OpLabel
        %262 = OpPhi %float %261 %289 %334 %332
        %266 = OpPhi %float %265 %289 %335 %332
        %268 = OpPhi %v3float %267 %289 %337 %332
        %270 = OpPhi %v3float %269 %289 %338 %332
        %272 = OpPhi %uint %271 %289 %339 %332
        %274 = OpPhi %bool %273 %289 %340 %332
               OpBranch %256
        %256 = OpLabel
        %276 = OpIAdd %uint %275 %uint_1
               OpBranch %253
        %280 = OpLabel
               OpBranch %341
        %341 = OpLabel
        %241 = OpPhi %v3float %254 %280 %342 %343
        %243 = OpPhi %v3float %257 %280 %344 %343
        %245 = OpPhi %uint %259 %280 %345 %343
        %346 = OpPhi %float %261 %280 %347 %343
        %247 = OpPhi %float %263 %280 %348 %343
        %349 = OpPhi %float %265 %280 %350 %343
        %222 = OpPhi %v3float %267 %280 %351 %343
        %227 = OpPhi %v3float %269 %280 %352 %343
        %233 = OpPhi %uint %271 %280 %353 %343
        %354 = OpPhi %bool %273 %280 %355 %343
        %356 = OpPhi %uint %uint_0 %280 %357 %343
        %358 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_11
        %359 = OpLoad %uint %358
        %360 = OpULessThan %bool %356 %359
               OpLoopMerge %361 %343 None
               OpBranchConditional %360 %362 %361
        %362 = OpLabel
        %363 = OpAccessChain %_ptr_Uniform_Triangle %Triangles %int_0 %356
        %364 = OpAccessChain %_ptr_Uniform_v3float %363 %uint_0
        %365 = OpLoad %v3float %364
        %366 = OpAccessChain %_ptr_Uniform_v3float %363 %uint_1
        %367 = OpLoad %v3float %366
        %368 = OpAccessChain %_ptr_Uniform_v3float %363 %uint_2
        %369 = OpLoad %v3float %368
        %370 = OpAccessChain %_ptr_Uniform_v3float %363 %uint_3
        %371 = OpLoad %v3float %370
        %372 = OpAccessChain %_ptr_Uniform_v3float %363 %uint_4
        %373 = OpLoad %v3float %372
        %374 = OpAccessChain %_ptr_Uniform_v3float %363 %uint_5
        %375 = OpLoad %v3float %374
        %376 = OpAccessChain %_ptr_Uniform_uint %363 %uint_9
        %377 = OpLoad %uint %376
               OpSelectionMerge %378 None
               OpSwitch %uint_0 %379
        %379 = OpLabel
        %380 = OpFSub %v3float %367 %365
        %381 = OpFSub %v3float %369 %365
        %382 = OpExtInst %v3float %1 Cross %236 %381
        %383 = OpDot %float %380 %382
        %384 = OpFOrdGreaterThan %bool %383 %float_n9_99999975en06
               OpSelectionMerge %385 None
               OpBranchConditional %384 %386 %385
        %386 = OpLabel
        %387 = OpFOrdLessThan %bool %383 %float_9_99999975en06
               OpBranch %385
        %385 = OpLabel
        %388 = OpPhi %bool %false %379 %387 %386
               OpSelectionMerge %389 None
               OpBranchConditional %388 %390 %389
        %390 = OpLabel
               OpBranch %378
        %389 = OpLabel
        %391 = OpFDiv %float %float_1 %383
        %392 = OpFSub %v3float %234 %365
        %393 = OpDot %float %392 %382
        %394 = OpFMul %float %391 %393
        %395 = OpFOrdLessThan %bool %394 %float_0
        %396 = OpLogicalNot %bool %395
               OpSelectionMerge %397 None
               OpBranchConditional %396 %398 %397
        %398 = OpLabel
        %399 = OpFOrdGreaterThan %bool %394 %float_1
               OpBranch %397
        %397 = OpLabel
        %400 = OpPhi %bool %true %389 %399 %398
               OpSelectionMerge %401 None
               OpBranchConditional %400 %402 %401
        %402 = OpLabel
               OpBranch %378
        %401 = OpLabel
        %403 = OpExtInst %v3float %1 Cross %392 %380
        %404 = OpDot %float %236 %403
        %405 = OpFMul %float %391 %404
        %406 = OpFOrdLessThan %bool %405 %float_0
        %407 = OpLogicalNot %bool %406
               OpSelectionMerge %408 None
               OpBranchConditional %407 %409 %408
        %409 = OpLabel
        %410 = OpFAdd %float %394 %405
        %411 = OpFOrdGreaterThan %bool %410 %float_1
               OpBranch %408
        %408 = OpLabel
        %412 = OpPhi %bool %true %401 %411 %409
               OpSelectionMerge %413 None
               OpBranchConditional %412 %414 %413
        %414 = OpLabel
               OpBranch %378
        %413 = OpLabel
        %415 = OpDot %float %381 %403
        %416 = OpFMul %float %391 %415
        %417 = OpFOrdLessThan %bool %416 %float_0_00100000005
        %418 = OpLogicalNot %bool %417
               OpSelectionMerge %419 None
               OpBranchConditional %418 %420 %419
        %420 = OpLabel
        %421 = OpFOrdGreaterThan %bool %416 %349
               OpBranch %419
        %419 = OpLabel
        %422 = OpPhi %bool %true %413 %421 %420
               OpSelectionMerge %423 None
               OpBranchConditional %422 %424 %423
        %424 = OpLabel
               OpBranch %378
        %423 = OpLabel
        %425 = OpVectorTimesScalar %v3float %236 %416
        %426 = OpFAdd %v3float %234 %425
        %427 = OpFSub %float %float_1 %394
        %428 = OpFSub %float %427 %405
        %429 = OpVectorTimesScalar %v3float %371 %428
        %430 = OpVectorTimesScalar %v3float %373 %394
        %431 = OpFAdd %v3float %429 %430
        %432 = OpVectorTimesScalar %v3float %375 %405
        %433 = OpFAdd %v3float %431 %432
        %434 = OpExtInst %v3float %1 Normalize %433
               OpBranch %378
        %378 = OpLabel
        %435 = OpPhi %bool %false %390 %false %402 %false %414 %false %424 %true %423
        %342 = OpPhi %v3float %241 %390 %241 %402 %241 %414 %241 %424 %426 %423
        %344 = OpPhi %v3float %243 %390 %243 %402 %243 %414 %243 %424 %434 %423
        %345 = OpPhi %uint %245 %390 %245 %402 %245 %414 %245 %424 %377 %423
        %348 = OpPhi %float %247 %390 %247 %402 %247 %414 %247 %424 %416 %423
               OpSelectionMerge %436 None
               OpBranchConditional %435 %437 %436
        %437 = OpLabel
        %438 = OpFOrdLessThan %bool %348 %346
        %439 = OpSelect %float %438 %348 %346
        %440 = OpSelect %float %438 %348 %349
        %441 = OpCompositeConstruct %v3bool %438 %438 %438
        %442 = OpSelect %v3float %441 %342 %222
        %443 = OpSelect %v3float %441 %344 %227
        %444 = OpSelect %uint %438 %345 %233
        %445 = OpSelect %bool %438 %435 %354
               OpBranch %436
        %436 = OpLabel
        %347 = OpPhi %float %346 %378 %439 %437
        %350 = OpPhi %float %349 %378 %440 %437
        %351 = OpPhi %v3float %222 %378 %442 %437
        %352 = OpPhi %v3float %227 %378 %443 %437
        %353 = OpPhi %uint %233 %378 %444 %437
        %355 = OpPhi %bool %354 %378 %445 %437
               OpBranch %343
        %343 = OpLabel
        %357 = OpIAdd %uint %356 %uint_1
               OpBranch %341
        %361 = OpLabel
        %446 = OpLogicalNot %bool %354
               OpSelectionMerge %447 None
               OpBranchConditional %446 %448 %447
        %448 = OpLabel
        %449 = OpCompositeExtract %float %236 2
        %450 = OpCompositeExtract %float %236 0
        %451 = OpExtInst %float %1 Atan2 %449 %450
        %452 = OpCompositeExtract %float %236 1
        %453 = OpExtInst %float %1 Acos %452
        %454 = OpFAdd %float %451 %float_3_14159274
        %455 = OpFMul %float %454 %float_0_159154937
        %456 = OpFMul %float %453 %float_0_318309873
        %457 = OpCompositeConstruct %v2float %455 %456
        %458 = OpLoad %type_2d_image_0 %EnvironmentMap
        %459 = OpLoad %type_sampler %LinearSampler
        %460 = OpSampledImage %type_sampled_image %458 %459
        %461 = OpImageSampleExplicitLod %v4float %460 %457 Lod %float_0
        %462 = OpVectorShuffle %v3float %461 %461 0 1 2
        %463 = OpFMul %v3float %230 %462
        %464 = OpFAdd %v3float %228 %463
               OpBranch %251
        %447 = OpLabel
        %465 = OpAccessChain %_ptr_Uniform_Material %Materials %int_0 %233
        %466 = OpAccessChain %_ptr_Uniform_v3float %465 %uint_0
        %467 = OpLoad %v3float %466
        %468 = OpAccessChain %_ptr_Uniform_float %465 %uint_1
        %469 = OpLoad %float %468
        %470 = OpAccessChain %_ptr_Uniform_v3float %465 %uint_2
        %471 = OpLoad %v3float %470
        %472 = OpAccessChain %_ptr_Uniform_float %465 %uint_3
        %473 = OpLoad %float %472
        %474 = OpFMul %v3float %230 %471
        %229 = OpFAdd %v3float %228 %474
        %475 = OpFNegate %v3float %236
               OpSelectionMerge %476 None
               OpSwitch %uint_0 %477
        %477 = OpLabel
        %478 = OpBitwiseXor %uint %224 %uint_61
        %479 = OpShiftRightLogical %uint %224 %uint_16
        %480 = OpBitwiseXor %uint %478 %479
        %481 = OpIMul %uint %480 %uint_9
        %482 = OpShiftRightLogical %uint %481 %uint_4
        %483 = OpBitwiseXor %uint %481 %482
        %484 = OpIMul %uint %483 %uint_668265261
        %485 = OpShiftRightLogical %uint %484 %uint_15
        %486 = OpBitwiseXor %uint %484 %485
        %487 = OpConvertUToF %float %486
        %488 = OpFMul %float %487 %float_2_32830644en10
        %489 = OpFOrdLessThan %bool %488 %float_0_5
               OpSelectionMerge %490 None
               OpBranchConditional %489 %491 %492
        %492 = OpLabel
               OpBranch %493
        %493 = OpLabel
        %494 = OpPhi %uint %486 %492 %495 %493
        %496 = OpBitwiseXor %uint %494 %uint_61
        %497 = OpShiftRightLogical %uint %494 %uint_16
        %498 = OpBitwiseXor %uint %496 %497
        %499 = OpIMul %uint %498 %uint_9
        %500 = OpShiftRightLogical %uint %499 %uint_4
        %501 = OpBitwiseXor %uint %499 %500
        %502 = OpIMul %uint %501 %uint_668265261
        %503 = OpShiftRightLogical %uint %502 %uint_15
        %504 = OpBitwiseXor %uint %502 %503
        %505 = OpConvertUToF %float %504
        %506 = OpFMul %float %505 %float_2_32830644en10
        %507 = OpBitwiseXor %uint %504 %uint_61
        %508 = OpShiftRightLogical %uint %504 %uint_16
        %509 = OpBitwiseXor %uint %507 %508
        %510 = OpIMul %uint %509 %uint_9
        %511 = OpShiftRightLogical %uint %510 %uint_4
        %512 = OpBitwiseXor %uint %510 %511
        %513 = OpIMul %uint %512 %uint_668265261
        %514 = OpShiftRightLogical %uint %513 %uint_15
        %515 = OpBitwiseXor %uint %513 %514
        %516 = OpConvertUToF %float %515
        %517 = OpFMul %float %516 %float_2_32830644en10
        %518 = OpBitwiseXor %uint %515 %uint_61
        %519 = OpShiftRightLogical %uint %515 %uint_16
        %520 = OpBitwiseXor %uint %518 %519
        %521 = OpIMul %uint %520 %uint_9
        %522 = OpShiftRightLogical %uint %521 %uint_4
        %523 = OpBitwiseXor %uint %521 %522
        %524 = OpIMul %uint %523 %uint_668265261
        %525 = OpShiftRightLogical %uint %524 %uint_15
        %495 = OpBitwiseXor %uint %524 %525
        %526 = OpConvertUToF %float %495
        %527 = OpFMul %float %526 %float_2_32830644en10
        %528 = OpCompositeConstruct %v3float %506 %517 %527
        %529 = OpVectorTimesScalar %v3float %528 %float_2
        %530 = OpFSub %v3float %529 %47
        %531 = OpDot %float %530 %530
        %532 = OpFOrdGreaterThanEqual %bool %531 %float_1
               OpLoopMerge %533 %493 None
               OpBranchConditional %532 %493 %533
        %533 = OpLabel
        %534 = OpExtInst %v3float %1 Normalize %530
        %535 = OpDot %float %534 %227
        %536 = OpFOrdGreaterThan %bool %535 %float_0
               OpSelectionMerge %537 None
               OpBranchConditional %536 %538 %539
        %539 = OpLabel
        %540 = OpFNegate %v3float %534
               OpBranch %537
        %538 = OpLabel
               OpBranch %537
        %537 = OpLabel
        %541 = OpPhi %v3float %540 %539 %534 %538
        %542 = OpFSub %float %float_1 %469
        %543 = OpVectorTimesScalar %v3float %467 %542
        %544 = OpFMul %v3float %543 %103
               OpBranch %476
        %491 = OpLabel
        %545 = OpExtInst %v3float %1 Reflect %236 %227
               OpBranch %546
        %546 = OpLabel
        %547 = OpPhi %uint %486 %491 %548 %546
        %549 = OpBitwiseXor %uint %547 %uint_61
        %550 = OpShiftRightLogical %uint %547 %uint_16
        %551 = OpBitwiseXor %uint %549 %550
        %552 = OpIMul %uint %551 %uint_9
        %553 = OpShiftRightLogical %uint %552 %uint_4
        %554 = OpBitwiseXor %uint %552 %553
        %555 = OpIMul %uint %554 %uint_668265261
        %556 = OpShiftRightLogical %uint %555 %uint_15
        %557 = OpBitwiseXor %uint %555 %556
        %558 = OpConvertUToF %float %557
        %559 = OpFMul %float %558 %float_2_32830644en10
        %560 = OpBitwiseXor %uint %557 %uint_61
        %561 = OpShiftRightLogical %uint %557 %uint_16
        %562 = OpBitwiseXor %uint %560 %561
        %563 = OpIMul %uint %562 %uint_9
        %564 = OpShiftRightLogical %uint %563 %uint_4
        %565 = OpBitwiseXor %uint %563 %564
        %566 = OpIMul %uint %565 %uint_668265261
        %567 = OpShiftRightLogical %uint %566 %uint_15
        %568 = OpBitwiseXor %uint %566 %567
        %569 = OpConvertUToF %float %568
        %570 = OpFMul %float %569 %float_2_32830644en10
        %571 = OpBitwiseXor %uint %568 %uint_61
        %572 = OpShiftRightLogical %uint %568 %uint_16
        %573 = OpBitwiseXor %uint %571 %572
        %574 = OpIMul %uint %573 %uint_9
        %575 = OpShiftRightLogical %uint %574 %uint_4
        %576 = OpBitwiseXor %uint %574 %575
        %577 = OpIMul %uint %576 %uint_668265261
        %578 = OpShiftRightLogical %uint %577 %uint_15
        %548 = OpBitwiseXor %uint %577 %578
        %579 = OpConvertUToF %float %548
        %580 = OpFMul %float %579 %float_2_32830644en10
        %581 = OpCompositeConstruct %v3float %559 %570 %580
        %582 = OpVectorTimesScalar %v3float %581 %float_2
        %583 = OpFSub %v3float %582 %47
        %584 = OpDot %float %583 %583
        %585 = OpFOrdGreaterThanEqual %bool %584 %float_1
               OpLoopMerge %586 %546 None
               OpBranchConditional %585 %546 %586
        %586 = OpLabel
        %587 = OpExtInst %v3float %1 Normalize %583
        %588 = OpVectorTimesScalar %v3float %587 %473
        %589 = OpFAdd %v3float %545 %588
        %590 = OpExtInst %v3float %1 Normalize %589
        %591 = OpDot %float %590 %227
        %592 = OpFOrdLessThanEqual %bool %591 %float_0
               OpSelectionMerge %593 None
               OpBranchConditional %592 %594 %593
        %594 = OpLabel
        %595 = OpExtInst %v3float %1 Reflect %590 %227
               OpBranch %593
        %593 = OpLabel
        %596 = OpPhi %v3float %590 %586 %595 %594
        %597 = OpDot %float %227 %475
        %598 = OpExtInst %float %1 NMax %597 %float_0
        %599 = OpCompositeConstruct %v3float %469 %469 %469
        %600 = OpExtInst %v3float %1 FMix %63 %467 %599
        %601 = OpFSub %v3float %47 %600
        %602 = OpFSub %float %float_1 %598
        %603 = OpExtInst %float %1 Pow %602 %float_5
        %604 = OpVectorTimesScalar %v3float %601 %603
        %605 = OpFAdd %v3float %600 %604
               OpBranch %476
        %490 = OpLabel
               OpUnreachable
        %476 = OpLabel
        %606 = OpPhi %uint %495 %537 %548 %593
        %237 = OpPhi %v3float %541 %537 %596 %593
        %607 = OpPhi %v3float %544 %537 %605 %593
        %608 = OpDot %float %227 %237
        %609 = OpExtInst %float %1 NMax %608 %float_0
        %610 = OpVectorTimesScalar %v3float %607 %609
        %611 = OpFMul %v3float %610 %104
        %612 = OpFMul %v3float %230 %611
        %613 = OpCompositeExtract %float %612 0
        %614 = OpCompositeExtract %float %612 1
        %615 = OpExtInst %float %1 NMax %613 %614
        %616 = OpCompositeExtract %float %612 2
        %617 = OpExtInst %float %1 NMax %615 %616
        %618 = OpFOrdLessThan %bool %617 %float_0_100000001
               OpSelectionMerge %619 None
               OpBranchConditional %618 %620 %619
        %620 = OpLabel
        %621 = OpUGreaterThan %bool %238 %uint_3
               OpBranch %619
        %619 = OpLabel
        %622 = OpPhi %bool %false %476 %621 %620
               OpSelectionMerge %623 None
               OpBranchConditional %622 %624 %623
        %624 = OpLabel
        %625 = OpBitwiseXor %uint %606 %uint_61
        %626 = OpShiftRightLogical %uint %606 %uint_16
        %627 = OpBitwiseXor %uint %625 %626
        %628 = OpIMul %uint %627 %uint_9
        %629 = OpShiftRightLogical %uint %628 %uint_4
        %630 = OpBitwiseXor %uint %628 %629
        %631 = OpIMul %uint %630 %uint_668265261
        %632 = OpShiftRightLogical %uint %631 %uint_15
        %633 = OpBitwiseXor %uint %631 %632
        %634 = OpConvertUToF %float %633
        %635 = OpFMul %float %634 %float_2_32830644en10
        %636 = OpFOrdGreaterThan %bool %635 %617
               OpSelectionMerge %637 None
               OpBranchConditional %636 %638 %637
        %638 = OpLabel
               OpBranch %251
        %637 = OpLabel
        %639 = OpCompositeConstruct %v3float %617 %617 %617
        %640 = OpFDiv %v3float %612 %639
               OpBranch %623
        %623 = OpLabel
        %225 = OpPhi %uint %606 %619 %633 %637
        %231 = OpPhi %v3float %612 %619 %640 %637
        %641 = OpVectorTimesScalar %v3float %227 %float_0_00100000005
        %235 = OpFAdd %v3float %222 %641
               OpBranch %223
        %223 = OpLabel
        %239 = OpIAdd %uint %238 %uint_1
               OpBranch %220
        %251 = OpLabel
        %142 = OpPhi %v3float %221 %220 %222 %448 %222 %638
        %145 = OpPhi %v3float %226 %220 %227 %448 %227 %638
        %147 = OpPhi %uint %232 %220 %233 %448 %233 %638
        %149 = OpPhi %uint %224 %220 %224 %448 %633 %638
        %155 = OpPhi %v3float %240 %220 %241 %448 %241 %638
        %157 = OpPhi %v3float %242 %220 %243 %448 %243 %638
        %159 = OpPhi %uint %244 %220 %245 %448 %245 %638
        %161 = OpPhi %float %246 %220 %247 %448 %247 %638
        %642 = OpPhi %v3float %228 %220 %464 %448 %229 %638
        %151 = OpFAdd %v3float %150 %642
               OpBranch %143
        %143 = OpLabel
        %153 = OpIAdd %uint %152 %uint_1
               OpBranch %140
        %165 = OpLabel
        %643 = OpConvertUToF %float %163
        %644 = OpCompositeConstruct %v3float %643 %643 %643
        %645 = OpFDiv %v3float %150 %644
               OpControlBarrier %uint_2 %uint_2 %uint_264
        %646 = OpVectorShuffle %v2uint %110 %110 0 1
        %647 = OpLoad %type_2d_image %AccumulationTexture
        %648 = OpImageRead %v4float %647 %646 None
        %649 = OpVectorShuffle %v3float %648 %648 0 1 2
        %650 = OpCompositeExtract %float %648 3
        %651 = OpFAdd %float %650 %float_1
        %652 = OpFDiv %float %float_1 %651
        %653 = OpCompositeConstruct %v3float %652 %652 %652
        %654 = OpExtInst %v3float %1 FMix %649 %645 %653
        %655 = OpFAdd %v3float %654 %47
        %656 = OpFDiv %v3float %654 %655
        %657 = OpExtInst %v3float %1 Pow %656 %49
        %658 = OpCompositeExtract %float %657 0
        %659 = OpCompositeExtract %float %657 1
        %660 = OpCompositeExtract %float %657 2
        %661 = OpCompositeConstruct %v4float %658 %659 %660 %float_1
        %662 = OpLoad %type_2d_image %OutputTexture
               OpImageWrite %662 %646 %661 None
        %663 = OpCompositeExtract %float %654 0
        %664 = OpCompositeExtract %float %654 1
        %665 = OpCompositeExtract %float %654 2
        %666 = OpCompositeConstruct %v4float %663 %664 %665 %651
        %667 = OpLoad %type_2d_image %AccumulationTexture
               OpImageWrite %667 %646 %666 None
               OpBranch %111
        %111 = OpLabel
               OpReturn
               OpFunctionEnd

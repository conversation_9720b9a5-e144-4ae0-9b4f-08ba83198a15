; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 364
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TANGENT %in_var_BITANGENT %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_COLOR0 %in_var_BLENDWEIGHT %in_var_BLENDINDICES %gl_InstanceIndex %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6 %out_var_TEXCOORD7 %out_var_TEXCOORD8 %out_var_TEXCOORD9 %out_var_TEXCOORD10 %out_var_TEXCOORD11
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "DeltaTime"
               OpMemberName %type_PerFrame 6 "FrameCount"
               OpMemberName %type_PerFrame 7 "ScreenResolution"
               OpName %PerFrame "PerFrame"
               OpName %type_PerObject "type.PerObject"
               OpMemberName %type_PerObject 0 "WorldMatrix"
               OpMemberName %type_PerObject 1 "NormalMatrix"
               OpMemberName %type_PerObject 2 "PrevWorldMatrix"
               OpMemberName %type_PerObject 3 "MaterialIndex"
               OpMemberName %type_PerObject 4 "BoundingBoxMin"
               OpMemberName %type_PerObject 5 "BoundingBoxMax"
               OpMemberName %type_PerObject 6 "LODLevel"
               OpName %PerObject "PerObject"
               OpName %type_StructuredBuffer_BoneTransform "type.StructuredBuffer.BoneTransform"
               OpName %BoneTransform "BoneTransform"
               OpMemberName %BoneTransform 0 "Transform"
               OpMemberName %BoneTransform 1 "InverseBindPose"
               OpName %BoneTransforms "BoneTransforms"
               OpName %type_StructuredBuffer_v4float "type.StructuredBuffer.v4float"
               OpName %InstanceTransforms "InstanceTransforms"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TANGENT "in.var.TANGENT"
               OpName %in_var_BITANGENT "in.var.BITANGENT"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %in_var_BLENDWEIGHT "in.var.BLENDWEIGHT"
               OpName %in_var_BLENDINDICES "in.var.BLENDINDICES"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %out_var_TEXCOORD7 "out.var.TEXCOORD7"
               OpName %out_var_TEXCOORD8 "out.var.TEXCOORD8"
               OpName %out_var_TEXCOORD9 "out.var.TEXCOORD9"
               OpName %out_var_TEXCOORD10 "out.var.TEXCOORD10"
               OpName %out_var_TEXCOORD11 "out.var.TEXCOORD11"
               OpName %main "main"
               OpDecorate %gl_InstanceIndex BuiltIn InstanceIndex
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %out_var_TEXCOORD10 Flat
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TANGENT Location 2
               OpDecorate %in_var_BITANGENT Location 3
               OpDecorate %in_var_TEXCOORD0 Location 4
               OpDecorate %in_var_TEXCOORD1 Location 5
               OpDecorate %in_var_COLOR0 Location 6
               OpDecorate %in_var_BLENDWEIGHT Location 7
               OpDecorate %in_var_BLENDINDICES Location 8
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %out_var_TEXCOORD7 Location 7
               OpDecorate %out_var_TEXCOORD8 Location 8
               OpDecorate %out_var_TEXCOORD9 Location 9
               OpDecorate %out_var_TEXCOORD10 Location 10
               OpDecorate %out_var_TEXCOORD11 Location 11
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %PerObject DescriptorSet 0
               OpDecorate %PerObject Binding 1
               OpDecorate %BoneTransforms DescriptorSet 0
               OpDecorate %BoneTransforms Binding 1
               OpDecorate %InstanceTransforms DescriptorSet 0
               OpDecorate %InstanceTransforms Binding 3
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 212
               OpMemberDecorate %type_PerFrame 7 Offset 216
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %type_PerObject 0 Offset 0
               OpMemberDecorate %type_PerObject 0 MatrixStride 16
               OpMemberDecorate %type_PerObject 0 RowMajor
               OpMemberDecorate %type_PerObject 1 Offset 64
               OpMemberDecorate %type_PerObject 1 MatrixStride 16
               OpMemberDecorate %type_PerObject 1 RowMajor
               OpMemberDecorate %type_PerObject 2 Offset 128
               OpMemberDecorate %type_PerObject 2 MatrixStride 16
               OpMemberDecorate %type_PerObject 2 RowMajor
               OpMemberDecorate %type_PerObject 3 Offset 192
               OpMemberDecorate %type_PerObject 4 Offset 196
               OpMemberDecorate %type_PerObject 5 Offset 208
               OpMemberDecorate %type_PerObject 6 Offset 220
               OpDecorate %type_PerObject Block
               OpMemberDecorate %BoneTransform 0 Offset 0
               OpMemberDecorate %BoneTransform 0 MatrixStride 16
               OpMemberDecorate %BoneTransform 0 RowMajor
               OpMemberDecorate %BoneTransform 1 Offset 64
               OpMemberDecorate %BoneTransform 1 MatrixStride 16
               OpMemberDecorate %BoneTransform 1 RowMajor
               OpDecorate %_runtimearr_BoneTransform ArrayStride 128
               OpMemberDecorate %type_StructuredBuffer_BoneTransform 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_BoneTransform 0 NonWritable
               OpDecorate %type_StructuredBuffer_BoneTransform BufferBlock
               OpDecorate %_runtimearr_v4float ArrayStride 16
               OpMemberDecorate %type_StructuredBuffer_v4float 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_v4float 0 NonWritable
               OpDecorate %type_StructuredBuffer_v4float BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
       %uint = OpTypeInt 32 0
     %uint_4 = OpConstant %uint 4
     %uint_1 = OpConstant %uint 1
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %v4float = OpTypeVector %float 4
         %49 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
    %float_0 = OpConstant %float 0
      %int_3 = OpConstant %int 3
         %52 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
%mat4v4float = OpTypeMatrix %v4float 4
         %54 = OpConstantComposite %mat4v4float %52 %52 %52 %52
    %v3float = OpTypeVector %float 3
         %56 = OpConstantComposite %v3float %float_0 %float_0 %float_0
  %float_100 = OpConstant %float 100
    %v2float = OpTypeVector %float 2
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %float %uint %v2float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%type_PerObject = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %uint %v3float %v3float %float
%_ptr_Uniform_type_PerObject = OpTypePointer Uniform %type_PerObject
%BoneTransform = OpTypeStruct %mat4v4float %mat4v4float
%_runtimearr_BoneTransform = OpTypeRuntimeArray %BoneTransform
%type_StructuredBuffer_BoneTransform = OpTypeStruct %_runtimearr_BoneTransform
%_ptr_Uniform_type_StructuredBuffer_BoneTransform = OpTypePointer Uniform %type_StructuredBuffer_BoneTransform
%_runtimearr_v4float = OpTypeRuntimeArray %v4float
%type_StructuredBuffer_v4float = OpTypeStruct %_runtimearr_v4float
%_ptr_Uniform_type_StructuredBuffer_v4float = OpTypePointer Uniform %type_StructuredBuffer_v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
     %v4uint = OpTypeVector %uint 4
%_ptr_Input_v4uint = OpTypePointer Input %v4uint
%_ptr_Input_uint = OpTypePointer Input %uint
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_uint = OpTypePointer Output %uint
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %75 = OpTypeFunction %void
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
       %bool = OpTypeBool
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
  %PerObject = OpVariable %_ptr_Uniform_type_PerObject Uniform
%BoneTransforms = OpVariable %_ptr_Uniform_type_StructuredBuffer_BoneTransform Uniform
%InstanceTransforms = OpVariable %_ptr_Uniform_type_StructuredBuffer_v4float Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_BITANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%in_var_BLENDWEIGHT = OpVariable %_ptr_Input_v4float Input
%in_var_BLENDINDICES = OpVariable %_ptr_Input_v4uint Input
%gl_InstanceIndex = OpVariable %_ptr_Input_uint Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD7 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD8 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD9 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD10 = OpVariable %_ptr_Output_uint Output
%out_var_TEXCOORD11 = OpVariable %_ptr_Output_float Output
%float_0_0199999996 = OpConstant %float 0.0199999996
       %main = OpFunction %void None %75
         %83 = OpLabel
         %84 = OpLoad %v3float %in_var_POSITION
         %85 = OpLoad %v3float %in_var_NORMAL
         %86 = OpLoad %v3float %in_var_TANGENT
         %87 = OpLoad %v3float %in_var_BITANGENT
         %88 = OpLoad %v2float %in_var_TEXCOORD0
         %89 = OpLoad %v2float %in_var_TEXCOORD1
         %90 = OpLoad %v4float %in_var_COLOR0
         %91 = OpLoad %v4float %in_var_BLENDWEIGHT
         %92 = OpLoad %v4uint %in_var_BLENDINDICES
         %93 = OpLoad %uint %gl_InstanceIndex
         %94 = OpIMul %uint %93 %uint_4
         %95 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %94
         %96 = OpLoad %v4float %95
         %97 = OpIAdd %uint %94 %uint_1
         %98 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %97
         %99 = OpLoad %v4float %98
        %100 = OpIAdd %uint %94 %uint_2
        %101 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %100
        %102 = OpLoad %v4float %101
        %103 = OpIAdd %uint %94 %uint_3
        %104 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %103
        %105 = OpLoad %v4float %104
        %106 = OpCompositeConstruct %mat4v4float %96 %99 %102 %105
        %107 = OpDot %float %91 %49
        %108 = OpFOrdGreaterThan %bool %107 %float_0
               OpSelectionMerge %109 None
               OpBranchConditional %108 %110 %109
        %110 = OpLabel
        %111 = OpCompositeConstruct %v4float %107 %107 %107 %107
        %112 = OpFDiv %v4float %91 %111
        %113 = OpCompositeExtract %float %112 0
        %114 = OpFOrdGreaterThan %bool %113 %float_0
               OpSelectionMerge %115 None
               OpBranchConditional %114 %116 %115
        %116 = OpLabel
        %117 = OpCompositeExtract %uint %92 0
        %118 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %117 %int_0
        %119 = OpLoad %mat4v4float %118
        %120 = OpMatrixTimesScalar %mat4v4float %119 %113
               OpBranch %115
        %115 = OpLabel
        %121 = OpPhi %mat4v4float %54 %110 %120 %116
        %122 = OpCompositeExtract %float %112 1
        %123 = OpFOrdGreaterThan %bool %122 %float_0
               OpSelectionMerge %124 None
               OpBranchConditional %123 %125 %124
        %125 = OpLabel
        %126 = OpCompositeExtract %uint %92 1
        %127 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %126 %int_0
        %128 = OpLoad %mat4v4float %127
        %129 = OpMatrixTimesScalar %mat4v4float %128 %122
        %130 = OpCompositeExtract %v4float %121 0
        %131 = OpCompositeExtract %v4float %129 0
        %132 = OpFAdd %v4float %130 %131
        %133 = OpCompositeExtract %v4float %121 1
        %134 = OpCompositeExtract %v4float %129 1
        %135 = OpFAdd %v4float %133 %134
        %136 = OpCompositeExtract %v4float %121 2
        %137 = OpCompositeExtract %v4float %129 2
        %138 = OpFAdd %v4float %136 %137
        %139 = OpCompositeExtract %v4float %121 3
        %140 = OpCompositeExtract %v4float %129 3
        %141 = OpFAdd %v4float %139 %140
        %142 = OpCompositeConstruct %mat4v4float %132 %135 %138 %141
               OpBranch %124
        %124 = OpLabel
        %143 = OpPhi %mat4v4float %121 %115 %142 %125
        %144 = OpCompositeExtract %float %112 2
        %145 = OpFOrdGreaterThan %bool %144 %float_0
               OpSelectionMerge %146 None
               OpBranchConditional %145 %147 %146
        %147 = OpLabel
        %148 = OpCompositeExtract %uint %92 2
        %149 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %148 %int_0
        %150 = OpLoad %mat4v4float %149
        %151 = OpMatrixTimesScalar %mat4v4float %150 %144
        %152 = OpCompositeExtract %v4float %143 0
        %153 = OpCompositeExtract %v4float %151 0
        %154 = OpFAdd %v4float %152 %153
        %155 = OpCompositeExtract %v4float %143 1
        %156 = OpCompositeExtract %v4float %151 1
        %157 = OpFAdd %v4float %155 %156
        %158 = OpCompositeExtract %v4float %143 2
        %159 = OpCompositeExtract %v4float %151 2
        %160 = OpFAdd %v4float %158 %159
        %161 = OpCompositeExtract %v4float %143 3
        %162 = OpCompositeExtract %v4float %151 3
        %163 = OpFAdd %v4float %161 %162
        %164 = OpCompositeConstruct %mat4v4float %154 %157 %160 %163
               OpBranch %146
        %146 = OpLabel
        %165 = OpPhi %mat4v4float %143 %124 %164 %147
        %166 = OpCompositeExtract %float %112 3
        %167 = OpFOrdGreaterThan %bool %166 %float_0
               OpSelectionMerge %168 None
               OpBranchConditional %167 %169 %168
        %169 = OpLabel
        %170 = OpCompositeExtract %uint %92 3
        %171 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %170 %int_0
        %172 = OpLoad %mat4v4float %171
        %173 = OpMatrixTimesScalar %mat4v4float %172 %166
        %174 = OpCompositeExtract %v4float %165 0
        %175 = OpCompositeExtract %v4float %173 0
        %176 = OpFAdd %v4float %174 %175
        %177 = OpCompositeExtract %v4float %165 1
        %178 = OpCompositeExtract %v4float %173 1
        %179 = OpFAdd %v4float %177 %178
        %180 = OpCompositeExtract %v4float %165 2
        %181 = OpCompositeExtract %v4float %173 2
        %182 = OpFAdd %v4float %180 %181
        %183 = OpCompositeExtract %v4float %165 3
        %184 = OpCompositeExtract %v4float %173 3
        %185 = OpFAdd %v4float %183 %184
        %186 = OpCompositeConstruct %mat4v4float %176 %179 %182 %185
               OpBranch %168
        %168 = OpLabel
        %187 = OpPhi %mat4v4float %165 %146 %186 %169
        %188 = OpCompositeExtract %float %84 0
        %189 = OpCompositeExtract %float %84 1
        %190 = OpCompositeExtract %float %84 2
        %191 = OpCompositeConstruct %v4float %188 %189 %190 %float_1
        %192 = OpMatrixTimesVector %v4float %187 %191
        %193 = OpVectorShuffle %v3float %192 %192 0 1 2
               OpSelectionMerge %194 None
               OpBranchConditional %114 %195 %194
        %195 = OpLabel
        %196 = OpCompositeExtract %uint %92 0
        %197 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %196 %int_0
        %198 = OpLoad %mat4v4float %197
        %199 = OpCompositeExtract %v4float %198 0
        %200 = OpVectorShuffle %v3float %199 %199 0 1 2
        %201 = OpCompositeExtract %v4float %198 1
        %202 = OpVectorShuffle %v3float %201 %201 0 1 2
        %203 = OpCompositeExtract %v4float %198 2
        %204 = OpVectorShuffle %v3float %203 %203 0 1 2
        %205 = OpCompositeConstruct %mat3v3float %200 %202 %204
        %206 = OpMatrixTimesVector %v3float %205 %85
        %207 = OpVectorTimesScalar %v3float %206 %113
               OpBranch %194
        %194 = OpLabel
        %208 = OpPhi %v3float %56 %168 %207 %195
               OpSelectionMerge %209 None
               OpBranchConditional %123 %210 %209
        %210 = OpLabel
        %211 = OpCompositeExtract %uint %92 1
        %212 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %211 %int_0
        %213 = OpLoad %mat4v4float %212
        %214 = OpCompositeExtract %v4float %213 0
        %215 = OpVectorShuffle %v3float %214 %214 0 1 2
        %216 = OpCompositeExtract %v4float %213 1
        %217 = OpVectorShuffle %v3float %216 %216 0 1 2
        %218 = OpCompositeExtract %v4float %213 2
        %219 = OpVectorShuffle %v3float %218 %218 0 1 2
        %220 = OpCompositeConstruct %mat3v3float %215 %217 %219
        %221 = OpMatrixTimesVector %v3float %220 %85
        %222 = OpVectorTimesScalar %v3float %221 %122
        %223 = OpFAdd %v3float %208 %222
               OpBranch %209
        %209 = OpLabel
        %224 = OpPhi %v3float %208 %194 %223 %210
               OpSelectionMerge %225 None
               OpBranchConditional %145 %226 %225
        %226 = OpLabel
        %227 = OpCompositeExtract %uint %92 2
        %228 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %227 %int_0
        %229 = OpLoad %mat4v4float %228
        %230 = OpCompositeExtract %v4float %229 0
        %231 = OpVectorShuffle %v3float %230 %230 0 1 2
        %232 = OpCompositeExtract %v4float %229 1
        %233 = OpVectorShuffle %v3float %232 %232 0 1 2
        %234 = OpCompositeExtract %v4float %229 2
        %235 = OpVectorShuffle %v3float %234 %234 0 1 2
        %236 = OpCompositeConstruct %mat3v3float %231 %233 %235
        %237 = OpMatrixTimesVector %v3float %236 %85
        %238 = OpVectorTimesScalar %v3float %237 %144
        %239 = OpFAdd %v3float %224 %238
               OpBranch %225
        %225 = OpLabel
        %240 = OpPhi %v3float %224 %209 %239 %226
               OpSelectionMerge %241 None
               OpBranchConditional %167 %242 %241
        %242 = OpLabel
        %243 = OpCompositeExtract %uint %92 3
        %244 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %243 %int_0
        %245 = OpLoad %mat4v4float %244
        %246 = OpCompositeExtract %v4float %245 0
        %247 = OpVectorShuffle %v3float %246 %246 0 1 2
        %248 = OpCompositeExtract %v4float %245 1
        %249 = OpVectorShuffle %v3float %248 %248 0 1 2
        %250 = OpCompositeExtract %v4float %245 2
        %251 = OpVectorShuffle %v3float %250 %250 0 1 2
        %252 = OpCompositeConstruct %mat3v3float %247 %249 %251
        %253 = OpMatrixTimesVector %v3float %252 %85
        %254 = OpVectorTimesScalar %v3float %253 %166
        %255 = OpFAdd %v3float %240 %254
               OpBranch %241
        %241 = OpLabel
        %256 = OpPhi %v3float %240 %225 %255 %242
               OpSelectionMerge %257 None
               OpBranchConditional %114 %258 %257
        %258 = OpLabel
        %259 = OpCompositeExtract %uint %92 0
        %260 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %259 %int_0
        %261 = OpLoad %mat4v4float %260
        %262 = OpCompositeExtract %v4float %261 0
        %263 = OpVectorShuffle %v3float %262 %262 0 1 2
        %264 = OpCompositeExtract %v4float %261 1
        %265 = OpVectorShuffle %v3float %264 %264 0 1 2
        %266 = OpCompositeExtract %v4float %261 2
        %267 = OpVectorShuffle %v3float %266 %266 0 1 2
        %268 = OpCompositeConstruct %mat3v3float %263 %265 %267
        %269 = OpMatrixTimesVector %v3float %268 %86
        %270 = OpVectorTimesScalar %v3float %269 %113
               OpBranch %257
        %257 = OpLabel
        %271 = OpPhi %v3float %56 %241 %270 %258
               OpSelectionMerge %272 None
               OpBranchConditional %123 %273 %272
        %273 = OpLabel
        %274 = OpCompositeExtract %uint %92 1
        %275 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %274 %int_0
        %276 = OpLoad %mat4v4float %275
        %277 = OpCompositeExtract %v4float %276 0
        %278 = OpVectorShuffle %v3float %277 %277 0 1 2
        %279 = OpCompositeExtract %v4float %276 1
        %280 = OpVectorShuffle %v3float %279 %279 0 1 2
        %281 = OpCompositeExtract %v4float %276 2
        %282 = OpVectorShuffle %v3float %281 %281 0 1 2
        %283 = OpCompositeConstruct %mat3v3float %278 %280 %282
        %284 = OpMatrixTimesVector %v3float %283 %86
        %285 = OpVectorTimesScalar %v3float %284 %122
        %286 = OpFAdd %v3float %271 %285
               OpBranch %272
        %272 = OpLabel
        %287 = OpPhi %v3float %271 %257 %286 %273
               OpSelectionMerge %288 None
               OpBranchConditional %145 %289 %288
        %289 = OpLabel
        %290 = OpCompositeExtract %uint %92 2
        %291 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %290 %int_0
        %292 = OpLoad %mat4v4float %291
        %293 = OpCompositeExtract %v4float %292 0
        %294 = OpVectorShuffle %v3float %293 %293 0 1 2
        %295 = OpCompositeExtract %v4float %292 1
        %296 = OpVectorShuffle %v3float %295 %295 0 1 2
        %297 = OpCompositeExtract %v4float %292 2
        %298 = OpVectorShuffle %v3float %297 %297 0 1 2
        %299 = OpCompositeConstruct %mat3v3float %294 %296 %298
        %300 = OpMatrixTimesVector %v3float %299 %86
        %301 = OpVectorTimesScalar %v3float %300 %144
        %302 = OpFAdd %v3float %287 %301
               OpBranch %288
        %288 = OpLabel
        %303 = OpPhi %v3float %287 %272 %302 %289
               OpSelectionMerge %304 None
               OpBranchConditional %167 %305 %304
        %305 = OpLabel
        %306 = OpCompositeExtract %uint %92 3
        %307 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %306 %int_0
        %308 = OpLoad %mat4v4float %307
        %309 = OpCompositeExtract %v4float %308 0
        %310 = OpVectorShuffle %v3float %309 %309 0 1 2
        %311 = OpCompositeExtract %v4float %308 1
        %312 = OpVectorShuffle %v3float %311 %311 0 1 2
        %313 = OpCompositeExtract %v4float %308 2
        %314 = OpVectorShuffle %v3float %313 %313 0 1 2
        %315 = OpCompositeConstruct %mat3v3float %310 %312 %314
        %316 = OpMatrixTimesVector %v3float %315 %86
        %317 = OpVectorTimesScalar %v3float %316 %166
        %318 = OpFAdd %v3float %303 %317
               OpBranch %304
        %304 = OpLabel
        %319 = OpPhi %v3float %303 %288 %318 %305
               OpBranch %109
        %109 = OpLabel
        %320 = OpPhi %v3float %86 %83 %319 %304
        %321 = OpPhi %v3float %85 %83 %256 %304
        %322 = OpPhi %v3float %84 %83 %193 %304
        %323 = OpCompositeExtract %float %322 0
        %324 = OpCompositeExtract %float %322 1
        %325 = OpCompositeExtract %float %322 2
        %326 = OpCompositeConstruct %v4float %323 %324 %325 %float_1
        %327 = OpMatrixTimesVector %v4float %106 %326
        %328 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_0
        %329 = OpLoad %mat4v4float %328
        %330 = OpMatrixTimesVector %v4float %329 %327
        %331 = OpVectorShuffle %v3float %330 %330 0 1 2
        %332 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_2
        %333 = OpLoad %mat4v4float %332
        %334 = OpMatrixTimesVector %v4float %333 %327
        %335 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
        %336 = OpLoad %mat4v4float %335
        %337 = OpMatrixTimesVector %v4float %336 %334
        %338 = OpMatrixTimesVector %v4float %336 %330
        %339 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_1
        %340 = OpLoad %mat4v4float %339
        %341 = OpCompositeExtract %v4float %340 0
        %342 = OpVectorShuffle %v3float %341 %341 0 1 2
        %343 = OpCompositeExtract %v4float %340 1
        %344 = OpVectorShuffle %v3float %343 %343 0 1 2
        %345 = OpCompositeExtract %v4float %340 2
        %346 = OpVectorShuffle %v3float %345 %345 0 1 2
        %347 = OpCompositeConstruct %mat3v3float %342 %344 %346
        %348 = OpMatrixTimesVector %v3float %347 %321
        %349 = OpExtInst %v3float %1 Normalize %348
        %350 = OpMatrixTimesVector %v3float %347 %320
        %351 = OpExtInst %v3float %1 Normalize %350
        %352 = OpMatrixTimesVector %v3float %347 %87
        %353 = OpExtInst %v3float %1 Normalize %352
        %354 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_3
        %355 = OpLoad %v3float %354
        %356 = OpFSub %v3float %355 %331
        %357 = OpExtInst %v3float %1 Normalize %356
        %358 = OpAccessChain %_ptr_Uniform_uint %PerObject %int_3
        %359 = OpLoad %uint %358
        %360 = OpExtInst %float %1 Length %356
        %361 = OpFSub %float %float_100 %360
        %362 = OpFMul %float %361 %float_0_0199999996
        %363 = OpExtInst %float %1 FClamp %362 %float_0 %float_1
               OpStore %gl_Position %338
               OpStore %out_var_TEXCOORD0 %331
               OpStore %out_var_TEXCOORD1 %349
               OpStore %out_var_TEXCOORD2 %351
               OpStore %out_var_TEXCOORD3 %353
               OpStore %out_var_TEXCOORD4 %88
               OpStore %out_var_TEXCOORD5 %89
               OpStore %out_var_TEXCOORD6 %90
               OpStore %out_var_TEXCOORD7 %357
               OpStore %out_var_TEXCOORD8 %337
               OpStore %out_var_TEXCOORD9 %338
               OpStore %out_var_TEXCOORD10 %359
               OpStore %out_var_TEXCOORD11 %363
               OpReturn
               OpFunctionEnd

_amdgpu_cs_main:
	s_mov_b32 s34, s1                                          // 000000000000: BEA20301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v4, s2, 3, v0                               // 000000000008: D7460004 04010602
	s_mov_b32 s35, s1                                          // 000000000010: BEA30301
	s_mov_b64 s[0:1], exec                                     // 000000000014: BE80047E
	s_load_dwordx4 s[20:23], s[34:35], null                    // 000000000018: F4080511 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s33, s[20:23], 0xa0                    // 000000000024: F420084A FA0000A0
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cvt_u32_f32_e32 v0, s33                                  // 000000000030: 7E000E21
	v_cmpx_lt_u32_e64 v4, v0                                   // 000000000034: D4D1007E 00020104
	s_cbranch_execz _L0                                        // 00000000003C: BF880407
	s_buffer_load_dword s38, s[20:23], 0xa4                    // 000000000040: F420098A FA0000A4
	v_lshl_add_u32 v5, s3, 3, v1                               // 000000000048: D7460005 04050603
	s_waitcnt lgkmcnt(0)                                       // 000000000050: BF8CC07F
	v_cvt_u32_f32_e32 v1, s38                                  // 000000000054: 7E020E26
	v_cmp_lt_u32_e32 vcc_lo, v5, v1                            // 000000000058: 7D820305
	s_and_b64 exec, exec, vcc                                  // 00000000005C: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000060: BF8803FE
	s_buffer_load_dword s39, s[20:23], 0x98                    // 000000000064: F42009CA FA000098
	v_mov_b32_e32 v1, 0                                        // 00000000006C: 7E020280
	v_mov_b32_e32 v7, 0                                        // 000000000070: 7E0E0280
	v_mov_b32_e32 v8, 0                                        // 000000000074: 7E100280
	v_mov_b32_e32 v6, 0                                        // 000000000078: 7E0C0280
	s_mov_b32 s40, 0                                           // 00000000007C: BEA80380
	s_waitcnt lgkmcnt(0)                                       // 000000000080: BF8CC07F
	s_cmp_eq_u32 s39, 0                                        // 000000000084: BF068027
	s_cbranch_scc1 _L1                                         // 000000000088: BF8503C3
	s_clause 0x1                                               // 00000000008C: BFA10001
	s_buffer_load_dwordx2 s[36:37], s[20:23], 0x90             // 000000000090: F424090A FA000090
	s_buffer_load_dword s0, s[20:23], 0x9c                     // 000000000098: F420000A FA00009C
	s_clause 0x1                                               // 0000000000A0: BFA10001
	s_load_dwordx4 s[24:27], s[34:35], 0x30                    // 0000000000A4: F4080611 FA000030
	s_load_dwordx4 s[28:31], s[34:35], 0x60                    // 0000000000AC: F4080711 FA000060
	v_mad_u64_u32 v[0:1], null, v0, v5, v[4:5]                 // 0000000000B4: D5767D00 04120B00
	v_mov_b32_e32 v6, 0                                        // 0000000000BC: 7E0C0280
	v_mov_b32_e32 v8, 0                                        // 0000000000C0: 7E100280
	v_mov_b32_e32 v7, 0                                        // 0000000000C4: 7E0E0280
	s_mov_b32 s41, 0xbc46c6a5                                  // 0000000000C8: BEA903FF BC46C6A5
	s_mov_b32 s42, 0xbcc19a5f                                  // 0000000000D0: BEAA03FF BCC19A5F
	s_waitcnt lgkmcnt(0)                                       // 0000000000D8: BF8CC07F
	s_mul_i32 s1, s36, 0xafa21                                 // 0000000000DC: 9301FF24 000AFA21
	s_mov_b32 s36, 0xbea2f983                                  // 0000000000E4: BEA403FF BEA2F983
	v_add3_u32 v34, s1, s0, v0                                 // 0000000000EC: D76D0022 04000001
	s_branch _L2                                               // 0000000000F4: BF820007
_L28:
	s_or_b64 exec, exec, s[0:1]                                // 0000000000F8: 88FE007E
_L29:
	v_add_f32_e32 v6, v3, v6                                   // 0000000000FC: 060C0D03
	v_add_f32_e32 v8, v35, v8                                  // 000000000100: 06101123
	v_add_f32_e32 v7, v36, v7                                  // 000000000104: 060E0F24
	s_add_i32 s40, s40, 1                                      // 000000000108: 81288128
	s_cmp_lt_u32 s40, s39                                      // 00000000010C: BF0A2728
	s_cbranch_scc0 _L3                                         // 000000000110: BF8403A0
_L2:
	v_lshrrev_b32_e32 v0, 16, v34                              // 000000000114: 2C004490
	s_cmp_eq_u32 s37, 0                                        // 000000000118: BF068025
	v_xor3_b32 v0, v34, v0, 61                                 // 00000000011C: D5780000 02F60122
	v_lshl_add_u32 v0, v0, 3, v0                               // 000000000124: D7460000 04010700
	v_lshrrev_b32_e32 v1, 4, v0                                // 00000000012C: 2C020084
	v_xor_b32_e32 v0, v1, v0                                   // 000000000130: 3A000101
	v_mul_lo_u32 v0, 0x27d4eb2d, v0                            // 000000000134: D5690000 000200FF 27D4EB2D
	v_lshrrev_b32_e32 v1, 15, v0                               // 000000000140: 2C02008F
	v_xor_b32_e32 v0, v1, v0                                   // 000000000144: 3A000101
	v_lshrrev_b32_e32 v1, 16, v0                               // 000000000148: 2C020090
	v_xor3_b32 v1, v0, v1, 61                                  // 00000000014C: D5780001 02F60300
	v_lshl_add_u32 v1, v1, 3, v1                               // 000000000154: D7460001 04050701
	v_lshrrev_b32_e32 v2, 4, v1                                // 00000000015C: 2C040284
	v_xor_b32_e32 v1, v2, v1                                   // 000000000160: 3A020302
	v_mul_lo_u32 v1, 0x27d4eb2d, v1                            // 000000000164: D5690001 000202FF 27D4EB2D
	v_lshrrev_b32_e32 v2, 15, v1                               // 000000000170: 2C04028F
	v_xor_b32_e32 v34, v2, v1                                  // 000000000174: 3A440302
	s_cbranch_scc1 _L4                                         // 000000000178: BF850382
	s_buffer_load_dwordx8 s[0:7], s[20:23], 0x60               // 00000000017C: F42C000A FA000060
	v_cvt_f32_u32_e32 v1, v4                                   // 000000000184: 7E020D04
	v_cvt_f32_u32_e32 v2, v5                                   // 000000000188: 7E040D05
	v_cvt_f32_u32_e32 v0, v0                                   // 00000000018C: 7E000D00
	v_rcp_f32_e64 v3, s33 mul:2                                // 000000000190: D5AA0003 08000021
	v_cvt_f32_u32_e32 v24, v34                                 // 000000000198: 7E300D22
	v_add_f32_e32 v1, -0.5, v1                                 // 00000000019C: 060202F1
	v_rcp_f32_e64 v25, s38 mul:2                               // 0000000001A0: D5AA0019 08000026
	s_buffer_load_dwordx8 s[12:19], s[20:23], 0x40             // 0000000001A8: F42C030A FA000040
	v_add_f32_e32 v2, -0.5, v2                                 // 0000000001B0: 060404F1
	s_buffer_load_dwordx4 s[44:47], s[20:23], 0x20             // 0000000001B4: F4280B0A FA000020
	v_fmac_f32_e32 v1, 0x2f800000, v0                          // 0000000001BC: 560200FF 2F800000
	v_mov_b32_e32 v40, 1.0                                     // 0000000001C4: 7E5002F2
	v_mov_b32_e32 v41, 1.0                                     // 0000000001C8: 7E5202F2
	v_fmac_f32_e32 v2, 0x2f800000, v24                         // 0000000001CC: 560430FF 2F800000
	v_mov_b32_e32 v42, 1.0                                     // 0000000001D4: 7E5402F2
	v_fma_f32 v0, v1, v3, -1.0                                 // 0000000001D8: D54B0000 03CE0701
	v_mov_b32_e32 v35, 0                                       // 0000000001E0: 7E460280
	v_mov_b32_e32 v36, 0                                       // 0000000001E4: 7E480280
	v_fma_f32 v1, -v2, v25, 1.0                                // 0000000001E8: D54B0001 23CA3302
	s_mov_b32 s43, 0                                           // 0000000001F0: BEAB0380
	s_waitcnt lgkmcnt(0)                                       // 0000000001F4: BF8CC07F
	v_fma_f32 v2, v0, s4, s6                                   // 0000000001F8: D54B0002 00180900
	v_fmac_f32_e32 v2, s5, v1                                  // 000000000200: 56040205
	v_fma_f32 v3, v0, s12, s14                                 // 000000000204: D54B0003 00381900
	v_fma_f32 v24, v0, s16, s18                                // 00000000020C: D54B0018 00482100
	v_fma_f32 v0, v0, s0, s2                                   // 000000000214: D54B0000 00080100
	v_add_f32_e32 v2, s7, v2                                   // 00000000021C: 06040407
	s_buffer_load_dwordx8 s[4:11], s[20:23], null              // 000000000220: F42C010A FA000000
	v_fmac_f32_e32 v3, s13, v1                                 // 000000000228: 5606020D
	v_fmac_f32_e32 v24, s17, v1                                // 00000000022C: 56300211
	v_fmac_f32_e32 v0, s1, v1                                  // 000000000230: 56000201
	v_rcp_f32_e32 v2, v2                                       // 000000000234: 7E045502
	s_clause 0x1                                               // 000000000238: BFA10001
	s_buffer_load_dwordx2 s[0:1], s[20:23], 0x80               // 00000000023C: F424000A FA000080
	s_buffer_load_dword s2, s[20:23], 0x88                     // 000000000244: F420008A FA000088
	v_add_f32_e32 v3, s15, v3                                  // 00000000024C: 0606060F
	v_add_f32_e32 v1, s19, v24                                 // 000000000250: 06023013
	v_add_f32_e32 v0, s3, v0                                   // 000000000254: 06000003
	v_mul_f32_e32 v3, v2, v3                                   // 000000000258: 10060702
	v_mul_f32_e32 v1, v2, v1                                   // 00000000025C: 10020302
	v_mul_f32_e32 v0, v2, v0                                   // 000000000260: 10000102
	s_waitcnt lgkmcnt(0)                                       // 000000000264: BF8CC07F
	v_fma_f32 v24, v3, s8, s11                                 // 000000000268: D54B0018 002C1103
	v_fma_f32 v2, v3, s4, s7                                   // 000000000270: D54B0002 001C0903
	v_fma_f32 v3, v3, s44, s47                                 // 000000000278: D54B0003 00BC5903
	v_fmac_f32_e32 v24, s9, v1                                 // 000000000280: 56300209
	v_fmac_f32_e32 v2, s5, v1                                  // 000000000284: 56040205
	v_fmac_f32_e32 v3, s45, v1                                 // 000000000288: 5606022D
	s_mov_b64 s[4:5], 0                                        // 00000000028C: BE840480
	v_fmac_f32_e32 v24, s10, v0                                // 000000000290: 5630000A
	v_fmac_f32_e32 v2, s6, v0                                  // 000000000294: 56040006
	v_fmac_f32_e32 v3, s46, v0                                 // 000000000298: 5606002E
	v_subrev_f32_e32 v24, s1, v24                              // 00000000029C: 0A303001
	v_subrev_f32_e32 v25, s0, v2                               // 0000000002A0: 0A320400
	v_subrev_f32_e32 v26, s2, v3                               // 0000000002A4: 0A340602
	v_mov_b32_e32 v3, 0                                        // 0000000002A8: 7E060280
	v_mov_b32_e32 v2, s2                                       // 0000000002AC: 7E040202
	v_mul_f32_e32 v0, v24, v24                                 // 0000000002B0: 10003118
	v_fmac_f32_e32 v0, v25, v25                                // 0000000002B4: 56003319
	v_fmac_f32_e32 v0, v26, v26                                // 0000000002B8: 5600351A
	v_rsq_f32_e32 v1, v0                                       // 0000000002BC: 7E025D00
	v_cmp_neq_f32_e32 vcc_lo, 0, v0                            // 0000000002C0: 7C1A0080
	v_mov_b32_e32 v0, s0                                       // 0000000002C4: 7E000200
	v_cndmask_b32_e32 v27, 0, v1, vcc_lo                       // 0000000002C8: 02360280
	v_mov_b32_e32 v1, s1                                       // 0000000002CC: 7E020201
	v_mul_f32_e32 v37, v27, v26                                // 0000000002D0: 104A351B
	v_mul_f32_e32 v38, v27, v24                                // 0000000002D4: 104C311B
	v_mul_f32_e32 v39, v27, v25                                // 0000000002D8: 104E331B
	s_branch _L5                                               // 0000000002DC: BF82000E
_L27:
	s_or_b64 exec, exec, s[12:13]                              // 0000000002E0: 88FE0C7E
	s_andn2_b64 s[6:7], s[6:7], exec                           // 0000000002E4: 8A867E06
	s_and_b64 s[10:11], s[10:11], exec                         // 0000000002E8: 878A7E0A
	s_orn2_b64 s[8:9], s[8:9], exec                            // 0000000002EC: 8B887E08
	s_or_b64 s[6:7], s[6:7], s[10:11]                          // 0000000002F0: 88860A06
_L21:
	s_or_b64 exec, exec, s[0:1]                                // 0000000002F4: 88FE007E
	s_and_b64 s[0:1], exec, s[8:9]                             // 0000000002F8: 8780087E
	s_mov_b32 s43, s16                                         // 0000000002FC: BEAB0310
	s_or_b64 s[4:5], s[0:1], s[4:5]                            // 000000000300: 88840400
	s_andn2_b64 s[0:1], s[2:3], exec                           // 000000000304: 8A807E02
	s_and_b64 s[2:3], s[6:7], exec                             // 000000000308: 87827E06
	s_or_b64 s[2:3], s[0:1], s[2:3]                            // 00000000030C: 88820200
	s_andn2_b64 exec, exec, s[4:5]                             // 000000000310: 8AFE047E
	s_cbranch_execz _L6                                        // 000000000314: BF8802A7
_L5:
	s_buffer_load_dword s16, s[20:23], 0xb0                    // 000000000318: F420040A FA0000B0
	v_mov_b32_e32 v24, v34                                     // 000000000320: 7E300322
	v_mov_b32_e32 v27, v36                                     // 000000000324: 7E360324
	v_mov_b32_e32 v26, v35                                     // 000000000328: 7E340323
	v_mov_b32_e32 v25, v3                                      // 00000000032C: 7E320303
	v_mov_b32_e32 v30, v42                                     // 000000000330: 7E3C032A
	v_mov_b32_e32 v29, v41                                     // 000000000334: 7E3A0329
	v_mov_b32_e32 v28, v40                                     // 000000000338: 7E380328
	v_mov_b32_e32 v31, v37                                     // 00000000033C: 7E3E0325
	v_mov_b32_e32 v33, v38                                     // 000000000340: 7E420326
	v_mov_b32_e32 v32, v39                                     // 000000000344: 7E400327
	v_mov_b32_e32 v3, 0x447a0000                               // 000000000348: 7E0602FF 447A0000
	s_waitcnt lgkmcnt(0)                                       // 000000000350: BF8CC07F
	s_cmp_eq_u32 s16, 0                                        // 000000000354: BF068010
	s_cbranch_scc1 _L7                                         // 000000000358: BF850076
	v_mul_f32_e32 v3, v33, v33                                 // 00000000035C: 10064321
	v_mov_b32_e32 v34, 0x447a0000                              // 000000000360: 7E4402FF 447A0000
	s_mov_b64 s[10:11], 0                                      // 000000000368: BE8A0480
	s_mov_b32 s17, 0                                           // 00000000036C: BE910380
	s_mov_b32 s18, 0                                           // 000000000370: BE920380
	v_fmac_f32_e32 v3, v32, v32                                // 000000000374: 56064120
	v_fmac_f32_e32 v3, v31, v31                                // 000000000378: 56063F1F
	v_rcp_f32_e32 v35, v3                                      // 00000000037C: 7E465503
	v_mul_f32_e32 v36, 4.0, v3                                 // 000000000380: 104806F6
	v_mov_b32_e32 v3, 0x447a0000                               // 000000000384: 7E0602FF 447A0000
_L12:
	s_add_i32 s0, s17, 4                                       // 00000000038C: 81008411
	s_mov_b64 s[12:13], exec                                   // 000000000390: BE8C047E
	s_clause 0x1                                               // 000000000394: BFA10001
	s_buffer_load_dword s45, s[20:23], s0                      // 000000000398: F4200B4A 00000000
	s_buffer_load_dword s44, s[20:23], s17                     // 0000000003A0: F4200B0A 22000000
	s_add_i32 s0, s17, 8                                       // 0000000003A8: 81008811
	s_buffer_load_dword s19, s[20:23], s0                      // 0000000003AC: F42004CA 00000000
	s_add_i32 s0, s17, 12                                      // 0000000003B4: 81008C11
	s_buffer_load_dword s0, s[20:23], s0                       // 0000000003B8: F420000A 00000000
	s_waitcnt lgkmcnt(0)                                       // 0000000003C0: BF8CC07F
	v_subrev_f32_e32 v37, s45, v1                              // 0000000003C4: 0A4A022D
	v_subrev_f32_e32 v38, s44, v0                              // 0000000003C8: 0A4C002C
	v_subrev_f32_e32 v40, s19, v2                              // 0000000003CC: 0A500413
	v_mul_f32_e32 v39, v37, v37                                // 0000000003D0: 104E4B25
	v_mul_f32_e32 v37, v37, v33                                // 0000000003D4: 104A4325
	v_fmac_f32_e32 v39, v38, v38                               // 0000000003D8: 564E4D26
	v_fmac_f32_e32 v37, v38, v32                               // 0000000003DC: 564A4126
	v_fmac_f32_e32 v39, v40, v40                               // 0000000003E0: 564E5128
	v_fmac_f32_e32 v37, v40, v31                               // 0000000003E4: 564A3F28
	v_fma_f32 v38, s0, s0, -v39                                // 0000000003E8: D54B0026 849C0000
	v_add_f32_e32 v39, v37, v37                                // 0000000003F0: 064E4B25
	s_mov_b64 s[0:1], 0                                        // 0000000003F4: BE800480
	v_mul_f32_e32 v38, v36, v38                                // 0000000003F8: 104C4D24
	v_fmac_f32_e32 v38, v39, v39                               // 0000000003FC: 564C4F27
	v_cmpx_ngt_f32_e32 0, v38                                  // 000000000400: 7C364C80
	s_cbranch_execz _L8                                        // 000000000404: BF880030
	v_sqrt_f32_e32 v38, v38                                    // 000000000408: 7E4C6726
	v_fma_f32 v39, v37, -2.0, -v38 div:2                       // 00000000040C: D54B0027 9C99EB25
	v_fma_f32 v37, v37, -2.0, v38 div:2                        // 000000000414: D54B0025 1C99EB25
	v_mul_f32_e32 v39, v39, v35                                // 00000000041C: 104E4727
	v_mul_f32_e32 v37, v37, v35                                // 000000000420: 104A4725
	v_cmp_lt_f32_e32 vcc_lo, 0x3a83126f, v39                   // 000000000424: 7C024EFF 3A83126F
	v_cmp_lt_f32_e64 s0, v39, v3                               // 00000000042C: D4010000 00020727
	s_and_b64 vcc, vcc, s[0:1]                                 // 000000000434: 87EA006A
	v_cndmask_b32_e32 v37, v37, v39, vcc_lo                    // 000000000438: 024A4F25
	v_cmp_ngt_f32_e32 vcc_lo, 0x3a83126f, v37                  // 00000000043C: 7C164AFF 3A83126F
	v_cmp_ngt_f32_e64 s0, v37, v3                              // 000000000444: D40B0000 00020725
	s_and_b64 s[46:47], vcc, s[0:1]                            // 00000000044C: 87AE006A
	s_mov_b64 s[0:1], 0                                        // 000000000450: BE800480
	s_and_saveexec_b64 s[14:15], s[46:47]                      // 000000000454: BE8E242E
	s_cbranch_execz _L9                                        // 000000000458: BF880019
	v_fma_f32 v17, v37, v33, v1                                // 00000000045C: D54B0011 04064325
	v_fma_f32 v16, v37, v32, v0                                // 000000000464: D54B0010 04024125
	v_fma_f32 v18, v37, v31, v2                                // 00000000046C: D54B0012 040A3F25
	s_mov_b64 s[0:1], exec                                     // 000000000474: BE80047E
	v_subrev_f32_e32 v21, s45, v17                             // 000000000478: 0A2A222D
	v_subrev_f32_e32 v20, s44, v16                             // 00000000047C: 0A28202C
	v_subrev_f32_e32 v22, s19, v18                             // 000000000480: 0A2C2413
	s_add_i32 s19, s17, 16                                     // 000000000484: 81139011
	s_buffer_load_dword s19, s[20:23], s19                     // 000000000488: F42004CA 26000000
	v_mul_f32_e32 v19, v21, v21                                // 000000000490: 10262B15
	v_fmac_f32_e32 v19, v20, v20                               // 000000000494: 56262914
	v_fmac_f32_e32 v19, v22, v22                               // 000000000498: 56262D16
	v_rsq_f32_e32 v23, v19                                     // 00000000049C: 7E2E5D13
	v_cmp_neq_f32_e32 vcc_lo, 0, v19                           // 0000000004A0: 7C1A2680
	s_waitcnt lgkmcnt(0)                                       // 0000000004A4: BF8CC07F
	v_mov_b32_e32 v19, s19                                     // 0000000004A8: 7E260213
	v_cndmask_b32_e32 v23, 0, v23, vcc_lo                      // 0000000004AC: 022E2E80
	v_mul_f32_e32 v20, v23, v20                                // 0000000004B0: 10282917
	v_mul_f32_e32 v21, v23, v21                                // 0000000004B4: 102A2B17
	v_mul_f32_e32 v22, v23, v22                                // 0000000004B8: 102C2D17
	v_mov_b32_e32 v23, v37                                     // 0000000004BC: 7E2E0325
_L9:
	s_or_b64 exec, exec, s[14:15]                              // 0000000004C0: 88FE0E7E
	s_and_b64 s[0:1], s[0:1], exec                             // 0000000004C4: 87807E00
_L8:
	s_or_b64 exec, exec, s[12:13]                              // 0000000004C8: 88FE0C7E
	s_andn2_b64 s[8:9], s[8:9], exec                           // 0000000004CC: 8A887E08
	s_and_b64 s[12:13], s[10:11], exec                         // 0000000004D0: 878C7E0A
	s_or_b64 s[8:9], s[8:9], s[12:13]                          // 0000000004D4: 88880C08
	s_and_saveexec_b64 s[12:13], s[0:1]                        // 0000000004D8: BE8C2400
	s_cbranch_execz _L10                                       // 0000000004DC: BF88000E
	v_cmp_lt_f32_e32 vcc_lo, v23, v34                          // 0000000004E0: 7C024517
	s_andn2_b64 s[8:9], s[8:9], exec                           // 0000000004E4: 8A887E08
	s_or_b64 s[0:1], vcc, s[10:11]                             // 0000000004E8: 88800A6A
	v_cndmask_b32_e32 v34, v34, v23, vcc_lo                    // 0000000004EC: 02442F22
	v_cndmask_b32_e32 v3, v3, v23, vcc_lo                      // 0000000004F0: 02062F03
	v_cndmask_b32_e32 v13, v13, v16, vcc_lo                    // 0000000004F4: 021A210D
	v_cndmask_b32_e32 v14, v14, v17, vcc_lo                    // 0000000004F8: 021C230E
	v_cndmask_b32_e32 v15, v15, v18, vcc_lo                    // 0000000004FC: 021E250F
	v_cndmask_b32_e32 v10, v10, v20, vcc_lo                    // 000000000500: 0214290A
	v_cndmask_b32_e32 v11, v11, v21, vcc_lo                    // 000000000504: 02162B0B
	v_cndmask_b32_e32 v12, v12, v22, vcc_lo                    // 000000000508: 02182D0C
	v_cndmask_b32_e32 v9, v9, v19, vcc_lo                      // 00000000050C: 02122709
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000510: 87807E00
	s_or_b64 s[8:9], s[8:9], s[0:1]                            // 000000000514: 88880008
_L10:
	s_or_b64 exec, exec, s[12:13]                              // 000000000518: 88FE0C7E
	s_add_i32 s18, s18, 1                                      // 00000000051C: 81128112
	s_add_i32 s17, s17, 32                                     // 000000000520: 8111A011
	s_cmp_ge_u32 s18, s16                                      // 000000000524: BF091012
	s_cbranch_scc1 _L11                                        // 000000000528: BF850005
	s_mov_b64 s[10:11], s[8:9]                                 // 00000000052C: BE8A0408
	s_branch _L12                                              // 000000000530: BF82FF96
_L7:
	v_mov_b32_e32 v34, 0x447a0000                              // 000000000534: 7E4402FF 447A0000
	s_mov_b64 s[8:9], 0                                        // 00000000053C: BE880480
_L11:
	s_buffer_load_dword s44, s[20:23], 0xb4                    // 000000000540: F4200B0A FA0000B4
	s_waitcnt lgkmcnt(0)                                       // 000000000548: BF8CC07F
	s_cmp_eq_u32 s44, 0                                        // 00000000054C: BF06802C
	s_cbranch_scc1 _L13                                        // 000000000550: BF8500D5
	s_mov_b32 s45, 0                                           // 000000000554: BEAD0380
	s_mov_b32 s46, 0                                           // 000000000558: BEAE0380
_L20:
	s_add_i32 s1, s45, 40                                      // 00000000055C: 8101A82D
	s_add_i32 s0, s45, 4                                       // 000000000560: 8100842D
	s_add_i32 s12, s45, 8                                      // 000000000564: 810C882D
	s_clause 0x3                                               // 000000000568: BFA10003
	s_buffer_load_dword s1, s[24:27], s1                       // 00000000056C: F420004C 02000000
	s_buffer_load_dword s15, s[24:27], s45                     // 000000000574: F42003CC 5A000000
	s_buffer_load_dword s16, s[24:27], s0                      // 00000000057C: F420040C 00000000
	s_buffer_load_dword s14, s[24:27], s12                     // 000000000584: F420038C 18000000
	s_add_i32 s0, s45, 32                                      // 00000000058C: 8100A02D
	s_add_i32 s12, s45, 36                                     // 000000000590: 810CA42D
	s_clause 0x1                                               // 000000000594: BFA10001
	s_buffer_load_dword s0, s[24:27], s0                       // 000000000598: F420000C 00000000
	s_buffer_load_dword s12, s[24:27], s12                     // 0000000005A0: F420030C 18000000
	s_add_i32 s13, s45, 20                                     // 0000000005A8: 810D942D
	s_add_i32 s17, s45, 16                                     // 0000000005AC: 8111902D
	s_clause 0x1                                               // 0000000005B0: BFA10001
	s_buffer_load_dword s13, s[24:27], s13                     // 0000000005B4: F420034C 1A000000
	s_buffer_load_dword s17, s[24:27], s17                     // 0000000005BC: F420044C 22000000
	s_add_i32 s18, s45, 24                                     // 0000000005C4: 8112982D
	s_buffer_load_dword s18, s[24:27], s18                     // 0000000005C8: F420048C 24000000
	s_waitcnt lgkmcnt(0)                                       // 0000000005D0: BF8CC07F
	v_sub_f32_e64 v36, s1, s14                                 // 0000000005D4: D5040024 00001C01
	v_sub_f32_e64 v37, s0, s15                                 // 0000000005DC: D5040025 00001E00
	v_sub_f32_e64 v38, s12, s16                                // 0000000005E4: D5040026 0000200C
	s_mov_b64 s[0:1], 0                                        // 0000000005EC: BE800480
	v_mul_f32_e32 v35, v36, v32                                // 0000000005F0: 10464124
	v_sub_f32_e64 v39, s13, s16                                // 0000000005F4: D5040027 0000200D
	v_sub_f32_e64 v40, s17, s15                                // 0000000005FC: D5040028 00001E11
	v_mul_f32_e32 v42, v38, v31                                // 000000000604: 10543F26
	v_mul_f32_e32 v43, v37, v33                                // 000000000608: 10564325
	v_fma_f32 v41, v37, v31, -v35                              // 00000000060C: D54B0029 848E3F25
	s_mov_b64 s[12:13], exec                                   // 000000000614: BE8C047E
	v_fma_f32 v35, v36, v33, -v42                              // 000000000618: D54B0023 84AA4324
	v_sub_f32_e64 v42, s18, s14                                // 000000000620: D504002A 00001C12
	v_mul_f32_e32 v46, v41, v39                                // 000000000628: 105C4F29
	v_fma_f32 v45, v38, v32, -v43                              // 00000000062C: D54B002D 84AE4126
	v_fmac_f32_e32 v46, v35, v40                               // 000000000634: 565C5123
	v_fmac_f32_e32 v46, v45, v42                               // 000000000638: 565C552D
	v_cmpx_ngt_f32_e64 0x3727c5ac, |v46|                       // 00000000063C: D41B027E 00025CFF 3727C5AC
	s_cbranch_execz _L14                                       // 000000000648: BF88007C
	v_subrev_f32_e32 v44, s16, v1                              // 00000000064C: 0A580210
	v_subrev_f32_e32 v43, s15, v0                              // 000000000650: 0A56000F
	v_mul_f32_e32 v47, v41, v44                                // 000000000654: 105E5929
	v_rcp_f32_e32 v41, v46                                     // 000000000658: 7E52552E
	v_subrev_f32_e32 v46, s14, v2                              // 00000000065C: 0A5C040E
	v_fmac_f32_e32 v47, v35, v43                               // 000000000660: 565E5723
	v_fmac_f32_e32 v47, v45, v46                               // 000000000664: 565E5D2D
	v_mul_f32_e32 v35, v41, v47                                // 000000000668: 10465F29
	v_cmp_ngt_f32_e32 vcc_lo, 0, v35                           // 00000000066C: 7C164680
	v_cmp_nlt_f32_e64 s0, 1.0, v35                             // 000000000670: D40E0000 000246F2
	s_and_b64 s[16:17], vcc, s[0:1]                            // 000000000678: 8790006A
	s_mov_b64 s[0:1], 0                                        // 00000000067C: BE800480
	s_and_saveexec_b64 s[14:15], s[16:17]                      // 000000000680: BE8E2410
	s_cbranch_execz _L15                                       // 000000000684: BF88006B
	v_mul_f32_e32 v45, v42, v43                                // 000000000688: 105A572A
	v_mul_f32_e32 v47, v39, v46                                // 00000000068C: 105E5D27
	v_fma_f32 v45, v40, v46, -v45                              // 000000000690: D54B002D 84B65D28
	v_mul_f32_e32 v40, v40, v44                                // 000000000698: 10505928
	v_fma_f32 v42, v42, v44, -v47                              // 00000000069C: D54B002A 84BE592A
	v_mul_f32_e32 v44, v45, v33                                // 0000000006A4: 1058432D
	v_fma_f32 v43, v39, v43, -v40                              // 0000000006A8: D54B002B 84A25727
	v_fmac_f32_e32 v44, v42, v32                               // 0000000006B0: 5658412A
	v_fmac_f32_e32 v44, v43, v31                               // 0000000006B4: 56583F2B
	v_mul_f32_e32 v39, v41, v44                                // 0000000006B8: 104E5929
	v_fma_f32 v40, v41, v44, v35                               // 0000000006BC: D54B0028 048E5929
	v_cmp_ngt_f32_e32 vcc_lo, 0, v39                           // 0000000006C4: 7C164E80
	v_cmp_nlt_f32_e64 s0, 1.0, v40                             // 0000000006C8: D40E0000 000250F2
	s_and_b64 s[18:19], vcc, s[0:1]                            // 0000000006D0: 8792006A
	s_mov_b64 s[0:1], 0                                        // 0000000006D4: BE800480
	s_and_saveexec_b64 s[16:17], s[18:19]                      // 0000000006D8: BE902412
	s_cbranch_execz _L16                                       // 0000000006DC: BF880053
	v_mul_f32_e32 v38, v45, v38                                // 0000000006E0: 104C4D2D
	v_fmac_f32_e32 v38, v42, v37                               // 0000000006E4: 564C4B2A
	v_fmac_f32_e32 v38, v43, v36                               // 0000000006E8: 564C492B
	v_mul_f32_e32 v36, v41, v38                                // 0000000006EC: 10484D29
	v_cmp_ngt_f32_e32 vcc_lo, 0x3a83126f, v36                  // 0000000006F0: 7C1648FF 3A83126F
	v_cmp_ngt_f32_e64 s0, v36, v3                              // 0000000006F8: D40B0000 00020724
	s_and_b64 s[48:49], vcc, s[0:1]                            // 000000000700: 87B0006A
	s_mov_b64 s[0:1], 0                                        // 000000000704: BE800480
	s_and_saveexec_b64 s[18:19], s[48:49]                      // 000000000708: BE922430
	s_cbranch_execz _L17                                       // 00000000070C: BF880045
	s_add_i32 s47, s45, 0x44                                   // 000000000710: 812FFF2D 00000044
	s_add_i32 s48, s45, 64                                     // 000000000718: 8130C02D
	s_clause 0x1                                               // 00000000071C: BFA10001
	s_buffer_load_dword s47, s[24:27], s47                     // 000000000720: F4200BCC 5E000000
	s_buffer_load_dword s48, s[24:27], s48                     // 000000000728: F4200C0C 60000000
	s_add_i32 s49, s45, 0x54                                   // 000000000730: 8131FF2D 00000054
	s_add_i32 s51, s45, 0x48                                   // 000000000738: 8133FF2D 00000048
	s_buffer_load_dword s49, s[24:27], s49                     // 000000000740: F4200C4C 62000000
	s_add_i32 s50, s45, 52                                     // 000000000748: 8132B42D
	s_add_i32 s52, s45, 0x50                                   // 00000000074C: 8134FF2D 00000050
	s_clause 0x2                                               // 000000000754: BFA10002
	s_buffer_load_dword s51, s[24:27], s51                     // 000000000758: F4200CCC 66000000
	s_buffer_load_dword s52, s[24:27], s52                     // 000000000760: F4200D0C 68000000
	s_buffer_load_dword s50, s[24:27], s50                     // 000000000768: F4200C8C 64000000
	s_add_i32 s53, s45, 0x58                                   // 000000000770: 8135FF2D 00000058
	s_add_i32 s54, s45, 48                                     // 000000000778: 8136B02D
	s_clause 0x1                                               // 00000000077C: BFA10001
	s_buffer_load_dword s53, s[24:27], s53                     // 000000000780: F4200D4C 6A000000
	s_buffer_load_dword s54, s[24:27], s54                     // 000000000788: F4200D8C 6C000000
	s_add_i32 s55, s45, 56                                     // 000000000790: 8137B82D
	v_sub_f32_e32 v16, 1.0, v40                                // 000000000794: 082050F2
	s_buffer_load_dword s55, s[24:27], s55                     // 000000000798: F4200DCC 6E000000
	v_fma_f32 v18, v36, v31, v2                                // 0000000007A0: D54B0012 040A3F24
	s_mov_b64 s[0:1], exec                                     // 0000000007A8: BE80047E
	s_waitcnt lgkmcnt(0)                                       // 0000000007AC: BF8CC07F
	v_mul_f32_e32 v21, s47, v35                                // 0000000007B0: 102A462F
	s_add_i32 s47, s45, 0x78                                   // 0000000007B4: 812FFF2D 00000078
	v_mul_f32_e32 v20, s48, v35                                // 0000000007BC: 10284630
	s_buffer_load_dword s47, s[24:27], s47                     // 0000000007C0: F4200BCC 5E000000
	v_mul_f32_e32 v22, s51, v35                                // 0000000007C8: 102C4633
	v_fmac_f32_e32 v21, s49, v39                               // 0000000007CC: 562A4E31
	v_fmac_f32_e32 v20, s52, v39                               // 0000000007D0: 56284E34
	v_fmac_f32_e32 v22, s53, v39                               // 0000000007D4: 562C4E35
	v_fmac_f32_e32 v21, s50, v16                               // 0000000007D8: 562A2032
	v_fmac_f32_e32 v20, s54, v16                               // 0000000007DC: 56282036
	v_fmac_f32_e32 v22, s55, v16                               // 0000000007E0: 562C2037
	v_mul_f32_e32 v17, v21, v21                                // 0000000007E4: 10222B15
	v_fmac_f32_e32 v17, v20, v20                               // 0000000007E8: 56222914
	s_waitcnt lgkmcnt(0)                                       // 0000000007EC: BF8CC07F
	v_mov_b32_e32 v19, s47                                     // 0000000007F0: 7E26022F
	v_fmac_f32_e32 v17, v22, v22                               // 0000000007F4: 56222D16
	v_rsq_f32_e32 v16, v17                                     // 0000000007F8: 7E205D11
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 0000000007FC: 7C1A2280
	v_fma_f32 v17, v36, v33, v1                                // 000000000800: D54B0011 04064324
	v_cndmask_b32_e32 v23, 0, v16, vcc_lo                      // 000000000808: 022E2080
	v_fma_f32 v16, v36, v32, v0                                // 00000000080C: D54B0010 04024124
	v_mul_f32_e32 v20, v23, v20                                // 000000000814: 10282917
	v_mul_f32_e32 v21, v23, v21                                // 000000000818: 102A2B17
	v_mul_f32_e32 v22, v23, v22                                // 00000000081C: 102C2D17
	v_mov_b32_e32 v23, v36                                     // 000000000820: 7E2E0324
_L17:
	s_or_b64 exec, exec, s[18:19]                              // 000000000824: 88FE127E
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000828: 87807E00
_L16:
	s_or_b64 exec, exec, s[16:17]                              // 00000000082C: 88FE107E
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000830: 87807E00
_L15:
	s_or_b64 exec, exec, s[14:15]                              // 000000000834: 88FE0E7E
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000838: 87807E00
_L14:
	s_or_b64 exec, exec, s[12:13]                              // 00000000083C: 88FE0C7E
	s_andn2_b64 s[10:11], s[10:11], exec                       // 000000000840: 8A8A7E0A
	s_and_b64 s[12:13], s[8:9], exec                           // 000000000844: 878C7E08
	s_or_b64 s[10:11], s[10:11], s[12:13]                      // 000000000848: 888A0C0A
	s_and_saveexec_b64 s[12:13], s[0:1]                        // 00000000084C: BE8C2400
	s_cbranch_execz _L18                                       // 000000000850: BF88000E
	v_cmp_lt_f32_e32 vcc_lo, v23, v34                          // 000000000854: 7C024517
	s_or_b64 s[0:1], s[8:9], vcc                               // 000000000858: 88806A08
	v_cndmask_b32_e32 v34, v34, v23, vcc_lo                    // 00000000085C: 02442F22
	v_cndmask_b32_e32 v3, v3, v23, vcc_lo                      // 000000000860: 02062F03
	v_cndmask_b32_e32 v13, v13, v16, vcc_lo                    // 000000000864: 021A210D
	v_cndmask_b32_e32 v14, v14, v17, vcc_lo                    // 000000000868: 021C230E
	v_cndmask_b32_e32 v15, v15, v18, vcc_lo                    // 00000000086C: 021E250F
	v_cndmask_b32_e32 v10, v10, v20, vcc_lo                    // 000000000870: 0214290A
	v_cndmask_b32_e32 v11, v11, v21, vcc_lo                    // 000000000874: 02162B0B
	v_cndmask_b32_e32 v12, v12, v22, vcc_lo                    // 000000000878: 02182D0C
	v_cndmask_b32_e32 v9, v9, v19, vcc_lo                      // 00000000087C: 02122709
	s_andn2_b64 s[8:9], s[10:11], exec                         // 000000000880: 8A887E0A
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000884: 87807E00
	s_or_b64 s[10:11], s[8:9], s[0:1]                          // 000000000888: 888A0008
_L18:
	s_or_b64 exec, exec, s[12:13]                              // 00000000088C: 88FE0C7E
	s_add_i32 s46, s46, 1                                      // 000000000890: 812E812E
	s_addk_i32 s45, 0x90                                       // 000000000894: B7AD0090
	s_cmp_lt_u32 s46, s44                                      // 000000000898: BF0A2C2E
	s_cbranch_scc0 _L19                                        // 00000000089C: BF840003
	s_mov_b64 s[8:9], s[10:11]                                 // 0000000008A0: BE88040A
	s_branch _L20                                              // 0000000008A4: BF82FF2D
_L13:
	s_mov_b64 s[10:11], s[8:9]                                 // 0000000008A8: BE8A0408
_L19:
	v_readfirstlane_b32 s16, v0                                // 0000000008AC: 7E200500
	s_mov_b64 s[8:9], -1                                       // 0000000008B0: BE8804C1
	s_or_b64 s[6:7], s[6:7], exec                              // 0000000008B4: 88867E06
	s_and_saveexec_b64 s[0:1], s[10:11]                        // 0000000008B8: BE80240A
	s_cbranch_execz _L21                                       // 0000000008BC: BF88FE8D
	v_mul_lo_u32 v35, v9, 48                                   // 0000000008C0: D5690023 00016109
	v_lshrrev_b32_e32 v34, 16, v24                             // 0000000008C8: 2C443090
	v_xor3_b32 v34, v24, v34, 61                               // 0000000008CC: D5780022 02F64518
	buffer_load_dwordx4 v[0:3], v35, s[28:31], 0 offen         // 0000000008D4: E0381000 80070023
	v_lshl_add_u32 v34, v34, 3, v34                            // 0000000008DC: D7460022 04890722
	v_lshrrev_b32_e32 v36, 4, v34                              // 0000000008E4: 2C484484
	v_xor_b32_e32 v34, v36, v34                                // 0000000008E8: 3A444524
	v_mul_lo_u32 v34, 0x27d4eb2d, v34                          // 0000000008EC: D5690022 000244FF 27D4EB2D
	v_lshrrev_b32_e32 v36, 15, v34                             // 0000000008F8: 2C48448F
	v_xor_b32_e32 v34, v36, v34                                // 0000000008FC: 3A444524
	v_cvt_f32_u32_e32 v36, v34                                 // 000000000900: 7E480D22
	v_mul_f32_e32 v36, 0x2f800000, v36                         // 000000000904: 104848FF 2F800000
	v_cmp_ngt_f32_e32 vcc_lo, 0.5, v36                         // 00000000090C: 7C1648F0
	s_and_saveexec_b64 s[8:9], vcc                             // 000000000910: BE88246A
	s_xor_b64 s[8:9], exec, s[8:9]                             // 000000000914: 8988087E
	s_cbranch_execz _L22                                       // 000000000918: BF88004F
	s_mov_b64 s[10:11], 0                                      // 00000000091C: BE8A0480
_L23:
	v_lshrrev_b32_e32 v36, 16, v34                             // 000000000920: 2C484490
	v_xor3_b32 v34, v34, v36, 61                               // 000000000924: D5780022 02F64922
	v_lshl_add_u32 v34, v34, 3, v34                            // 00000000092C: D7460022 04890722
	v_lshrrev_b32_e32 v36, 4, v34                              // 000000000934: 2C484484
	v_xor_b32_e32 v34, v36, v34                                // 000000000938: 3A444524
	v_mul_lo_u32 v34, 0x27d4eb2d, v34                          // 00000000093C: D5690022 000244FF 27D4EB2D
	v_lshrrev_b32_e32 v36, 15, v34                             // 000000000948: 2C48448F
	v_xor_b32_e32 v34, v36, v34                                // 00000000094C: 3A444524
	v_lshrrev_b32_e32 v36, 16, v34                             // 000000000950: 2C484490
	v_cvt_f32_u32_e32 v39, v34                                 // 000000000954: 7E4E0D22
	v_xor3_b32 v36, v34, v36, 61                               // 000000000958: D5780024 02F64922
	v_lshl_add_u32 v36, v36, 3, v36                            // 000000000960: D7460024 04910724
	v_lshrrev_b32_e32 v37, 4, v36                              // 000000000968: 2C4A4884
	v_xor_b32_e32 v36, v37, v36                                // 00000000096C: 3A484925
	v_mul_lo_u32 v36, 0x27d4eb2d, v36                          // 000000000970: D5690024 000248FF 27D4EB2D
	v_lshrrev_b32_e32 v37, 15, v36                             // 00000000097C: 2C4A488F
	v_xor_b32_e32 v36, v37, v36                                // 000000000980: 3A484925
	v_lshrrev_b32_e32 v37, 16, v36                             // 000000000984: 2C4A4890
	v_xor3_b32 v37, v36, v37, 61                               // 000000000988: D5780025 02F64B24
	v_cvt_f32_u32_e32 v36, v36                                 // 000000000990: 7E480D24
	v_lshl_add_u32 v37, v37, 3, v37                            // 000000000994: D7460025 04950725
	v_fma_f32 v36, 0x30000000, v36, -1.0                       // 00000000099C: D54B0024 03CE48FF 30000000
	v_lshrrev_b32_e32 v38, 4, v37                              // 0000000009A8: 2C4C4A84
	v_xor_b32_e32 v37, v38, v37                                // 0000000009AC: 3A4A4B26
	v_mul_lo_u32 v37, 0x27d4eb2d, v37                          // 0000000009B0: D5690025 00024AFF 27D4EB2D
	v_lshrrev_b32_e32 v38, 15, v37                             // 0000000009BC: 2C4C4A8F
	v_xor_b32_e32 v34, v38, v37                                // 0000000009C0: 3A444B26
	v_fma_f32 v37, 0x30000000, v39, -1.0                       // 0000000009C4: D54B0025 03CE4EFF 30000000
	v_mul_f32_e32 v39, v36, v36                                // 0000000009D0: 104E4924
	v_cvt_f32_u32_e32 v38, v34                                 // 0000000009D4: 7E4C0D22
	v_fmac_f32_e32 v39, v37, v37                               // 0000000009D8: 564E4B25
	v_fma_f32 v38, 0x30000000, v38, -1.0                       // 0000000009DC: D54B0026 03CE4CFF 30000000
	v_fmac_f32_e32 v39, v38, v38                               // 0000000009E8: 564E4D26
	v_cmp_nle_f32_e32 vcc_lo, 1.0, v39                         // 0000000009EC: 7C184EF2
	s_or_b64 s[10:11], vcc, s[10:11]                           // 0000000009F0: 888A0A6A
	s_andn2_b64 exec, exec, s[10:11]                           // 0000000009F4: 8AFE0A7E
	s_cbranch_execnz _L23                                      // 0000000009F8: BF89FFC9
	s_or_b64 exec, exec, s[10:11]                              // 0000000009FC: 88FE0A7E
	v_rsq_f32_e32 v40, v39                                     // 000000000A00: 7E505D27
	v_cmp_neq_f32_e32 vcc_lo, 0, v39                           // 000000000A04: 7C1A4E80
	s_waitcnt vmcnt(0)                                         // 000000000A08: BF8C3F70
	v_fmaak_f32 v3, s36, v3, 0x3ea2f983                        // 000000000A0C: 5A060624 3EA2F983
	v_cndmask_b32_e32 v39, 0, v40, vcc_lo                      // 000000000A14: 024E5080
	v_mul_f32_e32 v40, v3, v0                                  // 000000000A18: 10500103
	v_mul_f32_e32 v41, v39, v36                                // 000000000A1C: 10524927
	v_mul_f32_e32 v37, v39, v37                                // 000000000A20: 104A4B27
	v_mul_f32_e32 v42, v39, v38                                // 000000000A24: 10544D27
	v_mul_f32_e32 v36, v41, v11                                // 000000000A28: 10481729
	v_fmac_f32_e32 v36, v37, v10                               // 000000000A2C: 56481525
	v_fmac_f32_e32 v36, v42, v12                               // 000000000A30: 5648192A
	v_cmp_lt_f32_e32 vcc_lo, 0, v36                            // 000000000A34: 7C024880
	v_mul_f32_e32 v36, v3, v1                                  // 000000000A38: 10480303
	v_cndmask_b32_e64 v39, -v37, v37, vcc_lo                   // 000000000A3C: D5010027 21AA4B25
	v_cndmask_b32_e64 v38, -v41, v41, vcc_lo                   // 000000000A44: D5010026 21AA5329
	v_cndmask_b32_e64 v37, -v42, v42, vcc_lo                   // 000000000A4C: D5010025 21AA552A
	v_mul_f32_e32 v41, v3, v2                                  // 000000000A54: 10520503
_L22:
	s_andn2_saveexec_b64 s[8:9], s[8:9]                        // 000000000A58: BE882708
	s_cbranch_execz _L24                                       // 000000000A5C: BF880088
	s_mov_b64 s[10:11], 0                                      // 000000000A60: BE8A0480
_L25:
	v_lshrrev_b32_e32 v36, 16, v34                             // 000000000A64: 2C484490
	v_xor3_b32 v34, v34, v36, 61                               // 000000000A68: D5780022 02F64922
	v_lshl_add_u32 v34, v34, 3, v34                            // 000000000A70: D7460022 04890722
	v_lshrrev_b32_e32 v36, 4, v34                              // 000000000A78: 2C484484
	v_xor_b32_e32 v34, v36, v34                                // 000000000A7C: 3A444524
	v_mul_lo_u32 v34, 0x27d4eb2d, v34                          // 000000000A80: D5690022 000244FF 27D4EB2D
	v_lshrrev_b32_e32 v36, 15, v34                             // 000000000A8C: 2C48448F
	v_xor_b32_e32 v34, v36, v34                                // 000000000A90: 3A444524
	v_lshrrev_b32_e32 v36, 16, v34                             // 000000000A94: 2C484490
	v_cvt_f32_u32_e32 v40, v34                                 // 000000000A98: 7E500D22
	v_xor3_b32 v36, v34, v36, 61                               // 000000000A9C: D5780024 02F64922
	v_lshl_add_u32 v36, v36, 3, v36                            // 000000000AA4: D7460024 04910724
	v_lshrrev_b32_e32 v37, 4, v36                              // 000000000AAC: 2C4A4884
	v_xor_b32_e32 v36, v37, v36                                // 000000000AB0: 3A484925
	v_mul_lo_u32 v36, 0x27d4eb2d, v36                          // 000000000AB4: D5690024 000248FF 27D4EB2D
	v_lshrrev_b32_e32 v37, 15, v36                             // 000000000AC0: 2C4A488F
	v_xor_b32_e32 v36, v37, v36                                // 000000000AC4: 3A484925
	v_lshrrev_b32_e32 v37, 16, v36                             // 000000000AC8: 2C4A4890
	v_xor3_b32 v37, v36, v37, 61                               // 000000000ACC: D5780025 02F64B24
	v_cvt_f32_u32_e32 v36, v36                                 // 000000000AD4: 7E480D24
	v_lshl_add_u32 v37, v37, 3, v37                            // 000000000AD8: D7460025 04950725
	v_lshrrev_b32_e32 v38, 4, v37                              // 000000000AE0: 2C4C4A84
	v_xor_b32_e32 v37, v38, v37                                // 000000000AE4: 3A4A4B26
	v_fma_f32 v38, 0x30000000, v36, -1.0                       // 000000000AE8: D54B0026 03CE48FF 30000000
	v_mul_lo_u32 v37, 0x27d4eb2d, v37                          // 000000000AF4: D5690025 00024AFF 27D4EB2D
	v_lshrrev_b32_e32 v39, 15, v37                             // 000000000B00: 2C4E4A8F
	v_xor_b32_e32 v34, v39, v37                                // 000000000B04: 3A444B27
	v_fma_f32 v37, 0x30000000, v40, -1.0                       // 000000000B08: D54B0025 03CE50FF 30000000
	v_mul_f32_e32 v39, v38, v38                                // 000000000B14: 104E4D26
	v_cvt_f32_u32_e32 v36, v34                                 // 000000000B18: 7E480D22
	v_fmac_f32_e32 v39, v37, v37                               // 000000000B1C: 564E4B25
	v_fma_f32 v36, 0x30000000, v36, -1.0                       // 000000000B20: D54B0024 03CE48FF 30000000
	v_fmac_f32_e32 v39, v36, v36                               // 000000000B2C: 564E4924
	v_cmp_nle_f32_e32 vcc_lo, 1.0, v39                         // 000000000B30: 7C184EF2
	s_or_b64 s[10:11], vcc, s[10:11]                           // 000000000B34: 888A0A6A
	s_andn2_b64 exec, exec, s[10:11]                           // 000000000B38: 8AFE0A7E
	s_cbranch_execnz _L25                                      // 000000000B3C: BF89FFC9
	s_or_b64 exec, exec, s[10:11]                              // 000000000B40: 88FE0A7E
	buffer_load_dword v40, v35, s[28:31], 0 offen offset:28    // 000000000B44: E030101C 80072823
	v_mul_f32_e32 v41, v33, v11                                // 000000000B4C: 10521721
	v_rsq_f32_e32 v42, v39                                     // 000000000B50: 7E545D27
	v_cmp_neq_f32_e32 vcc_lo, 0, v39                           // 000000000B54: 7C1A4E80
	s_waitcnt vmcnt(1)                                         // 000000000B58: BF8C3F71
	v_add_f32_e32 v0, 0xbd23d70a, v0                           // 000000000B5C: 060000FF BD23D70A
	v_add_f32_e32 v1, 0xbd23d70a, v1                           // 000000000B64: 060202FF BD23D70A
	v_fmac_f32_e32 v41, v32, v10                               // 000000000B6C: 56521520
	v_add_f32_e32 v2, 0xbd23d70a, v2                           // 000000000B70: 060404FF BD23D70A
	v_fma_f32 v41, v31, v12, v41 mul:2                         // 000000000B78: D54B0029 0CA6191F
	v_cndmask_b32_e32 v39, 0, v42, vcc_lo                      // 000000000B80: 024E5480
	v_fma_f32 v42, -v11, v41, v33                              // 000000000B84: D54B002A 2486530B
	v_mul_f32_e32 v38, v39, v38                                // 000000000B8C: 104C4D27
	v_fma_f32 v43, -v10, v41, v32                              // 000000000B90: D54B002B 2482530A
	v_mul_f32_e32 v37, v39, v37                                // 000000000B98: 104A4B27
	v_mul_f32_e32 v36, v39, v36                                // 000000000B9C: 10484927
	v_mul_f32_e32 v39, v31, v12                                // 000000000BA0: 104E191F
	v_fmac_f32_e32 v39, v32, v10                               // 000000000BA4: 564E1520
	s_waitcnt vmcnt(0)                                         // 000000000BA8: BF8C3F70
	v_fmac_f32_e32 v42, v38, v40                               // 000000000BAC: 56545126
	v_fma_f32 v38, -v12, v41, v31                              // 000000000BB0: D54B0026 247E530C
	v_fmac_f32_e32 v43, v37, v40                               // 000000000BB8: 56565125
	v_fmaak_f32 v41, v2, v3, 0x3d23d70a                        // 000000000BBC: 5A520702 3D23D70A
	v_fma_f32 v2, -v2, v3, 0x3f75c28f                          // 000000000BC4: D54B0002 23FE0702 3F75C28F
	v_mul_f32_e32 v37, v42, v42                                // 000000000BD0: 104A552A
	v_fmac_f32_e32 v38, v36, v40                               // 000000000BD4: 564C5124
	v_fmaak_f32 v40, v0, v3, 0x3d23d70a                        // 000000000BD8: 5A500700 3D23D70A
	v_fma_f32 v0, -v0, v3, 0x3f75c28f                          // 000000000BE0: D54B0000 23FE0700 3F75C28F
	v_fmac_f32_e32 v37, v43, v43                               // 000000000BEC: 564A572B
	v_fmac_f32_e32 v37, v38, v38                               // 000000000BF0: 564A4D26
	v_rsq_f32_e32 v36, v37                                     // 000000000BF4: 7E485D25
	v_cmp_neq_f32_e32 vcc_lo, 0, v37                           // 000000000BF8: 7C1A4A80
	v_fma_f32 v37, v11, -v33, -v39                             // 000000000BFC: D54B0025 C49E430B
	v_max_f32_e32 v37, 0, v37                                  // 000000000C04: 204A4A80
	v_cndmask_b32_e32 v36, 0, v36, vcc_lo                      // 000000000C08: 02484880
	v_sub_f32_e32 v37, 1.0, v37                                // 000000000C0C: 084A4AF2
	v_mul_f32_e32 v42, v36, v42                                // 000000000C10: 10545524
	v_mul_f32_e32 v39, v36, v43                                // 000000000C14: 104E5724
	v_mul_f32_e32 v44, v36, v38                                // 000000000C18: 10584D24
	v_mul_f32_e32 v38, v37, v37                                // 000000000C1C: 104C4B25
	v_fmaak_f32 v36, v1, v3, 0x3d23d70a                        // 000000000C20: 5A480701 3D23D70A
	v_mul_f32_e32 v43, v11, v42                                // 000000000C28: 1056550B
	v_fma_f32 v1, -v1, v3, 0x3f75c28f                          // 000000000C2C: D54B0001 23FE0701 3F75C28F
	v_mul_f32_e32 v38, v38, v38                                // 000000000C38: 104C4D26
	v_fmac_f32_e32 v43, v10, v39                               // 000000000C3C: 56564F0A
	v_mul_f32_e32 v3, v37, v38                                 // 000000000C40: 10064D25
	v_fmac_f32_e32 v43, v12, v44                               // 000000000C44: 5656590C
	v_fmac_f32_e32 v40, v0, v3                                 // 000000000C48: 56500700
	v_fmac_f32_e32 v36, v1, v3                                 // 000000000C4C: 56480701
	v_add_f32_e32 v45, v43, v43                                // 000000000C50: 065A572B
	v_cmp_nge_f32_e32 vcc_lo, 0, v43                           // 000000000C54: 7C125680
	v_fmac_f32_e32 v41, v2, v3                                 // 000000000C58: 56520702
	v_fma_f32 v37, -v10, v45, v39                              // 000000000C5C: D54B0025 249E5B0A
	v_fma_f32 v38, -v11, v45, v42                              // 000000000C64: D54B0026 24AA5B0B
	v_fma_f32 v43, -v12, v45, v44                              // 000000000C6C: D54B002B 24B25B0C
	v_cndmask_b32_e32 v39, v37, v39, vcc_lo                    // 000000000C74: 024E4F25
	v_cndmask_b32_e32 v38, v38, v42, vcc_lo                    // 000000000C78: 024C5526
	v_cndmask_b32_e32 v37, v43, v44, vcc_lo                    // 000000000C7C: 024A592B
_L24:
	s_or_b64 exec, exec, s[8:9]                                // 000000000C80: 88FE087E
	buffer_load_dwordx3 v[43:45], v35, s[28:31], 0 offen offset:16// 000000000C84: E03C1010 80072B23
	s_waitcnt vmcnt(1)                                         // 000000000C8C: BF8C3F71
	v_mul_f32_e32 v0, v38, v11                                 // 000000000C90: 10001726
	v_mul_f32_e32 v1, 0.5, v28                                 // 000000000C94: 100238F0
	v_mul_f32_e32 v2, 0.5, v29                                 // 000000000C98: 10043AF0
	v_mul_f32_e32 v3, 0.5, v30                                 // 000000000C9C: 10063CF0
	s_cmp_gt_u32 s43, 3                                        // 000000000CA0: BF08832B
	v_fmac_f32_e32 v0, v39, v10                                // 000000000CA4: 56001527
	v_mul_f32_e32 v1, v1, v40                                  // 000000000CA8: 10025101
	v_mul_f32_e32 v2, v2, v36                                  // 000000000CAC: 10044902
	v_mul_f32_e32 v3, v3, v41                                  // 000000000CB0: 10065303
	s_cselect_b64 s[8:9], -1, 0                                // 000000000CB4: 858880C1
	v_fmac_f32_e32 v0, v37, v12                                // 000000000CB8: 56001925
	s_mov_b64 s[14:15], -1                                     // 000000000CBC: BE8E04C1
	s_mov_b64 s[10:11], -1                                     // 000000000CC0: BE8A04C1
	v_max_f32_e32 v0, 0, v0                                    // 000000000CC4: 20000080
	v_mul_f32_e32 v40, v1, v0                                  // 000000000CC8: 10500101
	v_mul_f32_e32 v41, v2, v0                                  // 000000000CCC: 10520102
	v_mul_f32_e32 v42, v3, v0                                  // 000000000CD0: 10540103
	v_max3_f32 v0, v40, v41, v42                               // 000000000CD4: D5540000 04AA5328
	v_cmp_gt_f32_e32 vcc_lo, 0x3dcccccd, v0                    // 000000000CDC: 7C0800FF 3DCCCCCD
	s_and_b64 s[16:17], vcc, s[8:9]                            // 000000000CE4: 8790086A
	s_mov_b64 s[8:9], -1                                       // 000000000CE8: BE8804C1
	s_waitcnt vmcnt(0)                                         // 000000000CEC: BF8C3F70
	v_fma_f32 v3, v28, v43, v25                                // 000000000CF0: D54B0003 0466571C
	v_fma_f32 v35, v29, v44, v26                               // 000000000CF8: D54B0023 046A591D
	v_fma_f32 v36, v30, v45, v27                               // 000000000D00: D54B0024 046E5B1E
	s_and_saveexec_b64 s[12:13], s[16:17]                      // 000000000D08: BE8C2410
	s_cbranch_execz _L26                                       // 000000000D0C: BF88001A
	v_lshrrev_b32_e32 v1, 16, v34                              // 000000000D10: 2C024490
	s_mov_b64 s[14:15], 0                                      // 000000000D14: BE8E0480
	s_mov_b64 s[10:11], exec                                   // 000000000D18: BE8A047E
	v_xor3_b32 v1, v34, v1, 61                                 // 000000000D1C: D5780001 02F60322
	v_lshl_add_u32 v1, v1, 3, v1                               // 000000000D24: D7460001 04050701
	v_lshrrev_b32_e32 v2, 4, v1                                // 000000000D2C: 2C040284
	v_xor_b32_e32 v1, v2, v1                                   // 000000000D30: 3A020302
	v_mul_lo_u32 v1, 0x27d4eb2d, v1                            // 000000000D34: D5690001 000202FF 27D4EB2D
	v_lshrrev_b32_e32 v2, 15, v1                               // 000000000D40: 2C04028F
	v_xor_b32_e32 v34, v2, v1                                  // 000000000D44: 3A440302
	v_cvt_f32_u32_e32 v1, v34                                  // 000000000D48: 7E020D22
	v_mul_f32_e32 v1, 0x2f800000, v1                           // 000000000D4C: 100202FF 2F800000
	v_cmpx_ngt_f32_e32 v1, v0                                  // 000000000D54: 7C360101
	v_rcp_f32_e32 v0, v0                                       // 000000000D58: 7E005500
	s_mov_b64 s[14:15], exec                                   // 000000000D5C: BE8E047E
	v_mul_f32_e32 v40, v0, v40                                 // 000000000D60: 10505100
	v_mul_f32_e32 v41, v0, v41                                 // 000000000D64: 10525300
	v_mul_f32_e32 v42, v0, v42                                 // 000000000D68: 10545500
	s_or_b64 exec, exec, s[10:11]                              // 000000000D6C: 88FE0A7E
	s_xor_b64 s[10:11], exec, -1                               // 000000000D70: 898AC17E
	s_orn2_b64 s[14:15], s[14:15], exec                        // 000000000D74: 8B8E7E0E
_L26:
	s_or_b64 exec, exec, s[12:13]                              // 000000000D78: 88FE0C7E
	s_and_saveexec_b64 s[12:13], s[14:15]                      // 000000000D7C: BE8C240E
	s_cbranch_execz _L27                                       // 000000000D80: BF88FD57
	s_add_i32 s16, s43, 1                                      // 000000000D84: 8110812B
	v_fmamk_f32 v0, v10, 0x3a83126f, v13                       // 000000000D88: 58001B0A 3A83126F
	s_cmp_ge_u32 s16, s37                                      // 000000000D90: BF092510
	v_fmamk_f32 v1, v11, 0x3a83126f, v14                       // 000000000D94: 58021D0B 3A83126F
	v_fmamk_f32 v2, v12, 0x3a83126f, v15                       // 000000000D9C: 58041F0C 3A83126F
	s_cselect_b64 s[8:9], -1, 0                                // 000000000DA4: 858880C1
	s_andn2_b64 s[10:11], s[10:11], exec                       // 000000000DA8: 8A8A7E0A
	s_orn2_b64 s[8:9], s[8:9], exec                            // 000000000DAC: 8B887E08
	s_branch _L27                                              // 000000000DB0: BF82FD4B
_L6:
	s_or_b64 exec, exec, s[4:5]                                // 000000000DB4: 88FE047E
	s_and_saveexec_b64 s[0:1], s[2:3]                          // 000000000DB8: BE802402
	s_xor_b64 s[0:1], exec, s[0:1]                             // 000000000DBC: 8980007E
	s_cbranch_execz _L28                                       // 000000000DC0: BF88FCCD
	v_cmp_lt_f32_e64 s2, 0x6f800000, |v32|                     // 000000000DC4: D4010202 000240FF 6F800000
	v_cmp_eq_f32_e32 vcc_lo, v32, v31                          // 000000000DD0: 7C043F20
	s_clause 0x1                                               // 000000000DD4: BFA10001
	s_load_dwordx8 s[4:11], s[34:35], 0x70                     // 000000000DD8: F40C0111 FA000070
	s_load_dwordx4 s[12:15], s[34:35], 0x20                    // 000000000DE0: F4080311 FA000020
	v_cndmask_b32_e64 v0, 1.0, 0x2f800000, s2                  // 000000000DE8: D5010000 0009FEF2 2F800000
	v_cndmask_b32_e64 v2, -1.0, 1.0, vcc_lo                    // 000000000DF4: D5010002 01A9E4F3
	v_cmp_eq_f32_e64 vcc_lo, |v32|, |v31|                      // 000000000DFC: D402036A 00023F20
	v_mul_f32_e32 v1, v32, v0                                  // 000000000E04: 10020120
	v_rcp_f32_e32 v1, v1                                       // 000000000E08: 7E025501
	v_mul_f32_e32 v1, v31, v1                                  // 000000000E0C: 1002031F
	v_mul_f32_e32 v0, v0, v1                                   // 000000000E10: 10000300
	v_cndmask_b32_e32 v0, v0, v2, vcc_lo                       // 000000000E14: 02000500
	v_max_f32_e64 v1, |v0|, 1.0                                // 000000000E18: D5100101 0001E500
	v_min_f32_e64 v2, |v0|, 1.0                                // 000000000E20: D50F0102 0001E500
	v_cmp_nlt_f32_e32 vcc_lo, 0, v0                            // 000000000E28: 7C1C0080
	v_cmp_gt_f32_e64 s2, |v0|, 1.0                             // 000000000E2C: D4040102 0001E500
	v_rcp_f32_e32 v1, v1                                       // 000000000E34: 7E025501
	v_mul_f32_e32 v1, v2, v1                                   // 000000000E38: 10020302
	v_mul_f32_e32 v2, v1, v1                                   // 000000000E3C: 10040301
	v_mul_f32_e32 v3, v2, v1                                   // 000000000E40: 10060302
	v_mul_f32_e32 v1, 0x3f7ffea5, v1                           // 000000000E44: 100202FF 3F7FFEA5
	v_fmaak_f32 v34, s41, v2, 0x3d5be101                       // 000000000E4C: 5A440429 3D5BE101
	v_mul_f32_e32 v35, v3, v2                                  // 000000000E54: 10460503
	v_fmac_f32_e32 v1, 0xbeaa5476, v3                          // 000000000E58: 560206FF BEAA5476
	v_fmaak_f32 v3, v34, v2, 0xbdf0555d                        // 000000000E60: 5A060522 BDF0555D
	v_mul_f32_e32 v2, v35, v2                                  // 000000000E68: 10040523
	v_fmac_f32_e32 v1, 0x3e468bc1, v35                         // 000000000E6C: 560246FF 3E468BC1
	v_fmac_f32_e32 v1, v2, v3                                  // 000000000E74: 56020702
	v_cndmask_b32_e32 v2, 1.0, v0, vcc_lo                      // 000000000E78: 020400F2
	v_cndmask_b32_e64 v3, 0, 1.0, s2                           // 000000000E7C: D5010003 0009E480
	v_fmaak_f32 v34, -2.0, v1, 0x3fc90fdb                      // 000000000E84: 5A4402F5 3FC90FDB
	v_cmp_le_f32_e32 vcc_lo, 0, v2                             // 000000000E8C: 7C060480
	v_fmac_f32_e32 v1, v34, v3                                 // 000000000E90: 56020722
	v_cndmask_b32_e32 v2, -1.0, v2, vcc_lo                     // 000000000E94: 020404F3
	v_cmp_nlt_f32_e32 vcc_lo, 0, v31                           // 000000000E98: 7C1C3E80
	v_fma_f32 v34, |v33|, s42, 0x3da68d87                      // 000000000E9C: D54B0122 03FC5521 3DA68D87
	v_mul_f32_e32 v1, v1, v2                                   // 000000000EA8: 10020501
	v_cndmask_b32_e32 v3, 1.0, v31, vcc_lo                     // 000000000EAC: 02063EF2
	v_sub_f32_e64 v2, 1.0, |v33|                               // 000000000EB0: D5040202 000242F2
	v_fma_f32 v34, v34, |v33|, 0xbe5bc094                      // 000000000EB8: D54B0222 03FE4322 BE5BC094
	v_cmp_le_f32_e32 vcc_lo, 0, v3                             // 000000000EC4: 7C060680
	v_sqrt_f32_e32 v2, v2                                      // 000000000EC8: 7E046702
	v_cndmask_b32_e32 v3, -1.0, v3, vcc_lo                     // 000000000ECC: 020606F3
	v_cmp_o_f32_e32 vcc_lo, v0, v0                             // 000000000ED0: 7C0E0100
	v_cndmask_b32_e32 v0, 0x7fc00000, v1, vcc_lo               // 000000000ED4: 020002FF 7FC00000
	v_cmp_nlt_f32_e32 vcc_lo, 0, v33                           // 000000000EDC: 7C1C4280
	v_fmamk_f32 v35, v3, 0x40490fdb, v0                        // 000000000EE0: 58460103 40490FDB
	v_cndmask_b32_e32 v1, 1.0, v33, vcc_lo                     // 000000000EE8: 020242F2
	v_cmp_gt_f32_e32 vcc_lo, 0, v32                            // 000000000EEC: 7C084080
	v_mul_f32_e32 v3, 0x3fc90fdb, v3                           // 000000000EF0: 100606FF 3FC90FDB
	v_fma_f32 v33, v34, |v33|, 0x3fc90fdb                      // 000000000EF8: D54B0221 03FE4322 3FC90FDB
	v_mov_b32_e32 v34, v24                                     // 000000000F04: 7E440318
	v_mul_f32_e32 v36, 0x3ea2f983, v1                          // 000000000F08: 104802FF 3EA2F983
	v_cndmask_b32_e32 v0, v0, v35, vcc_lo                      // 000000000F10: 02004700
	v_cmp_lt_f32_e32 vcc_lo, 0, v32                            // 000000000F14: 7C024080
	v_fmaak_f32 v2, v33, v2, 0xbfc90fdb                        // 000000000F18: 5A040521 BFC90FDB
	v_cndmask_b32_e64 v35, 0x40490fdb, 0, vcc_lo               // 000000000F20: D5010023 01A900FF 40490FDB
	v_cmp_neq_f32_e32 vcc_lo, 0, v32                           // 000000000F2C: 7C1A4080
	v_cndmask_b32_e32 v0, v3, v0, vcc_lo                       // 000000000F30: 02000103
	v_cmp_le_f32_e32 vcc_lo, 0, v1                             // 000000000F34: 7C060280
	v_cndmask_b32_e32 v1, 0xbea2f983, v36, vcc_lo              // 000000000F38: 020248FF BEA2F983
	v_cmp_neq_f32_e32 vcc_lo, 0, v31                           // 000000000F40: 7C1A3E80
	v_fma_f32 v1, v1, v2, 0.5                                  // 000000000F44: D54B0001 03C20501
	v_cndmask_b32_e32 v0, v35, v0, vcc_lo                      // 000000000F4C: 02000123
	v_fma_f32 v0, v0, 0.15915494, 0.5                          // 000000000F50: D54B0000 03C1F100
	s_waitcnt lgkmcnt(0)                                       // 000000000F58: BF8CC07F
	image_sample_lz v[0:2], v[0:1], s[4:11], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000F5C: F09C0708 00610000
	s_waitcnt vmcnt(0)                                         // 000000000F64: BF8C3F70
	v_fmac_f32_e32 v27, v2, v30                                // 000000000F68: 56363D02
	v_fmac_f32_e32 v26, v1, v29                                // 000000000F6C: 56343B01
	v_fmac_f32_e32 v25, v0, v28                                // 000000000F70: 56323900
	v_mov_b32_e32 v36, v27                                     // 000000000F74: 7E48031B
	v_mov_b32_e32 v35, v26                                     // 000000000F78: 7E46031A
	v_mov_b32_e32 v3, v25                                      // 000000000F7C: 7E060319
	s_branch _L28                                              // 000000000F80: BF82FC5D
_L4:
	v_mov_b32_e32 v36, 0                                       // 000000000F84: 7E480280
	v_mov_b32_e32 v35, 0                                       // 000000000F88: 7E460280
	v_mov_b32_e32 v3, 0                                        // 000000000F8C: 7E060280
	s_branch _L29                                              // 000000000F90: BF82FC5A
_L3:
	v_cvt_f32_u32_e32 v1, s39                                  // 000000000F94: 7E020C27
_L1:
	s_waitcnt lgkmcnt(0)                                       // 000000000F98: BF8CC07F
	s_load_dwordx8 s[0:7], s[34:35], 0x30                      // 000000000F9C: F40C0011 FA000030
	v_rcp_f32_e32 v0, v1                                       // 000000000FA4: 7E005501
	s_load_dwordx8 s[8:15], s[34:35], null                     // 000000000FA8: F40C0211 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000FB0: BF8CC07F
	image_load v[9:12], v[4:5], s[0:7] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000000FB4: F0001F08 00000904
	s_waitcnt vmcnt(0)                                         // 000000000FBC: BF8C3F70
	v_add_f32_e32 v12, 1.0, v12                                // 000000000FC0: 061818F2
	v_fma_f32 v2, v0, v6, -v9                                  // 000000000FC4: D54B0002 84260D00
	v_fma_f32 v3, v0, v8, -v10                                 // 000000000FCC: D54B0003 842A1100
	v_fma_f32 v0, v0, v7, -v11                                 // 000000000FD4: D54B0000 842E0F00
	v_rcp_f32_e32 v1, v12                                      // 000000000FDC: 7E02550C
	v_fma_f32 v9, v1, v2, v9                                   // 000000000FE0: D54B0009 04260501
	v_fma_f32 v10, v1, v3, v10                                 // 000000000FE8: D54B000A 042A0701
	v_fmac_f32_e32 v11, v1, v0                                 // 000000000FF0: 56160101
	v_mov_b32_e32 v3, 1.0                                      // 000000000FF4: 7E0602F2
	v_add_f32_e32 v0, 1.0, v9                                  // 000000000FF8: 060012F2
	v_add_f32_e32 v1, 1.0, v10                                 // 000000000FFC: 060214F2
	v_add_f32_e32 v2, 1.0, v11                                 // 000000001000: 060416F2
	v_rcp_f32_e32 v0, v0                                       // 000000001004: 7E005500
	v_rcp_f32_e32 v1, v1                                       // 000000001008: 7E025501
	v_rcp_f32_e32 v2, v2                                       // 00000000100C: 7E045502
	v_mul_f32_e32 v0, v9, v0                                   // 000000001010: 10000109
	v_mul_f32_e32 v1, v10, v1                                  // 000000001014: 1002030A
	v_mul_f32_e32 v2, v11, v2                                  // 000000001018: 1004050B
	v_log_f32_e32 v0, v0                                       // 00000000101C: 7E004F00
	v_log_f32_e32 v1, v1                                       // 000000001020: 7E024F01
	v_log_f32_e32 v2, v2                                       // 000000001024: 7E044F02
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 000000001028: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 000000001030: 0E0202FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 000000001038: 0E0404FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 000000001040: 7E004B00
	v_exp_f32_e32 v1, v1                                       // 000000001044: 7E024B01
	v_exp_f32_e32 v2, v2                                       // 000000001048: 7E044B02
	image_store v[0:3], v[4:5], s[8:15] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 00000000104C: F0201F08 00020004
	image_store v[9:12], v[4:5], s[0:7] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000001054: F0201F08 00000904
_L0:
	s_endpgm                                                   // 00000000105C: BF810000

_amdgpu_cs_main:
	v_lshl_add_u32 v11, s2, 3, v0                              // 000000000000: D746000B 04010602
	v_lshl_add_u32 v12, s3, 3, v1                              // 000000000008: D746000C 04050603
	s_mov_b64 s[2:3], exec                                     // 000000000010: BE82047E
	v_or_b32_e32 v0, v11, v12                                  // 000000000014: 3800190B
	v_cmpx_gt_u32_e32 64, v0                                   // 000000000018: 7DA800C0
	s_cbranch_execz _L0                                        // 00000000001C: BF880255
	s_getpc_b64 s[2:3]                                         // 000000000020: BE821F00
	s_mov_b32 s0, s1                                           // 000000000024: BE800301
	s_mov_b32 s1, s3                                           // 000000000028: BE810303
	v_cvt_f32_u32_e32 v0, v11                                  // 00000000002C: 7E000D0B
	s_load_dwordx8 s[0:7], s[0:1], null                        // 000000000030: F40C0000 FA000000
	s_mov_b32 s9, 0x3fcb2cb4                                   // 000000000038: BE8903FF 3FCB2CB4
	v_cvt_f32_u32_e32 v1, v12                                  // 000000000040: 7E020D0C
	v_fmaak_f32 v5, s9, v0, 0xc2480000                         // 000000000044: 5A0A0009 C2480000
	v_fmaak_f32 v7, s9, v1, 0xc2480000                         // 00000000004C: 5A0E0209 C2480000
	s_waitcnt lgkmcnt(0)                                       // 000000000054: BF8CC07F
	s_buffer_load_dword s8, s[0:3], 0xc                        // 000000000058: F4200200 FA00000C
	s_waitcnt lgkmcnt(0)                                       // 000000000060: BF8CC07F
	v_mul_f32_e64 v14, 0x3dcccccd, s8                          // 000000000064: D508000E 000010FF 3DCCCCCD
	v_mul_f32_e64 v3, 0x3d4ccccd, s8                           // 000000000070: D5080003 000010FF 3D4CCCCD
	v_fmamk_f32 v2, v5, 0x3c23d70a, v14                        // 00000000007C: 58041D05 3C23D70A
	v_fmamk_f32 v6, v7, 0x3c23d70a, v14                        // 000000000084: 580C1D07 3C23D70A
	v_floor_f32_e32 v8, v14                                    // 00000000008C: 7E10490E
	v_fmamk_f32 v10, v5, 0x3d4ccccd, v3                        // 000000000090: 58140705 3D4CCCCD
	v_fract_f32_e32 v9, v14                                    // 000000000098: 7E12410E
	v_floor_f32_e32 v4, v2                                     // 00000000009C: 7E084902
	v_fmamk_f32 v13, v7, 0x3d4ccccd, v3                        // 0000000000A0: 581A0707 3D4CCCCD
	v_floor_f32_e32 v15, v3                                    // 0000000000A8: 7E1E4903
	v_floor_f32_e32 v16, v6                                    // 0000000000AC: 7E204906
	v_fract_f32_e32 v2, v2                                     // 0000000000B0: 7E044102
	v_floor_f32_e32 v18, v10                                   // 0000000000B4: 7E24490A
	v_fmamk_f32 v8, v8, 0x42640000, v4                         // 0000000000B8: 58100908 42640000
	v_mul_f32_e32 v17, v9, v9                                  // 0000000000C0: 10221309
	v_fmaak_f32 v9, -2.0, v9, 0x40400000                       // 0000000000C4: 5A1212F5 40400000
	v_floor_f32_e32 v19, v13                                   // 0000000000CC: 7E26490D
	v_mul_f32_e32 v20, v2, v2                                  // 0000000000D0: 10280502
	v_fmaak_f32 v2, -2.0, v2, 0x40400000                       // 0000000000D4: 5A0404F5 40400000
	v_fmac_f32_e32 v18, 0x42640000, v15                        // 0000000000DC: 56241EFF 42640000
	v_fmac_f32_e32 v8, 0x42e20000, v16                         // 0000000000E4: 561020FF 42E20000
	v_fract_f32_e32 v10, v10                                   // 0000000000EC: 7E14410A
	v_mul_f32_e32 v9, v17, v9                                  // 0000000000F0: 10121311
	v_mul_f32_e32 v2, v20, v2                                  // 0000000000F4: 10040514
	v_fmac_f32_e32 v18, 0x42e20000, v19                        // 0000000000F8: 562426FF 42E20000
	v_mul_f32_e32 v15, 0.15915494, v8                          // 000000000100: 101E10F8
	v_add_f32_e32 v17, 1.0, v8                                 // 000000000104: 062210F2
	v_add_f32_e32 v19, 0x42640000, v8                          // 000000000108: 062610FF 42640000
	v_add_f32_e32 v20, 0x42680000, v8                          // 000000000110: 062810FF 42680000
	v_mul_f32_e32 v21, v10, v10                                // 000000000118: 102A150A
	v_fmaak_f32 v10, -2.0, v10, 0x40400000                     // 00000000011C: 5A1414F5 40400000
	v_sin_f32_e32 v15, v15                                     // 000000000124: 7E1E6B0F
	v_mul_f32_e32 v17, 0.15915494, v17                         // 000000000128: 102222F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 00000000012C: 102626F8
	v_mul_f32_e32 v20, 0.15915494, v20                         // 000000000130: 102828F8
	v_mul_f32_e32 v10, v21, v10                                // 000000000134: 10141515
	v_add_f32_e32 v21, 0x42e20000, v8                          // 000000000138: 062A10FF 42E20000
	v_add_f32_e32 v22, 0x42e40000, v8                          // 000000000140: 062C10FF 42E40000
	v_add_f32_e32 v23, 0x432a0000, v8                          // 000000000148: 062E10FF 432A0000
	v_add_f32_e32 v8, 0x432b0000, v8                           // 000000000150: 061010FF 432B0000
	v_sin_f32_e32 v17, v17                                     // 000000000158: 7E226B11
	v_sin_f32_e32 v19, v19                                     // 00000000015C: 7E266B13
	v_sin_f32_e32 v20, v20                                     // 000000000160: 7E286B14
	v_mul_f32_e32 v21, 0.15915494, v21                         // 000000000164: 102A2AF8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000168: 102C2CF8
	v_mul_f32_e32 v23, 0.15915494, v23                         // 00000000016C: 102E2EF8
	v_mul_f32_e32 v8, 0.15915494, v8                           // 000000000170: 101010F8
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 000000000174: 101E1EFF 472AEE8C
	v_sin_f32_e32 v21, v21                                     // 00000000017C: 7E2A6B15
	v_sin_f32_e32 v22, v22                                     // 000000000180: 7E2C6B16
	v_sin_f32_e32 v23, v23                                     // 000000000184: 7E2E6B17
	v_sin_f32_e32 v8, v8                                       // 000000000188: 7E106B08
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 00000000018C: 102222FF 472AEE8C
	v_fract_f32_e32 v26, v15                                   // 000000000194: 7E34410F
	v_mul_f32_e32 v15, 0x472aee8c, v19                         // 000000000198: 101E26FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v20                         // 0000000001A0: 102628FF 472AEE8C
	v_mul_f32_e32 v24, 0.15915494, v18                         // 0000000001A8: 103024F8
	v_fract_f32_e32 v17, v17                                   // 0000000001AC: 7E224111
	v_mul_f32_e32 v20, 0x472aee8c, v21                         // 0000000001B0: 10282AFF 472AEE8C
	v_fract_f32_e32 v15, v15                                   // 0000000001B8: 7E1E410F
	v_fract_f32_e32 v19, v19                                   // 0000000001BC: 7E264113
	v_mul_f32_e32 v21, 0x472aee8c, v22                         // 0000000001C0: 102A2CFF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v23                         // 0000000001C8: 102C2EFF 472AEE8C
	v_sub_f32_e32 v17, v17, v26                                // 0000000001D0: 08223511
	v_mul_f32_e32 v8, 0x472aee8c, v8                           // 0000000001D4: 101010FF 472AEE8C
	v_sub_f32_e32 v19, v19, v15                                // 0000000001DC: 08261F13
	v_fract_f32_e32 v20, v20                                   // 0000000001E0: 7E284114
	v_fract_f32_e32 v21, v21                                   // 0000000001E4: 7E2A4115
	v_fmac_f32_e32 v26, v17, v2                                // 0000000001E8: 56340511
	v_fract_f32_e32 v22, v22                                   // 0000000001EC: 7E2C4116
	v_fract_f32_e32 v8, v8                                     // 0000000001F0: 7E104108
	v_fmac_f32_e32 v15, v19, v2                                // 0000000001F4: 561E0513
	v_sub_f32_e32 v21, v21, v20                                // 0000000001F8: 082A2915
	v_add_f32_e32 v25, 1.0, v18                                // 0000000001FC: 063224F2
	v_add_f32_e32 v17, 0x42640000, v18                         // 000000000200: 062224FF 42640000
	v_sub_f32_e32 v8, v8, v22                                  // 000000000208: 08102D08
	v_sub_f32_e32 v15, v15, v26                                // 00000000020C: 081E350F
	v_add_f32_e32 v19, 0x42680000, v18                         // 000000000210: 062624FF 42680000
	v_fmac_f32_e32 v20, v21, v2                                // 000000000218: 56280515
	v_add_f32_e32 v21, 0x42e20000, v18                         // 00000000021C: 062A24FF 42E20000
	v_fmac_f32_e32 v22, v8, v2                                 // 000000000224: 562C0508
	v_add_f32_e32 v8, 0x42e40000, v18                          // 000000000228: 061024FF 42E40000
	v_fmac_f32_e32 v26, v15, v9                                // 000000000230: 5634130F
	v_add_f32_e32 v15, 0x432a0000, v18                         // 000000000234: 061E24FF 432A0000
	v_add_f32_e32 v18, 0x432b0000, v18                         // 00000000023C: 062424FF 432B0000
	v_mul_f32_e32 v23, 0.15915494, v25                         // 000000000244: 102E32F8
	v_mul_f32_e32 v17, 0.15915494, v17                         // 000000000248: 102222F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 00000000024C: 102626F8
	v_mul_f32_e32 v21, 0.15915494, v21                         // 000000000250: 102A2AF8
	v_mul_f32_e32 v8, 0.15915494, v8                           // 000000000254: 101010F8
	v_mul_f32_e32 v15, 0.15915494, v15                         // 000000000258: 101E1EF8
	v_mul_f32_e32 v18, 0.15915494, v18                         // 00000000025C: 102424F8
	v_sin_f32_e32 v24, v24                                     // 000000000260: 7E306B18
	v_sin_f32_e32 v23, v23                                     // 000000000264: 7E2E6B17
	v_sin_f32_e32 v17, v17                                     // 000000000268: 7E226B11
	v_sin_f32_e32 v19, v19                                     // 00000000026C: 7E266B13
	v_sin_f32_e32 v21, v21                                     // 000000000270: 7E2A6B15
	v_sin_f32_e32 v8, v8                                       // 000000000274: 7E106B08
	v_sin_f32_e32 v15, v15                                     // 000000000278: 7E1E6B0F
	v_sin_f32_e32 v18, v18                                     // 00000000027C: 7E246B12
	v_fract_f32_e32 v3, v3                                     // 000000000280: 7E064103
	v_fract_f32_e32 v6, v6                                     // 000000000284: 7E0C4106
	v_mul_f32_e32 v24, 0x472aee8c, v24                         // 000000000288: 103030FF 472AEE8C
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 000000000290: 102E2EFF 472AEE8C
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 000000000298: 102222FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 0000000002A0: 102626FF 472AEE8C
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 0000000002A8: 102A2AFF 472AEE8C
	v_mul_f32_e32 v8, 0x472aee8c, v8                           // 0000000002B0: 101010FF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 0000000002B8: 101E1EFF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 0000000002C0: 102424FF 472AEE8C
	v_fract_f32_e32 v24, v24                                   // 0000000002C8: 7E304118
	v_fract_f32_e32 v23, v23                                   // 0000000002CC: 7E2E4117
	v_fract_f32_e32 v17, v17                                   // 0000000002D0: 7E224111
	v_fract_f32_e32 v19, v19                                   // 0000000002D4: 7E264113
	v_fract_f32_e32 v21, v21                                   // 0000000002D8: 7E2A4115
	v_fract_f32_e32 v8, v8                                     // 0000000002DC: 7E104108
	v_fract_f32_e32 v15, v15                                   // 0000000002E0: 7E1E410F
	v_fract_f32_e32 v18, v18                                   // 0000000002E4: 7E244112
	v_sub_f32_e32 v23, v23, v24                                // 0000000002E8: 082E3117
	v_sub_f32_e32 v19, v19, v17                                // 0000000002EC: 08262313
	v_sub_f32_e32 v8, v8, v21                                  // 0000000002F0: 08102B08
	v_mul_f32_e32 v16, 0x42e20000, v16                         // 0000000002F4: 102020FF 42E20000
	v_sub_f32_e32 v18, v18, v15                                // 0000000002FC: 08241F12
	v_fmac_f32_e32 v24, v23, v10                               // 000000000300: 56301517
	v_fmac_f32_e32 v17, v19, v10                               // 000000000304: 56221513
	v_mul_f32_e32 v19, v3, v3                                  // 000000000308: 10260703
	v_fmaak_f32 v3, -2.0, v3, 0x40400000                       // 00000000030C: 5A0606F5 40400000
	v_fmac_f32_e32 v21, v8, v10                                // 000000000314: 562A1508
	v_fmac_f32_e32 v15, v18, v10                               // 000000000318: 561E1512
	v_sub_f32_e32 v8, v22, v20                                 // 00000000031C: 08102916
	v_fract_f32_e32 v10, v13                                   // 000000000320: 7E14410D
	v_mul_f32_e32 v3, v19, v3                                  // 000000000324: 10060713
	v_sub_f32_e32 v13, v17, v24                                // 000000000328: 081A3111
	v_sub_f32_e32 v15, v15, v21                                // 00000000032C: 081E2B0F
	v_mul_f32_e32 v23, v6, v6                                  // 000000000330: 102E0D06
	v_fmaak_f32 v6, -2.0, v6, 0x40400000                       // 000000000334: 5A0C0CF5 40400000
	v_fmac_f32_e32 v20, v8, v9                                 // 00000000033C: 56281308
	v_mul_f32_e32 v8, v10, v10                                 // 000000000340: 1010150A
	v_fmaak_f32 v9, -2.0, v10, 0x40400000                      // 000000000344: 5A1214F5 40400000
	v_fmac_f32_e32 v24, v13, v3                                // 00000000034C: 5630070D
	v_fmac_f32_e32 v21, v15, v3                                // 000000000350: 562A070F
	v_mul_f32_e32 v13, v23, v6                                 // 000000000354: 101A0D17
	v_sub_f32_e32 v3, v20, v26                                 // 000000000358: 08063514
	v_mul_f32_e32 v6, v8, v9                                   // 00000000035C: 100C1308
	v_add_nc_u32_e32 v9, -1, v11                               // 000000000360: 4A1216C1
	v_sub_f32_e32 v8, v21, v24                                 // 000000000364: 08103115
	v_add_nc_u32_e32 v10, -1, v12                              // 000000000368: 4A1418C1
	v_fmac_f32_e32 v26, v3, v13                                // 00000000036C: 56341B03
	v_mul_f32_e32 v17, 0x3c23d70a, v5                          // 000000000370: 10220AFF 3C23D70A
	v_mul_f32_e32 v15, 0x3c23d70a, v7                          // 000000000378: 101E0EFF 3C23D70A
	v_fma_f32 v6, v8, v6, v24 mul:2                            // 000000000380: D54B0006 0C620D08
	v_max_u32_e32 v3, v9, v10                                  // 000000000388: 28061509
	v_mov_b32_e32 v8, 0                                        // 00000000038C: 7E100280
	v_mov_b32_e32 v9, 1.0                                      // 000000000390: 7E1202F2
	v_fmac_f32_e32 v6, 0x41200000, v26                         // 000000000394: 560C34FF 41200000
	v_cmp_gt_u32_e32 vcc_lo, 62, v3                            // 00000000039C: 7D8806BE
	v_mov_b32_e32 v3, 1.0                                      // 0000000003A0: 7E0602F2
	v_mov_b32_e32 v10, v8                                      // 0000000003A4: 7E140308
	s_and_saveexec_b64 s[8:9], vcc                             // 0000000003A8: BE88246A
	s_cbranch_execz _L1                                        // 0000000003AC: BF88014D
	v_add_f32_e32 v8, 0xbc23d70a, v14                          // 0000000003B0: 06101CFF BC23D70A
	v_fmamk_f32 v9, v6, 0x3c23d70a, v14                        // 0000000003B8: 58121D06 3C23D70A
	v_add_f32_e32 v14, 0x3c23d70a, v14                         // 0000000003C0: 061C1CFF 3C23D70A
	v_add_f32_e32 v10, v8, v17                                 // 0000000003C8: 06142308
	v_floor_f32_e32 v18, v9                                    // 0000000003CC: 7E244909
	v_add_f32_e32 v17, v14, v17                                // 0000000003D0: 0622230E
	v_fract_f32_e32 v9, v9                                     // 0000000003D4: 7E124109
	v_add_f32_e32 v8, v8, v15                                  // 0000000003D8: 06101F08
	v_floor_f32_e32 v19, v10                                   // 0000000003DC: 7E26490A
	v_fract_f32_e32 v10, v10                                   // 0000000003E0: 7E14410A
	v_floor_f32_e32 v29, v17                                   // 0000000003E4: 7E3A4911
	v_mul_f32_e32 v20, v9, v9                                  // 0000000003E8: 10281309
	v_fmaak_f32 v9, -2.0, v9, 0x40400000                       // 0000000003EC: 5A1212F5 40400000
	v_fmac_f32_e32 v19, 0x42640000, v18                        // 0000000003F4: 562624FF 42640000
	v_mul_f32_e32 v21, v10, v10                                // 0000000003FC: 102A150A
	v_fmaak_f32 v10, -2.0, v10, 0x40400000                     // 000000000400: 5A1414F5 40400000
	v_fmac_f32_e32 v29, 0x42640000, v18                        // 000000000408: 563A24FF 42640000
	v_mul_f32_e32 v9, v20, v9                                  // 000000000410: 10121314
	v_add_f32_e32 v19, v19, v16                                // 000000000414: 06262113
	v_fract_f32_e32 v17, v17                                   // 000000000418: 7E224111
	v_mul_f32_e32 v10, v21, v10                                // 00000000041C: 10141515
	v_add_f32_e32 v16, v29, v16                                // 000000000420: 0620211D
	v_fmac_f32_e32 v4, 0x42640000, v18                         // 000000000424: 560824FF 42640000
	v_mul_f32_e32 v22, 0.15915494, v19                         // 00000000042C: 102C26F8
	v_add_f32_e32 v23, 1.0, v19                                // 000000000430: 062E26F2
	v_add_f32_e32 v24, 0x42640000, v19                         // 000000000434: 063026FF 42640000
	v_add_f32_e32 v25, 0x42680000, v19                         // 00000000043C: 063226FF 42680000
	v_add_f32_e32 v26, 0x42e20000, v19                         // 000000000444: 063426FF 42E20000
	v_add_f32_e32 v27, 0x42e40000, v19                         // 00000000044C: 063626FF 42E40000
	v_add_f32_e32 v28, 0x432a0000, v19                         // 000000000454: 063826FF 432A0000
	v_add_f32_e32 v19, 0x432b0000, v19                         // 00000000045C: 062626FF 432B0000
	v_sin_f32_e32 v22, v22                                     // 000000000464: 7E2C6B16
	v_mul_f32_e32 v23, 0.15915494, v23                         // 000000000468: 102E2EF8
	v_mul_f32_e32 v24, 0.15915494, v24                         // 00000000046C: 103030F8
	v_mul_f32_e32 v25, 0.15915494, v25                         // 000000000470: 103232F8
	v_mul_f32_e32 v26, 0.15915494, v26                         // 000000000474: 103434F8
	v_mul_f32_e32 v27, 0.15915494, v27                         // 000000000478: 103636F8
	v_mul_f32_e32 v28, 0.15915494, v28                         // 00000000047C: 103838F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000480: 102626F8
	v_sin_f32_e32 v23, v23                                     // 000000000484: 7E2E6B17
	v_sin_f32_e32 v24, v24                                     // 000000000488: 7E306B18
	v_sin_f32_e32 v25, v25                                     // 00000000048C: 7E326B19
	v_sin_f32_e32 v26, v26                                     // 000000000490: 7E346B1A
	v_sin_f32_e32 v27, v27                                     // 000000000494: 7E366B1B
	v_sin_f32_e32 v28, v28                                     // 000000000498: 7E386B1C
	v_sin_f32_e32 v19, v19                                     // 00000000049C: 7E266B13
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 0000000004A0: 102C2CFF 472AEE8C
	v_add_f32_e32 v14, v14, v15                                // 0000000004A8: 061C1F0E
	v_fract_f32_e32 v21, v22                                   // 0000000004AC: 7E2A4116
	v_mul_f32_e32 v22, 0x472aee8c, v23                         // 0000000004B0: 102C2EFF 472AEE8C
	v_mul_f32_e32 v23, 0x472aee8c, v24                         // 0000000004B8: 102E30FF 472AEE8C
	v_mul_f32_e32 v24, 0x472aee8c, v25                         // 0000000004C0: 103032FF 472AEE8C
	v_mul_f32_e32 v25, 0x472aee8c, v26                         // 0000000004C8: 103234FF 472AEE8C
	v_mul_f32_e32 v26, 0x472aee8c, v27                         // 0000000004D0: 103436FF 472AEE8C
	v_mul_f32_e32 v27, 0x472aee8c, v28                         // 0000000004D8: 103638FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 0000000004E0: 102626FF 472AEE8C
	v_fract_f32_e32 v22, v22                                   // 0000000004E8: 7E2C4116
	v_fract_f32_e32 v25, v25                                   // 0000000004EC: 7E324119
	v_fract_f32_e32 v26, v26                                   // 0000000004F0: 7E34411A
	v_fract_f32_e32 v27, v27                                   // 0000000004F4: 7E36411B
	v_fract_f32_e32 v19, v19                                   // 0000000004F8: 7E264113
	v_fract_f32_e32 v23, v23                                   // 0000000004FC: 7E2E4117
	v_fract_f32_e32 v24, v24                                   // 000000000500: 7E304118
	v_sub_f32_e32 v22, v22, v21                                // 000000000504: 082C2B16
	v_sub_f32_e32 v26, v26, v25                                // 000000000508: 0834331A
	v_sub_f32_e32 v19, v19, v27                                // 00000000050C: 08263713
	v_sub_f32_e32 v24, v24, v23                                // 000000000510: 08302F18
	v_fmac_f32_e32 v21, v22, v10                               // 000000000514: 562A1516
	v_add_f32_e32 v22, 1.0, v16                                // 000000000518: 062C20F2
	v_fmac_f32_e32 v25, v26, v10                               // 00000000051C: 5632151A
	v_fmac_f32_e32 v27, v19, v10                               // 000000000520: 56361513
	v_fmac_f32_e32 v23, v24, v10                               // 000000000524: 562E1518
	v_mul_f32_e32 v10, 0.15915494, v16                         // 000000000528: 101420F8
	v_mul_f32_e32 v19, 0.15915494, v22                         // 00000000052C: 10262CF8
	v_add_f32_e32 v24, 0x42e20000, v16                         // 000000000530: 063020FF 42E20000
	v_sub_f32_e32 v22, v27, v25                                // 000000000538: 082C331B
	v_sub_f32_e32 v20, v23, v21                                // 00000000053C: 08282B17
	v_add_f32_e32 v23, 0x42680000, v16                         // 000000000540: 062E20FF 42680000
	v_sin_f32_e32 v10, v10                                     // 000000000548: 7E146B0A
	v_sin_f32_e32 v19, v19                                     // 00000000054C: 7E266B13
	v_fmac_f32_e32 v25, v22, v9                                // 000000000550: 56321316
	v_add_f32_e32 v22, 0x42640000, v16                         // 000000000554: 062C20FF 42640000
	v_mul_f32_e32 v23, 0.15915494, v23                         // 00000000055C: 102E2EF8
	v_fmac_f32_e32 v21, v20, v9                                // 000000000560: 562A1314
	v_mul_f32_e32 v20, v17, v17                                // 000000000564: 10282311
	v_fmaak_f32 v17, -2.0, v17, 0x40400000                     // 000000000568: 5A2222F5 40400000
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000570: 102C2CF8
	v_sin_f32_e32 v23, v23                                     // 000000000574: 7E2E6B17
	v_mul_f32_e32 v24, 0.15915494, v24                         // 000000000578: 103030F8
	v_add_f32_e32 v26, 0x42e40000, v16                         // 00000000057C: 063420FF 42E40000
	v_mul_f32_e32 v10, 0x472aee8c, v10                         // 000000000584: 101414FF 472AEE8C
	v_sin_f32_e32 v22, v22                                     // 00000000058C: 7E2C6B16
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000590: 102626FF 472AEE8C
	v_mul_f32_e32 v17, v20, v17                                // 000000000598: 10222314
	v_sin_f32_e32 v20, v24                                     // 00000000059C: 7E286B18
	v_mul_f32_e32 v24, 0.15915494, v26                         // 0000000005A0: 103034F8
	v_add_f32_e32 v26, 0x432a0000, v16                         // 0000000005A4: 063420FF 432A0000
	v_add_f32_e32 v16, 0x432b0000, v16                         // 0000000005AC: 062020FF 432B0000
	v_fract_f32_e32 v10, v10                                   // 0000000005B4: 7E14410A
	v_fract_f32_e32 v19, v19                                   // 0000000005B8: 7E264113
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 0000000005BC: 102E2EFF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 0000000005C4: 102C2CFF 472AEE8C
	v_sin_f32_e32 v24, v24                                     // 0000000005CC: 7E306B18
	v_mul_f32_e32 v26, 0.15915494, v26                         // 0000000005D0: 103434F8
	v_mul_f32_e32 v16, 0.15915494, v16                         // 0000000005D4: 102020F8
	v_sub_f32_e32 v19, v19, v10                                // 0000000005D8: 08261513
	v_fract_f32_e32 v22, v22                                   // 0000000005DC: 7E2C4116
	v_fract_f32_e32 v23, v23                                   // 0000000005E0: 7E2E4117
	v_sin_f32_e32 v26, v26                                     // 0000000005E4: 7E346B1A
	v_sin_f32_e32 v16, v16                                     // 0000000005E8: 7E206B10
	v_mul_f32_e32 v20, 0x472aee8c, v20                         // 0000000005EC: 102828FF 472AEE8C
	v_fmac_f32_e32 v10, v19, v17                               // 0000000005F4: 56142313
	v_mul_f32_e32 v19, 0x472aee8c, v24                         // 0000000005F8: 102630FF 472AEE8C
	v_sub_f32_e32 v23, v23, v22                                // 000000000600: 082E2D17
	v_floor_f32_e32 v24, v8                                    // 000000000604: 7E304908
	v_fract_f32_e32 v20, v20                                   // 000000000608: 7E284114
	v_fract_f32_e32 v8, v8                                     // 00000000060C: 7E104108
	v_fract_f32_e32 v18, v19                                   // 000000000610: 7E244113
	v_mul_f32_e32 v19, 0x472aee8c, v26                         // 000000000614: 102634FF 472AEE8C
	v_mul_f32_e32 v16, 0x472aee8c, v16                         // 00000000061C: 102020FF 472AEE8C
	v_fmac_f32_e32 v22, v23, v17                               // 000000000624: 562C2317
	v_fmamk_f32 v23, v24, 0x42e20000, v4                       // 000000000628: 582E0918 42E20000
	v_sub_f32_e32 v18, v18, v20                                // 000000000630: 08242912
	v_fract_f32_e32 v19, v19                                   // 000000000634: 7E264113
	v_fract_f32_e32 v16, v16                                   // 000000000638: 7E204110
	v_sub_f32_e32 v22, v22, v10                                // 00000000063C: 082C1516
	v_add_f32_e32 v24, 1.0, v23                                // 000000000640: 06302EF2
	v_fmac_f32_e32 v20, v18, v17                               // 000000000644: 56282312
	v_add_f32_e32 v18, 0x42640000, v23                         // 000000000648: 06242EFF 42640000
	v_sub_f32_e32 v16, v16, v19                                // 000000000650: 08202710
	v_add_f32_e32 v27, 0x42680000, v23                         // 000000000654: 06362EFF 42680000
	v_mul_f32_e32 v24, 0.15915494, v24                         // 00000000065C: 103030F8
	v_mul_f32_e32 v26, 0.15915494, v23                         // 000000000660: 10342EF8
	v_mul_f32_e32 v18, 0.15915494, v18                         // 000000000664: 102424F8
	v_fmac_f32_e32 v19, v16, v17                               // 000000000668: 56262310
	v_fmac_f32_e32 v10, v22, v9                                // 00000000066C: 56141316
	v_sin_f32_e32 v17, v24                                     // 000000000670: 7E226B18
	v_mul_f32_e32 v24, 0.15915494, v27                         // 000000000674: 103036F8
	v_sin_f32_e32 v16, v26                                     // 000000000678: 7E206B1A
	v_sin_f32_e32 v18, v18                                     // 00000000067C: 7E246B12
	v_add_f32_e32 v26, 0x42e20000, v23                         // 000000000680: 06342EFF 42E20000
	v_add_f32_e32 v27, 0x42e40000, v23                         // 000000000688: 06362EFF 42E40000
	v_sin_f32_e32 v22, v24                                     // 000000000690: 7E2C6B18
	v_sub_f32_e32 v19, v19, v20                                // 000000000694: 08262913
	v_sub_f32_e32 v25, v25, v21                                // 000000000698: 08322B19
	v_mul_f32_e32 v24, 0.15915494, v26                         // 00000000069C: 103034F8
	v_mul_f32_e32 v26, 0.15915494, v27                         // 0000000006A0: 103436F8
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 0000000006A4: 102222FF 472AEE8C
	v_mul_f32_e32 v16, 0x472aee8c, v16                         // 0000000006AC: 102020FF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 0000000006B4: 102424FF 472AEE8C
	v_sin_f32_e32 v24, v24                                     // 0000000006BC: 7E306B18
	v_sin_f32_e32 v26, v26                                     // 0000000006C0: 7E346B1A
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 0000000006C4: 102C2CFF 472AEE8C
	v_fract_f32_e32 v16, v16                                   // 0000000006CC: 7E204110
	v_fract_f32_e32 v18, v18                                   // 0000000006D0: 7E244112
	v_fract_f32_e32 v17, v17                                   // 0000000006D4: 7E224111
	v_fmac_f32_e32 v20, v19, v9                                // 0000000006D8: 56281313
	v_fract_f32_e32 v15, v22                                   // 0000000006DC: 7E1E4116
	v_mul_f32_e32 v19, v8, v8                                  // 0000000006E0: 10261108
	v_fmaak_f32 v8, -2.0, v8, 0x40400000                       // 0000000006E4: 5A1010F5 40400000
	v_mul_f32_e32 v22, 0x472aee8c, v24                         // 0000000006EC: 102C30FF 472AEE8C
	v_mul_f32_e32 v24, 0x472aee8c, v26                         // 0000000006F4: 103034FF 472AEE8C
	v_floor_f32_e32 v26, v14                                   // 0000000006FC: 7E34490E
	v_sub_f32_e32 v15, v15, v18                                // 000000000700: 081E250F
	v_sub_f32_e32 v17, v17, v16                                // 000000000704: 08222111
	v_mul_f32_e32 v8, v19, v8                                  // 000000000708: 10101113
	v_fmac_f32_e32 v21, v25, v13                               // 00000000070C: 562A1B19
	v_fmac_f32_e32 v4, 0x42e20000, v26                         // 000000000710: 560834FF 42E20000
	v_fmac_f32_e32 v18, v15, v2                                // 000000000718: 5624050F
	v_add_f32_e32 v15, 0x432a0000, v23                         // 00000000071C: 061E2EFF 432A0000
	v_add_f32_e32 v23, 0x432b0000, v23                         // 000000000724: 062E2EFF 432B0000
	v_fmac_f32_e32 v16, v17, v2                                // 00000000072C: 56200511
	v_mul_f32_e32 v27, 0.15915494, v4                          // 000000000730: 103608F8
	v_fract_f32_e32 v17, v22                                   // 000000000734: 7E224116
	v_fract_f32_e32 v22, v24                                   // 000000000738: 7E2C4118
	v_mul_f32_e32 v23, 0.15915494, v23                         // 00000000073C: 102E2EF8
	v_add_f32_e32 v24, 1.0, v4                                 // 000000000740: 063008F2
	v_sin_f32_e32 v27, v27                                     // 000000000744: 7E366B1B
	v_add_f32_e32 v26, 0x42640000, v4                          // 000000000748: 063408FF 42640000
	v_sub_f32_e32 v22, v22, v17                                // 000000000750: 082C2316
	v_sin_f32_e32 v23, v23                                     // 000000000754: 7E2E6B17
	v_add_f32_e32 v28, 0x42e20000, v4                          // 000000000758: 063808FF 42E20000
	v_add_f32_e32 v29, 0x42e40000, v4                          // 000000000760: 063A08FF 42E40000
	v_add_f32_e32 v30, 0x432a0000, v4                          // 000000000768: 063C08FF 432A0000
	v_fmac_f32_e32 v17, v22, v2                                // 000000000770: 56220516
	v_mul_f32_e32 v15, 0.15915494, v15                         // 000000000774: 101E1EF8
	v_mul_f32_e32 v24, 0.15915494, v24                         // 000000000778: 103030F8
	v_mul_f32_e32 v26, 0.15915494, v26                         // 00000000077C: 103434F8
	v_mul_f32_e32 v28, 0.15915494, v28                         // 000000000780: 103838F8
	v_mul_f32_e32 v29, 0.15915494, v29                         // 000000000784: 103A3AF8
	v_mul_f32_e32 v22, 0x472aee8c, v23                         // 000000000788: 102C2EFF 472AEE8C
	v_mul_f32_e32 v23, 0x472aee8c, v27                         // 000000000790: 102E36FF 472AEE8C
	v_add_f32_e32 v27, 0x42680000, v4                          // 000000000798: 063608FF 42680000
	v_add_f32_e32 v4, 0x432b0000, v4                           // 0000000007A0: 060808FF 432B0000
	v_mul_f32_e32 v30, 0.15915494, v30                         // 0000000007A8: 103C3CF8
	v_sin_f32_e32 v15, v15                                     // 0000000007AC: 7E1E6B0F
	v_sin_f32_e32 v24, v24                                     // 0000000007B0: 7E306B18
	v_mul_f32_e32 v27, 0.15915494, v27                         // 0000000007B4: 103636F8
	v_mul_f32_e32 v4, 0.15915494, v4                           // 0000000007B8: 100808F8
	v_sin_f32_e32 v26, v26                                     // 0000000007BC: 7E346B1A
	v_sin_f32_e32 v28, v28                                     // 0000000007C0: 7E386B1C
	v_sin_f32_e32 v29, v29                                     // 0000000007C4: 7E3A6B1D
	v_sin_f32_e32 v27, v27                                     // 0000000007C8: 7E366B1B
	v_sin_f32_e32 v30, v30                                     // 0000000007CC: 7E3C6B1E
	v_sin_f32_e32 v4, v4                                       // 0000000007D0: 7E086B04
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 0000000007D4: 101E1EFF 472AEE8C
	v_mul_f32_e32 v24, 0x472aee8c, v24                         // 0000000007DC: 103030FF 472AEE8C
	v_fract_f32_e32 v22, v22                                   // 0000000007E4: 7E2C4116
	v_fract_f32_e32 v23, v23                                   // 0000000007E8: 7E2E4117
	v_mul_f32_e32 v26, 0x472aee8c, v26                         // 0000000007EC: 103434FF 472AEE8C
	v_mul_f32_e32 v28, 0x472aee8c, v28                         // 0000000007F4: 103838FF 472AEE8C
	v_mul_f32_e32 v29, 0x472aee8c, v29                         // 0000000007FC: 103A3AFF 472AEE8C
	v_mul_f32_e32 v27, 0x472aee8c, v27                         // 000000000804: 103636FF 472AEE8C
	v_mul_f32_e32 v30, 0x472aee8c, v30                         // 00000000080C: 103C3CFF 472AEE8C
	v_mul_f32_e32 v4, 0x472aee8c, v4                           // 000000000814: 100808FF 472AEE8C
	v_fract_f32_e32 v15, v15                                   // 00000000081C: 7E1E410F
	v_fract_f32_e32 v24, v24                                   // 000000000820: 7E304118
	v_fract_f32_e32 v26, v26                                   // 000000000824: 7E34411A
	v_fract_f32_e32 v27, v27                                   // 000000000828: 7E36411B
	v_fract_f32_e32 v28, v28                                   // 00000000082C: 7E38411C
	v_fract_f32_e32 v29, v29                                   // 000000000830: 7E3A411D
	v_fract_f32_e32 v30, v30                                   // 000000000834: 7E3C411E
	v_fract_f32_e32 v4, v4                                     // 000000000838: 7E084104
	v_sub_f32_e32 v22, v22, v15                                // 00000000083C: 082C1F16
	v_sub_f32_e32 v24, v24, v23                                // 000000000840: 08302F18
	v_sub_f32_e32 v27, v27, v26                                // 000000000844: 0836351B
	v_sub_f32_e32 v29, v29, v28                                // 000000000848: 083A391D
	v_sub_f32_e32 v4, v4, v30                                  // 00000000084C: 08083D04
	v_fmac_f32_e32 v15, v22, v2                                // 000000000850: 561E0516
	v_fmac_f32_e32 v23, v24, v2                                // 000000000854: 562E0518
	v_fmac_f32_e32 v26, v27, v2                                // 000000000858: 5634051B
	v_fmac_f32_e32 v28, v29, v2                                // 00000000085C: 5638051D
	v_fmac_f32_e32 v30, v4, v2                                 // 000000000860: 563C0504
	v_sub_f32_e32 v18, v18, v16                                // 000000000864: 08242112
	v_sub_f32_e32 v2, v15, v17                                 // 000000000868: 0804230F
	v_fract_f32_e32 v4, v14                                    // 00000000086C: 7E08410E
	v_sub_f32_e32 v14, v26, v23                                // 000000000870: 081C2F1A
	v_sub_f32_e32 v15, v30, v28                                // 000000000874: 081E391E
	v_fmac_f32_e32 v16, v18, v9                                // 000000000878: 56201312
	v_fmac_f32_e32 v17, v2, v9                                 // 00000000087C: 56221302
	v_mul_f32_e32 v2, v4, v4                                   // 000000000880: 10040904
	v_fmaak_f32 v4, 2.0, v4, 0xc0400000                        // 000000000884: 5A0808F4 C0400000
	v_fmac_f32_e32 v23, v14, v9                                // 00000000088C: 562E130E
	v_fmac_f32_e32 v28, v15, v9                                // 000000000890: 5638130F
	v_sub_f32_e32 v9, v20, v10                                 // 000000000894: 08121514
	v_sub_f32_e32 v14, v17, v16                                // 000000000898: 081C2111
	v_mul_f32_e32 v2, v2, v4                                   // 00000000089C: 10040902
	v_sub_f32_e32 v4, v23, v28                                 // 0000000008A0: 08083917
	v_fmac_f32_e32 v10, v13, v9                                // 0000000008A4: 5614130D
	v_fmac_f32_e32 v16, v14, v8                                // 0000000008A8: 5620110E
	v_fmac_f32_e32 v23, v2, v4                                 // 0000000008AC: 562E0902
	v_sub_f32_e32 v2, v21, v10                                 // 0000000008B0: 08041515
	v_sub_f32_e32 v4, v16, v23                                 // 0000000008B4: 08082F10
	v_mul_f32_e32 v2, 0x41200000, v2                           // 0000000008B8: 100404FF 41200000
	v_mul_f32_e32 v4, 0x41200000, v4                           // 0000000008C0: 100808FF 41200000
	v_fma_f32 v8, v2, v2, 4.0                                  // 0000000008C8: D54B0008 03DA0502
	v_fmac_f32_e32 v8, v4, v4                                  // 0000000008D0: 56100904
	v_rsq_f32_e32 v10, v8                                      // 0000000008D4: 7E145D08
	v_mul_legacy_f32_e32 v8, v2, v10                           // 0000000008D8: 0E101502
	v_add_f32_e32 v9, v10, v10                                 // 0000000008DC: 0612150A
	v_mul_legacy_f32_e32 v10, v4, v10                          // 0000000008E0: 0E141504
_L1:
	s_or_b64 exec, exec, s[8:9]                                // 0000000008E4: 88FE087E
	v_lshlrev_b32_e32 v13, 6, v12                              // 0000000008E8: 341A1886
	v_max_u32_e32 v14, v11, v12                                // 0000000008EC: 281C190B
	v_mul_f32_e32 v0, 0x3c820821, v0                           // 0000000008F0: 100000FF 3C820821
	v_mul_f32_e32 v1, 0x3c820821, v1                           // 0000000008F8: 100202FF 3C820821
	v_mov_b32_e32 v2, 0.5                                      // 000000000900: 7E0402F0
	v_or_b32_e32 v4, v13, v11                                  // 000000000904: 3808170D
	v_cmp_gt_u32_e32 vcc_lo, 63, v14                           // 000000000908: 7D881CBF
	v_lshlrev_b32_e32 v15, 6, v4                               // 00000000090C: 341E0886
	buffer_store_dwordx3 v[5:7], v15, s[0:3], 0 offen          // 000000000910: E07C1000 8000050F
	buffer_store_dwordx3 v[8:10], v15, s[0:3], 0 offen offset:16// 000000000918: E07C1010 8000080F
	buffer_store_dwordx2 v[0:1], v15, s[0:3], 0 offen offset:32// 000000000920: E0741020 8000000F
	buffer_store_dwordx4 v[0:3], v15, s[0:3], 0 offen offset:48// 000000000928: E0781030 8000000F
	s_and_b64 exec, exec, vcc                                  // 000000000930: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000934: BF88000F
	v_mad_u64_u32 v[0:1], null, v12, 63, v[11:12]              // 000000000938: D5767D00 042D7F0C
	v_add_nc_u32_e32 v1, v13, v11                              // 000000000940: 4A02170D
	v_add_nc_u32_e32 v6, 1, v4                                 // 000000000944: 4A0C0881
	v_add_nc_u32_e32 v5, 64, v1                                // 000000000948: 4A0A02C0
	v_mul_lo_u32 v0, v0, 24                                    // 00000000094C: D5690000 00013100
	v_mov_b32_e32 v7, v6                                       // 000000000954: 7E0E0306
	v_add_nc_u32_e32 v1, 0x41, v1                              // 000000000958: 4A0202FF 00000041
	buffer_store_dwordx4 v[4:7], v0, s[4:7], 0 offen           // 000000000960: E0781000 80010400
	v_mov_b32_e32 v6, v1                                       // 000000000968: 7E0C0301
	buffer_store_dwordx2 v[5:6], v0, s[4:7], 0 offen offset:16 // 00000000096C: E0741010 80010500
_L0:
	s_endpgm                                                   // 000000000974: BF810000

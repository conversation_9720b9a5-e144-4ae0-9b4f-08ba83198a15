; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 198
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "BaseColor"
               OpMemberName %type_Material 1 "Metallic"
               OpMemberName %type_Material 2 "Roughness"
               OpMemberName %type_Material 3 "LightDirection"
               OpMemberName %type_Material 4 "LightColor"
               OpMemberName %type_Material 5 "AmbientColor"
               OpMemberName %type_Material 6 "Time"
               OpName %Material "Material"
               OpName %type_2d_image_array "type.2d.image.array"
               OpName %DiffuseTextureArray "DiffuseTextureArray"
               OpName %type_2d_image "type.2d.image"
               OpName %NoiseTexture "NoiseTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %DiffuseTextureArray DescriptorSet 0
               OpDecorate %DiffuseTextureArray Binding 0
               OpDecorate %NoiseTexture DescriptorSet 0
               OpDecorate %NoiseTexture Binding 2
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 16
               OpMemberDecorate %type_Material 2 Offset 20
               OpMemberDecorate %type_Material 3 Offset 32
               OpMemberDecorate %type_Material 4 Offset 48
               OpMemberDecorate %type_Material 5 Offset 64
               OpMemberDecorate %type_Material 6 Offset 76
               OpDecorate %type_Material Block
        %int = OpTypeInt 32 1
      %int_3 = OpConstant %int 3
      %int_5 = OpConstant %int 5
      %int_6 = OpConstant %int 6
      %int_4 = OpConstant %int 4
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
   %float_32 = OpConstant %float 32
  %float_256 = OpConstant %float 256
    %float_1 = OpConstant %float 1
  %float_0_5 = OpConstant %float 0.5
%float_0_300000012 = OpConstant %float 0.300000012
    %float_2 = OpConstant %float 2
    %v3float = OpTypeVector %float 3
%float_0_699999988 = OpConstant %float 0.699999988
%float_0_800000012 = OpConstant %float 0.800000012
%float_0_899999976 = OpConstant %float 0.899999976
         %38 = OpConstantComposite %v3float %float_0_699999988 %float_0_800000012 %float_0_899999976
%float_0_100000001 = OpConstant %float 0.100000001
    %v4float = OpTypeVector %float 4
         %41 = OpConstantComposite %v4float %float_0_100000001 %float_0_800000012 %float_0 %float_1
         %42 = OpConstantComposite %v4float %float_0 %float_0_899999976 %float_0 %float_1
%float_0_200000003 = OpConstant %float 0.200000003
         %44 = OpConstantComposite %v4float %float_0_800000012 %float_0_200000003 %float_0 %float_1
    %float_4 = OpConstant %float 4
%float_0_0500000007 = OpConstant %float 0.0500000007
%type_Material = OpTypeStruct %v4float %float %float %v3float %v3float %v3float %float
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
%type_2d_image_array = OpTypeImage %float 2D 2 1 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_array = OpTypePointer UniformConstant %type_2d_image_array
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %58 = OpTypeFunction %void
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%type_sampled_image = OpTypeSampledImage %type_2d_image_array
       %bool = OpTypeBool
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image_0 = OpTypeSampledImage %type_2d_image
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
%DiffuseTextureArray = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%NoiseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
%float_n0_00999999978 = OpConstant %float -0.00999999978
     %v4bool = OpTypeVector %bool 4
       %main = OpFunction %void None %58
         %64 = OpLabel
         %65 = OpLoad %v3float %in_var_TEXCOORD0
         %66 = OpLoad %v3float %in_var_TEXCOORD1
         %67 = OpLoad %v2float %in_var_TEXCOORD2
         %68 = OpLoad %v4float %in_var_TEXCOORD3
         %69 = OpLoad %v3float %in_var_TEXCOORD4
         %70 = OpLoad %float %in_var_TEXCOORD5
         %71 = OpLoad %float %in_var_TEXCOORD6
         %72 = OpExtInst %v3float %1 Normalize %66
         %73 = OpAccessChain %_ptr_Uniform_v3float %Material %int_3
         %74 = OpLoad %v3float %73
         %75 = OpFNegate %v3float %74
         %76 = OpExtInst %v3float %1 Normalize %75
         %77 = OpExtInst %v3float %1 Normalize %69
         %78 = OpConvertFToS %int %70
         %79 = OpLoad %type_2d_image_array %DiffuseTextureArray
         %80 = OpLoad %type_sampler %LinearSampler
         %81 = OpConvertSToF %float %78
         %82 = OpCompositeExtract %float %67 0
         %83 = OpCompositeExtract %float %67 1
         %84 = OpCompositeConstruct %v3float %82 %83 %81
         %85 = OpSampledImage %type_sampled_image %79 %80
         %86 = OpImageSampleImplicitLod %v4float %85 %84 None
         %87 = OpFMul %v4float %86 %68
         %88 = OpFOrdLessThan %bool %70 %float_1
               OpSelectionMerge %89 None
               OpBranchConditional %88 %90 %91
         %90 = OpLabel
               OpBranch %89
         %91 = OpLabel
         %92 = OpFOrdLessThan %bool %70 %float_2
         %93 = OpCompositeConstruct %v4bool %92 %92 %92 %92
         %94 = OpSelect %v4float %93 %42 %44
               OpBranch %89
         %89 = OpLabel
         %95 = OpPhi %v4float %41 %90 %94 %91
         %96 = OpCompositeConstruct %v2float %70 %70
         %97 = OpFAdd %v2float %67 %96
         %98 = OpLoad %type_2d_image %NoiseTexture
         %99 = OpLoad %type_sampler %LinearSampler
        %100 = OpVectorTimesScalar %v2float %97 %float_0_100000001
        %101 = OpSampledImage %type_sampled_image_0 %98 %99
        %102 = OpImageSampleImplicitLod %v4float %101 %100 None
        %103 = OpCompositeExtract %float %102 0
        %104 = OpFSub %float %103 %float_0_100000001
        %105 = OpCompositeExtract %float %95 1
        %106 = OpFAdd %float %105 %104
        %107 = OpCompositeExtract %float %95 0
        %108 = OpDot %float %72 %76
        %109 = OpExtInst %float %1 NMax %float_0 %108
        %110 = OpVectorShuffle %v3float %87 %87 0 1 2
        %111 = OpAccessChain %_ptr_Uniform_v3float %Material %int_4
        %112 = OpLoad %v3float %111
        %113 = OpFMul %v3float %110 %112
        %114 = OpVectorTimesScalar %v3float %113 %109
        %115 = OpFAdd %v3float %76 %77
        %116 = OpExtInst %v3float %1 Normalize %115
        %117 = OpDot %float %72 %116
        %118 = OpExtInst %float %1 NMax %float_0 %117
        %119 = OpFSub %float %float_1 %106
        %120 = OpExtInst %float %1 FMix %float_32 %float_256 %119
        %121 = OpExtInst %float %1 Pow %118 %120
        %122 = OpVectorTimesScalar %v3float %112 %121
        %123 = OpVectorTimesScalar %v3float %122 %107
        %124 = OpAccessChain %_ptr_Uniform_v3float %Material %int_5
        %125 = OpLoad %v3float %124
        %126 = OpFMul %v3float %125 %110
        %127 = OpFAdd %v3float %126 %114
        %128 = OpFAdd %v3float %127 %123
               OpSelectionMerge %129 None
               OpBranchConditional %88 %130 %129
        %130 = OpLabel
        %131 = OpFNegate %v3float %76
        %132 = OpDot %float %131 %72
        %133 = OpExtInst %float %1 NMax %float_0 %132
        %134 = OpDot %float %77 %131
        %135 = OpExtInst %float %1 NMax %float_0 %134
        %136 = OpExtInst %float %1 Pow %135 %float_4
        %137 = OpFMul %float %136 %133
        %138 = OpVectorTimesScalar %v3float %110 %137
        %139 = OpVectorTimesScalar %v3float %138 %float_0_5
        %140 = OpFAdd %v3float %128 %139
               OpSelectionMerge %141 None
               OpBranchConditional %88 %142 %141
        %142 = OpLabel
        %143 = OpVectorShuffle %v2float %65 %65 0 2
        %144 = OpVectorTimesScalar %v2float %143 %float_0_100000001
        %145 = OpAccessChain %_ptr_Uniform_float %Material %int_6
        %146 = OpLoad %float %145
        %147 = OpFMul %float %146 %float_0_100000001
        %148 = OpCompositeConstruct %v2float %147 %147
        %149 = OpFAdd %v2float %144 %148
        %150 = OpLoad %type_2d_image %NoiseTexture
        %151 = OpLoad %type_sampler %LinearSampler
        %152 = OpVectorTimesScalar %v2float %149 %float_0_100000001
        %153 = OpSampledImage %type_sampled_image_0 %150 %151
        %154 = OpImageSampleImplicitLod %v4float %153 %152 None
        %155 = OpCompositeExtract %float %154 0
        %156 = OpFMul %float %146 %float_2
        %157 = OpCompositeExtract %float %65 0
        %158 = OpFMul %float %157 %float_0_100000001
        %159 = OpFAdd %float %156 %158
        %160 = OpExtInst %float %1 Sin %159
        %161 = OpFMul %float %160 %155
        %162 = OpFMul %float %161 %float_0_100000001
        %163 = OpCompositeExtract %float %140 1
        %164 = OpFAdd %float %163 %162
        %165 = OpCompositeInsert %v3float %164 %140 1
        %166 = OpFMul %float %161 %float_0_0500000007
        %167 = OpCompositeExtract %float %140 2
        %168 = OpFSub %float %167 %166
        %169 = OpCompositeInsert %v3float %168 %165 2
               OpBranch %141
        %141 = OpLabel
        %170 = OpPhi %v3float %140 %130 %169 %142
               OpBranch %129
        %129 = OpLabel
        %171 = OpPhi %v3float %128 %89 %170 %141
        %172 = OpFOrdLessThan %bool %71 %float_1
               OpSelectionMerge %173 None
               OpBranchConditional %172 %174 %173
        %174 = OpLabel
        %175 = OpVectorTimesScalar %v3float %171 %float_0_5
        %176 = OpCompositeConstruct %v3float %71 %71 %71
        %177 = OpExtInst %v3float %1 FMix %175 %171 %176
        %178 = OpFOrdLessThan %bool %71 %float_0_300000012
               OpSelectionMerge %179 None
               OpBranchConditional %178 %180 %179
        %180 = OpLabel
        %181 = OpFMul %float %71 %float_2
        %182 = OpFSub %float %float_0_300000012 %181
        %183 = OpCompositeExtract %float %177 0
        %184 = OpFAdd %float %183 %182
        %185 = OpCompositeInsert %v3float %184 %177 0
               OpBranch %179
        %179 = OpLabel
        %186 = OpPhi %v3float %177 %174 %185 %180
               OpBranch %173
        %173 = OpLabel
        %187 = OpPhi %v3float %171 %129 %186 %179
        %188 = OpExtInst %float %1 Length %65
        %189 = OpFMul %float %188 %float_n0_00999999978
        %190 = OpExtInst %float %1 Exp %189
        %191 = OpCompositeConstruct %v3float %190 %190 %190
        %192 = OpExtInst %v3float %1 FMix %38 %187 %191
        %193 = OpCompositeExtract %float %87 3
        %194 = OpCompositeExtract %float %192 0
        %195 = OpCompositeExtract %float %192 1
        %196 = OpCompositeExtract %float %192 2
        %197 = OpCompositeConstruct %v4float %194 %195 %196 %193
               OpStore %out_var_SV_TARGET %197
               OpReturn
               OpFunctionEnd

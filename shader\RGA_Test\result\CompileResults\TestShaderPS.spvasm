; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 181
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD6 %in_var_TEXCOORD7 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_2d_image "type.2d.image"
               OpName %DiffuseTexture "DiffuseTexture"
               OpName %NormalTexture "NormalTexture"
               OpName %SpecularTexture "SpecularTexture"
               OpName %EmissiveTexture "EmissiveTexture"
               OpName %type_cube_image "type.cube.image"
               OpName %EnvironmentTexture "EnvironmentTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %type_MaterialConstants "type.MaterialConstants"
               OpMemberName %type_MaterialConstants 0 "WorldMatrix"
               OpMemberName %type_MaterialConstants 1 "ViewMatrix"
               OpMemberName %type_MaterialConstants 2 "ProjectionMatrix"
               OpMemberName %type_MaterialConstants 3 "WorldViewProjectionMatrix"
               OpMemberName %type_MaterialConstants 4 "LightPosition"
               OpMemberName %type_MaterialConstants 5 "LightColor"
               OpMemberName %type_MaterialConstants 6 "MaterialDiffuse"
               OpMemberName %type_MaterialConstants 7 "MaterialSpecular"
               OpMemberName %type_MaterialConstants 8 "CameraPosition"
               OpMemberName %type_MaterialConstants 9 "Time"
               OpMemberName %type_MaterialConstants 10 "SpecularPower"
               OpMemberName %type_MaterialConstants 11 "TextureScale"
               OpName %MaterialConstants "MaterialConstants"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %in_var_TEXCOORD7 "in.var.TEXCOORD7"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %in_var_TEXCOORD7 Location 7
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %DiffuseTexture DescriptorSet 0
               OpDecorate %DiffuseTexture Binding 0
               OpDecorate %NormalTexture DescriptorSet 0
               OpDecorate %NormalTexture Binding 1
               OpDecorate %SpecularTexture DescriptorSet 0
               OpDecorate %SpecularTexture Binding 2
               OpDecorate %EmissiveTexture DescriptorSet 0
               OpDecorate %EmissiveTexture Binding 3
               OpDecorate %EnvironmentTexture DescriptorSet 0
               OpDecorate %EnvironmentTexture Binding 4
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %MaterialConstants DescriptorSet 0
               OpDecorate %MaterialConstants Binding 0
               OpMemberDecorate %type_MaterialConstants 0 Offset 0
               OpMemberDecorate %type_MaterialConstants 0 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 0 RowMajor
               OpMemberDecorate %type_MaterialConstants 1 Offset 64
               OpMemberDecorate %type_MaterialConstants 1 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 1 RowMajor
               OpMemberDecorate %type_MaterialConstants 2 Offset 128
               OpMemberDecorate %type_MaterialConstants 2 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 2 RowMajor
               OpMemberDecorate %type_MaterialConstants 3 Offset 192
               OpMemberDecorate %type_MaterialConstants 3 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 3 RowMajor
               OpMemberDecorate %type_MaterialConstants 4 Offset 256
               OpMemberDecorate %type_MaterialConstants 5 Offset 272
               OpMemberDecorate %type_MaterialConstants 6 Offset 288
               OpMemberDecorate %type_MaterialConstants 7 Offset 304
               OpMemberDecorate %type_MaterialConstants 8 Offset 320
               OpMemberDecorate %type_MaterialConstants 9 Offset 336
               OpMemberDecorate %type_MaterialConstants 10 Offset 340
               OpMemberDecorate %type_MaterialConstants 11 Offset 344
               OpDecorate %type_MaterialConstants Block
      %float = OpTypeFloat 32
%float_0_800000012 = OpConstant %float 0.800000012
%float_0_600000024 = OpConstant %float 0.600000024
%float_0_400000006 = OpConstant %float 0.400000006
    %float_1 = OpConstant %float 1
    %v4float = OpTypeVector %float 4
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_0500000007 = OpConstant %float 0.0500000007
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %33 = OpConstantComposite %v3float %float_0_100000001 %float_0_0500000007 %float_0
        %int = OpTypeInt 32 1
      %int_5 = OpConstant %int 5
%float_0_00999999978 = OpConstant %float 0.00999999978
    %v2float = OpTypeVector %float 2
         %38 = OpConstantComposite %v2float %float_0_00999999978 %float_0_00999999978
      %int_9 = OpConstant %int 9
  %float_0_5 = OpConstant %float 0.5
    %float_2 = OpConstant %float 2
         %42 = OpConstantComposite %v3float %float_1 %float_1 %float_1
      %int_3 = OpConstant %int 3
      %int_7 = OpConstant %int 7
     %int_10 = OpConstant %int 10
    %float_5 = OpConstant %float 5
      %int_6 = OpConstant %int 6
%float_0_300000012 = OpConstant %float 0.300000012
%float_0_454545468 = OpConstant %float 0.454545468
         %50 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
%float_0_899999976 = OpConstant %float 0.899999976
%float_1_00999999 = OpConstant %float 1.00999999
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_cube_image = OpTypeImage %float Cube 2 0 0 1 Unknown
%_ptr_UniformConstant_type_cube_image = OpTypePointer UniformConstant %type_cube_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%mat4v4float = OpTypeMatrix %v4float 4
%type_MaterialConstants = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %mat4v4float %v4float %v4float %v4float %v4float %v4float %float %float %v2float
%_ptr_Uniform_type_MaterialConstants = OpTypePointer Uniform %type_MaterialConstants
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %62 = OpTypeFunction %void
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image = OpTypeSampledImage %type_2d_image
%type_sampled_image_0 = OpTypeSampledImage %type_cube_image
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%DiffuseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%NormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%SpecularTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EmissiveTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EnvironmentTexture = OpVariable %_ptr_UniformConstant_type_cube_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%MaterialConstants = OpVariable %_ptr_Uniform_type_MaterialConstants Uniform
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD7 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
         %66 = OpConstantComposite %v3float %float_0_800000012 %float_0_600000024 %float_0_400000006
%float_2_28000021 = OpConstant %float 2.28000021
   %float_37 = OpConstant %float 37
       %main = OpFunction %void None %62
         %69 = OpLabel
         %70 = OpLoad %v3float %in_var_TEXCOORD1
         %71 = OpLoad %v3float %in_var_TEXCOORD2
         %72 = OpLoad %v3float %in_var_TEXCOORD3
         %73 = OpLoad %v2float %in_var_TEXCOORD4
         %74 = OpLoad %v3float %in_var_TEXCOORD6
         %75 = OpLoad %v3float %in_var_TEXCOORD7
         %76 = OpAccessChain %_ptr_Uniform_float %MaterialConstants %int_9
         %77 = OpLoad %float %76
         %78 = OpFMul %float %77 %float_0_5
         %79 = OpExtInst %float %1 Sin %78
         %80 = OpVectorTimesScalar %v2float %38 %79
         %81 = OpFAdd %v2float %73 %80
         %82 = OpVectorTimesScalar %v2float %81 %float_1
         %83 = OpLoad %type_2d_image %DiffuseTexture
         %84 = OpLoad %type_sampler %LinearSampler
         %85 = OpSampledImage %type_sampled_image %83 %84
         %86 = OpImageSampleImplicitLod %v4float %85 %82 None
         %87 = OpLoad %type_2d_image %NormalTexture
         %88 = OpLoad %type_sampler %LinearSampler
         %89 = OpSampledImage %type_sampled_image %87 %88
         %90 = OpImageSampleImplicitLod %v4float %89 %82 None
         %91 = OpLoad %type_2d_image %SpecularTexture
         %92 = OpLoad %type_sampler %LinearSampler
         %93 = OpSampledImage %type_sampled_image %91 %92
         %94 = OpImageSampleImplicitLod %v4float %93 %82 None
         %95 = OpLoad %type_2d_image %EmissiveTexture
         %96 = OpLoad %type_sampler %LinearSampler
         %97 = OpVectorTimesScalar %v2float %82 %float_1
         %98 = OpSampledImage %type_sampled_image %95 %96
         %99 = OpImageSampleImplicitLod %v4float %98 %97 None
        %100 = OpVectorShuffle %v3float %90 %90 0 1 2
        %101 = OpVectorTimesScalar %v3float %100 %float_2
        %102 = OpFSub %v3float %101 %42
        %103 = OpExtInst %v3float %1 Normalize %102
        %104 = OpExtInst %v3float %1 Normalize %71
        %105 = OpExtInst %v3float %1 Normalize %72
        %106 = OpExtInst %v3float %1 Normalize %70
        %107 = OpCompositeConstruct %mat3v3float %104 %105 %106
        %108 = OpMatrixTimesVector %v3float %107 %103
        %109 = OpVectorTimesScalar %v3float %108 %float_1
        %110 = OpExtInst %v3float %1 Normalize %109
        %111 = OpExtInst %v3float %1 Normalize %75
        %112 = OpExtInst %v3float %1 Normalize %74
        %113 = OpFAdd %v3float %111 %112
        %114 = OpExtInst %v3float %1 Normalize %113
        %115 = OpDot %float %110 %111
        %116 = OpExtInst %float %1 NMax %float_0 %115
        %117 = OpDot %float %110 %114
        %118 = OpExtInst %float %1 NMax %float_0 %117
        %119 = OpAccessChain %_ptr_Uniform_float %MaterialConstants %int_10
        %120 = OpLoad %float %119
        %121 = OpExtInst %float %1 Pow %118 %120
        %122 = OpDot %float %112 %110
        %123 = OpFSub %float %float_1 %122
        %124 = OpExtInst %float %1 Pow %123 %float_5
        %125 = OpFNegate %v3float %112
        %126 = OpExtInst %v3float %1 Reflect %125 %110
        %127 = OpLoad %type_cube_image %EnvironmentTexture
        %128 = OpLoad %type_sampler %LinearSampler
        %129 = OpSampledImage %type_sampled_image_0 %127 %128
        %130 = OpImageSampleImplicitLod %v4float %129 %126 None
        %131 = OpVectorShuffle %v3float %86 %86 0 1 2
        %132 = OpFMul %v3float %131 %66
        %133 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_6
        %134 = OpLoad %v4float %133
        %135 = OpVectorShuffle %v3float %134 %134 0 1 2
        %136 = OpFMul %v3float %132 %135
        %137 = OpVectorTimesScalar %v3float %136 %116
        %138 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_5
        %139 = OpLoad %v4float %138
        %140 = OpVectorShuffle %v3float %139 %139 0 1 2
        %141 = OpFMul %v3float %137 %140
        %142 = OpVectorShuffle %v3float %94 %94 0 1 2
        %143 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_7
        %144 = OpLoad %v4float %143
        %145 = OpVectorShuffle %v3float %144 %144 0 1 2
        %146 = OpFMul %v3float %142 %145
        %147 = OpVectorTimesScalar %v3float %146 %121
        %148 = OpFMul %v3float %147 %140
        %149 = OpVectorTimesScalar %v3float %148 %float_1
        %150 = OpVectorShuffle %v3float %99 %99 0 1 2
        %151 = OpFMul %v3float %150 %33
        %152 = OpFMul %v3float %151 %135
        %153 = OpVectorShuffle %v3float %130 %130 0 1 2
        %154 = OpVectorTimesScalar %v3float %153 %124
        %155 = OpVectorTimesScalar %v3float %154 %float_0_300000012
        %156 = OpFAdd %v3float %141 %149
        %157 = OpFAdd %v3float %156 %152
        %158 = OpFAdd %v3float %157 %155
        %159 = OpCompositeExtract %float %158 0
        %160 = OpFAdd %float %159 %float_2
        %161 = OpCompositeExtract %float %158 1
        %162 = OpFAdd %float %161 %float_2_28000021
        %163 = OpCompositeExtract %float %158 2
        %164 = OpFAdd %float %163 %float_37
        %165 = OpCompositeConstruct %v3float %160 %162 %164
        %166 = OpFDiv %v3float %165 %165
        %167 = OpFAdd %v3float %166 %42
        %168 = OpExtInst %v3float %1 Pow %167 %50
        %169 = OpCompositeExtract %float %86 3
        %170 = OpAccessChain %_ptr_Uniform_float %MaterialConstants %int_6 %int_3
        %171 = OpLoad %float %170
        %172 = OpFMul %float %169 %171
        %173 = OpVectorTimesScalar %v3float %168 %float_0_899999976
        %174 = OpVectorTimesScalar %v3float %173 %float_1_00999999
        %175 = OpVectorTimesScalar %v3float %174 %float_1_00999999
        %176 = OpVectorTimesScalar %v3float %175 %float_1_00999999
        %177 = OpCompositeExtract %float %176 0
        %178 = OpCompositeExtract %float %176 1
        %179 = OpCompositeExtract %float %176 2
        %180 = OpCompositeConstruct %v4float %177 %178 %179 %172
               OpStore %out_var_SV_TARGET %180
               OpReturn
               OpFunctionEnd

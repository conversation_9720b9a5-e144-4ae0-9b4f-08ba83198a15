# Optimize all HLSL shaders using hlsloptconv
Write-Host "Optimizing all HLSL shaders using hlsloptconv..." -ForegroundColor Green

# Path to hlsloptconv executable
$hlsloptconv = "..\..\hlsloptconv\bin\hlsloptconv.exe"

# Check if hlsloptconv exists
if (-not (Test-Path $hlsloptconv)) {
    Write-Host "ERROR: hlsloptconv.exe not found at $hlsloptconv" -ForegroundColor Red
    Write-Host "Please make sure hlsloptconv is built successfully." -ForegroundColor Red
    exit 1
}

# Create result directory if it doesn't exist
if (-not (Test-Path "result")) {
    New-Item -ItemType Directory -Path "result" | Out-Null
}

# Get all HLSL files from current directory, excluding problematic files
$hlslFiles = Get-ChildItem -Path "." -Filter "*.hlsl" | Where-Object {
    $_.Name -ne "compile_and_save_results.ps1" -and
    $_.Name -ne "StressTestCS.hlsl" -and
    $_.Name -ne "StressTestCS_Simple.hlsl" -and
    $_.Name -ne "StressTestCS_Texture.hlsl" -and
    $_.Name -ne "StressTestCS_Debug.hlsl"
}

Write-Host "Found $($hlslFiles.Count) HLSL files to optimize" -ForegroundColor Cyan

# Initialize summary log
$summaryLog = Join-Path "result" "optimization_summary.log"
"HLSL Optimization Summary - $(Get-Date)" | Out-File -FilePath $summaryLog -Encoding UTF8
"=" * 50 | Out-File -FilePath $summaryLog -Append -Encoding UTF8

# Process each HLSL file
foreach ($file in $hlslFiles) {
    Write-Host "`nProcessing: $($file.Name)" -ForegroundColor White

    # Determine shader stage based on filename and content
    $stage = ""
    if ($file.Name -like "*VS*" -or $file.Name -like "*Vert*") {
        $stage = "vertex"
    } elseif ($file.Name -like "*PS*" -or $file.Name -like "*Pixel*") {
        $stage = "pixel"
    } elseif ($file.Name -like "*CS*" -or $file.Name -like "*Compute*") {
        $stage = "compute"
    } else {
        # Check file content for shader type indicators
        $content = Get-Content $file.FullName -Raw
        if ($content -match '\[numthreads\(' -or $content -match 'SV_DispatchThreadID' -or $content -match 'SV_GroupThreadID') {
            $stage = "compute"
            Write-Host "  Info: Detected compute shader from content" -ForegroundColor Cyan
        } elseif ($content -match 'SV_Position' -or $content -match ': POSITION') {
            $stage = "vertex"
            Write-Host "  Info: Detected vertex shader from content" -ForegroundColor Cyan
        } elseif ($content -match 'SV_Target' -or $content -match ': COLOR') {
            $stage = "pixel"
            Write-Host "  Info: Detected pixel shader from content" -ForegroundColor Cyan
        } else {
            # Default to vertex shader if can't determine
            $stage = "vertex"
            Write-Host "  Warning: Could not determine shader stage, defaulting to vertex" -ForegroundColor Yellow
        }
    }

    $outputFile = Join-Path "result" $file.Name

    try {
        # Run hlsloptconv to optimize the shader
        # Input: HLSL, Output: HLSL Modern, Stage: determined from filename
        $arguments = @(
            "-f", "hlsl_modern"
            "-s", $stage
            "-o", $outputFile
            "-O", "2"  # Optimization level 2 (standard)
            $file.FullName
        )

        Write-Host "  Command: $hlsloptconv $($arguments -join ' ')" -ForegroundColor Gray

        $result = & $hlsloptconv @arguments

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  [OK] SUCCESS - Optimized shader saved to $outputFile" -ForegroundColor Green
            "`n$($file.Name): SUCCESS (Stage: $stage)" | Out-File -FilePath $summaryLog -Append -Encoding UTF8
        } else {
            Write-Host "  [FAIL] OPTIMIZATION FAILED" -ForegroundColor Red
            "`n$($file.Name): FAILED (Stage: $stage)" | Out-File -FilePath $summaryLog -Append -Encoding UTF8
            "Error: $result" | Out-File -FilePath $summaryLog -Append -Encoding UTF8
        }
    } catch {
        Write-Host "  [ERROR] EXCEPTION: $($_.Exception.Message)" -ForegroundColor Red
        "`n$($file.Name): EXCEPTION (Stage: $stage)" | Out-File -FilePath $summaryLog -Append -Encoding UTF8
        "Exception: $($_.Exception.Message)" | Out-File -FilePath $summaryLog -Append -Encoding UTF8
    }
}

Write-Host "`n[DONE] Shader optimization complete!" -ForegroundColor Green
Write-Host "  - Optimized shaders: result\" -ForegroundColor Cyan
Write-Host "  - Summary log: result\optimization_summary.log" -ForegroundColor Cyan

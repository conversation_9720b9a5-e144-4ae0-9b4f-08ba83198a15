; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 139
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %gl_VertexIndex %gl_InstanceIndex %in_var_COLOR %in_var_NORMAL %in_var_TANGENT %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %gl_Position %out_var_TEXCOORD0 %out_var_NORMAL %out_var_POSITION
               OpSource HLSL 600
               OpName %type_ConstantBuffer_Struct__MeshViewerVSCB "type.ConstantBuffer.Struct__MeshViewerVSCB"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 0 "RemapRanges"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 1 "ViewMode"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 2 "_padding0"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 3 "ViewProjMtx"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 4 "ViewerColor"
               OpName %_MeshViewerVSCB "_MeshViewerVSCB"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_COLOR "in.var.COLOR"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TANGENT "in.var.TANGENT"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_NORMAL "out.var.NORMAL"
               OpName %out_var_POSITION "out.var.POSITION"
               OpName %main "main"
               OpDecorate %gl_VertexIndex BuiltIn VertexIndex
               OpDecorate %gl_InstanceIndex BuiltIn InstanceIndex
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_COLOR Location 1
               OpDecorate %in_var_NORMAL Location 2
               OpDecorate %in_var_TANGENT Location 3
               OpDecorate %in_var_TEXCOORD0 Location 4
               OpDecorate %in_var_TEXCOORD1 Location 5
               OpDecorate %in_var_TEXCOORD2 Location 6
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_NORMAL Location 1
               OpDecorate %out_var_POSITION Location 2
               OpDecorate %_MeshViewerVSCB DescriptorSet 0
               OpDecorate %_MeshViewerVSCB Binding 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 0 Offset 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 1 Offset 4
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 2 Offset 8
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 3 Offset 16
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 3 MatrixStride 16
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 3 RowMajor
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 4 Offset 80
               OpDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %float = OpTypeFloat 32
    %v4float = OpTypeVector %float 4
    %v3float = OpTypeVector %float 3
    %float_1 = OpConstant %float 1
    %float_0 = OpConstant %float 0
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
         %30 = OpConstantComposite %v3float %float_1 %float_1 %float_1
    %v2float = OpTypeVector %float 2
%mat4v4float = OpTypeMatrix %v4float 4
%type_ConstantBuffer_Struct__MeshViewerVSCB = OpTypeStruct %uint %int %v2float %mat4v4float %v4float
%_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerVSCB = OpTypePointer Uniform %type_ConstantBuffer_Struct__MeshViewerVSCB
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_uint = OpTypePointer Input %uint
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_int = OpTypePointer Input %int
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
       %void = OpTypeVoid
         %42 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
       %bool = OpTypeBool
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_MeshViewerVSCB = OpVariable %_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerVSCB Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%gl_VertexIndex = OpVariable %_ptr_Input_uint Input
%gl_InstanceIndex = OpVariable %_ptr_Input_uint Input
%in_var_COLOR = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TANGENT = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_int Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_int Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v4float Output
%out_var_NORMAL = OpVariable %_ptr_Output_v3float Output
%out_var_POSITION = OpVariable %_ptr_Output_v3float Output
  %float_0_5 = OpConstant %float 0.5
         %49 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
       %main = OpFunction %void None %42
         %50 = OpLabel
         %51 = OpLoad %v3float %in_var_POSITION
         %52 = OpLoad %uint %gl_VertexIndex
         %53 = OpLoad %uint %gl_InstanceIndex
         %54 = OpLoad %v3float %in_var_COLOR
         %55 = OpLoad %v3float %in_var_NORMAL
         %56 = OpLoad %v4float %in_var_TANGENT
         %57 = OpLoad %v2float %in_var_TEXCOORD0
         %58 = OpLoad %int %in_var_TEXCOORD1
         %59 = OpLoad %int %in_var_TEXCOORD2
         %60 = OpCompositeExtract %float %51 0
         %61 = OpCompositeExtract %float %51 1
         %62 = OpCompositeExtract %float %51 2
         %63 = OpCompositeConstruct %v4float %60 %61 %62 %float_1
         %64 = OpAccessChain %_ptr_Uniform_mat4v4float %_MeshViewerVSCB %int_3
         %65 = OpLoad %mat4v4float %64
         %66 = OpMatrixTimesVector %v4float %65 %63
         %67 = OpAccessChain %_ptr_Uniform_int %_MeshViewerVSCB %int_1
         %68 = OpLoad %int %67
               OpSelectionMerge %69 None
               OpSwitch %68 %70 0 %71 1 %72 2 %73 3 %74 4 %75 5 %76 6 %77 7 %78 8 %79 9 %80 10 %81
         %71 = OpLabel
               OpBranch %69
         %72 = OpLabel
         %82 = OpVectorShuffle %v3float %66 %66 0 1 2
         %83 = OpCompositeExtract %float %66 3
         %84 = OpCompositeConstruct %v3float %83 %83 %83
         %85 = OpFDiv %v3float %82 %84
         %86 = OpCompositeExtract %float %85 0
         %87 = OpCompositeExtract %float %85 1
         %88 = OpCompositeExtract %float %85 2
         %89 = OpCompositeConstruct %v4float %86 %87 %88 %float_1
               OpBranch %69
         %73 = OpLabel
         %90 = OpConvertUToF %float %52
         %91 = OpCompositeConstruct %v4float %90 %float_0 %float_0 %float_1
               OpBranch %69
         %74 = OpLabel
         %92 = OpConvertUToF %float %53
         %93 = OpCompositeConstruct %v4float %92 %float_0 %float_0 %float_1
               OpBranch %69
         %75 = OpLabel
         %94 = OpCompositeExtract %float %54 0
         %95 = OpCompositeExtract %float %54 1
         %96 = OpCompositeExtract %float %54 2
         %97 = OpCompositeConstruct %v4float %94 %95 %96 %float_1
               OpBranch %69
         %76 = OpLabel
         %98 = OpExtInst %v3float %1 Normalize %55
         %99 = OpAccessChain %_ptr_Uniform_uint %_MeshViewerVSCB %int_0
        %100 = OpLoad %uint %99
        %101 = OpINotEqual %bool %100 %uint_0
               OpSelectionMerge %102 None
               OpBranchConditional %101 %103 %104
        %103 = OpLabel
        %105 = OpFAdd %v3float %98 %30
        %106 = OpFMul %v3float %105 %49
               OpBranch %102
        %104 = OpLabel
               OpBranch %102
        %102 = OpLabel
        %107 = OpPhi %v3float %106 %103 %98 %104
        %108 = OpCompositeExtract %float %107 0
        %109 = OpCompositeExtract %float %107 1
        %110 = OpCompositeExtract %float %107 2
        %111 = OpCompositeConstruct %v4float %108 %109 %110 %float_1
               OpBranch %69
         %77 = OpLabel
        %112 = OpVectorShuffle %v3float %56 %56 0 1 2
        %113 = OpExtInst %v3float %1 Normalize %112
        %114 = OpAccessChain %_ptr_Uniform_uint %_MeshViewerVSCB %int_0
        %115 = OpLoad %uint %114
        %116 = OpINotEqual %bool %115 %uint_0
               OpSelectionMerge %117 None
               OpBranchConditional %116 %118 %119
        %118 = OpLabel
        %120 = OpFAdd %v3float %113 %30
        %121 = OpFMul %v3float %120 %49
               OpBranch %117
        %119 = OpLabel
               OpBranch %117
        %117 = OpLabel
        %122 = OpPhi %v3float %121 %118 %113 %119
        %123 = OpCompositeExtract %float %122 0
        %124 = OpCompositeExtract %float %122 1
        %125 = OpCompositeExtract %float %122 2
        %126 = OpCompositeConstruct %v4float %123 %124 %125 %float_1
               OpBranch %69
         %78 = OpLabel
        %127 = OpCompositeExtract %float %57 0
        %128 = OpCompositeExtract %float %57 1
        %129 = OpCompositeConstruct %v4float %127 %128 %float_0 %float_1
               OpBranch %69
         %79 = OpLabel
        %130 = OpConvertSToF %float %58
        %131 = OpCompositeConstruct %v4float %130 %float_0 %float_0 %float_1
               OpBranch %69
         %80 = OpLabel
        %132 = OpConvertSToF %float %59
        %133 = OpCompositeConstruct %v4float %132 %float_0 %float_0 %float_1
               OpBranch %69
         %81 = OpLabel
        %134 = OpAccessChain %_ptr_Uniform_v4float %_MeshViewerVSCB %int_4
        %135 = OpLoad %v4float %134
               OpBranch %69
         %70 = OpLabel
        %136 = OpAccessChain %_ptr_Uniform_v4float %_MeshViewerVSCB %int_4
        %137 = OpLoad %v4float %136
               OpBranch %69
         %69 = OpLabel
        %138 = OpPhi %v4float %63 %71 %89 %72 %91 %73 %93 %74 %97 %75 %111 %102 %126 %117 %129 %78 %131 %79 %133 %80 %135 %81 %137 %70
               OpStore %gl_Position %66
               OpStore %out_var_TEXCOORD0 %138
               OpStore %out_var_NORMAL %55
               OpStore %out_var_POSITION %51
               OpReturn
               OpFunctionEnd

; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 117
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_NORMAL %in_var_POSITION %out_var_SV_Target0
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_ConstantBuffer_Struct__MeshViewerPSCB "type.ConstantBuffer.Struct__MeshViewerPSCB"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerPSCB 0 "CameraPos"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerPSCB 1 "ViewMode"
               OpName %_MeshViewerPSCB "_MeshViewerPSCB"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %out_var_SV_Target0 "out.var.SV_Target0"
               OpName %main "main"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_POSITION Location 2
               OpDecorate %out_var_SV_Target0 Location 0
               OpDecorate %_MeshViewerPSCB DescriptorSet 0
               OpDecorate %_MeshViewerPSCB Binding 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerPSCB 0 Offset 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerPSCB 1 Offset 12
               OpDecorate %type_ConstantBuffer_Struct__MeshViewerPSCB Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
     %int_11 = OpConstant %int 11
      %float = OpTypeFloat 32
    %v4float = OpTypeVector %float 4
    %float_2 = OpConstant %float 2
    %float_3 = OpConstant %float 3
    %v3float = OpTypeVector %float 3
         %18 = OpConstantComposite %v3float %float_2 %float_3 %float_2
    %float_1 = OpConstant %float 1
%float_0_800000012 = OpConstant %float 0.800000012
         %21 = OpConstantComposite %v3float %float_0_800000012 %float_0_800000012 %float_0_800000012
    %float_0 = OpConstant %float 0
%float_0_0199999996 = OpConstant %float 0.0199999996
         %24 = OpConstantComposite %v3float %float_0_0199999996 %float_0_0199999996 %float_0_0199999996
    %float_4 = OpConstant %float 4
%float_0_00100000005 = OpConstant %float 0.00100000005
%float_0_318309873 = OpConstant %float 0.318309873
    %float_5 = OpConstant %float 5
%type_ConstantBuffer_Struct__MeshViewerPSCB = OpTypeStruct %v3float %int
%_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerPSCB = OpTypePointer Uniform %type_ConstantBuffer_Struct__MeshViewerPSCB
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %34 = OpTypeFunction %void
%_ptr_Uniform_int = OpTypePointer Uniform %int
       %bool = OpTypeBool
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_MeshViewerPSCB = OpVariable %_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerPSCB Uniform
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v4float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%out_var_SV_Target0 = OpVariable %_ptr_Output_v4float Output
         %38 = OpConstantComposite %v3float %float_2 %float_2 %float_2
%float_0_0399999991 = OpConstant %float 0.0399999991
         %40 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
%float_0_959999979 = OpConstant %float 0.959999979
         %42 = OpConstantComposite %v3float %float_0_959999979 %float_0_959999979 %float_0_959999979
%float_n0_974399984 = OpConstant %float -0.974399984
%float_0_00814873353 = OpConstant %float 0.00814873353
%float_0_0800000057 = OpConstant %float 0.0800000057
%float_0_920000017 = OpConstant %float 0.920000017
       %main = OpFunction %void None %34
         %47 = OpLabel
         %48 = OpLoad %v4float %in_var_TEXCOORD0
         %49 = OpLoad %v3float %in_var_NORMAL
         %50 = OpLoad %v3float %in_var_POSITION
         %51 = OpAccessChain %_ptr_Uniform_int %_MeshViewerPSCB %int_1
         %52 = OpLoad %int %51
         %53 = OpIEqual %bool %52 %int_11
               OpSelectionMerge %54 None
               OpBranchConditional %53 %55 %56
         %55 = OpLabel
         %57 = OpAccessChain %_ptr_Uniform_v3float %_MeshViewerPSCB %int_0
         %58 = OpLoad %v3float %57
         %59 = OpFSub %v3float %58 %50
         %60 = OpExtInst %v3float %1 Normalize %59
         %61 = OpFSub %v3float %18 %50
         %62 = OpExtInst %v3float %1 Normalize %61
         %63 = OpExtInst %float %1 Length %61
         %64 = OpFMul %float %63 %63
         %65 = OpFAdd %float %float_1 %64
         %66 = OpFDiv %float %float_1 %65
         %67 = OpVectorTimesScalar %v3float %38 %66
         %68 = OpDot %float %62 %49
         %69 = OpExtInst %float %1 NMax %68 %float_0
         %70 = OpVectorTimesScalar %v3float %67 %69
         %71 = OpFAdd %v3float %60 %62
         %72 = OpExtInst %v3float %1 Normalize %71
         %73 = OpDot %float %49 %60
         %74 = OpExtInst %float %1 FClamp %73 %float_0 %float_1
         %75 = OpDot %float %49 %62
         %76 = OpExtInst %float %1 FClamp %75 %float_0 %float_1
         %77 = OpDot %float %49 %72
         %78 = OpExtInst %float %1 FClamp %77 %float_0 %float_1
         %79 = OpDot %float %60 %72
         %80 = OpExtInst %float %1 FClamp %79 %float_0 %float_1
         %81 = OpFSub %float %float_1 %80
         %82 = OpExtInst %float %1 Pow %81 %float_5
         %83 = OpVectorTimesScalar %v3float %42 %82
         %84 = OpFAdd %v3float %40 %83
         %85 = OpFMul %float %78 %78
         %86 = OpFMul %float %85 %float_n0_974399984
         %87 = OpFAdd %float %86 %float_1
         %88 = OpFMul %float %87 %87
         %89 = OpFDiv %float %float_0_00814873353 %88
         %90 = OpExtInst %float %1 NMax %76 %float_0_00100000005
         %91 = OpFMul %float %76 %float_0_920000017
         %92 = OpFAdd %float %91 %float_0_0800000057
         %93 = OpFDiv %float %90 %92
         %94 = OpExtInst %float %1 NMax %74 %float_0_00100000005
         %95 = OpFMul %float %74 %float_0_920000017
         %96 = OpFAdd %float %95 %float_0_0800000057
         %97 = OpFDiv %float %94 %96
         %98 = OpFMul %float %93 %97
         %99 = OpVectorTimesScalar %v3float %84 %89
        %100 = OpVectorTimesScalar %v3float %99 %98
        %101 = OpFMul %float %float_4 %94
        %102 = OpFMul %float %101 %90
        %103 = OpCompositeConstruct %v3float %102 %102 %102
        %104 = OpFDiv %v3float %100 %103
        %105 = OpFSub %v3float %42 %83
        %106 = OpFMul %v3float %21 %105
        %107 = OpVectorTimesScalar %v3float %106 %float_1
        %108 = OpVectorTimesScalar %v3float %107 %float_0_318309873
        %109 = OpFAdd %v3float %108 %104
        %110 = OpFMul %v3float %70 %109
        %111 = OpFAdd %v3float %110 %24
        %112 = OpCompositeExtract %float %111 0
        %113 = OpCompositeExtract %float %111 1
        %114 = OpCompositeExtract %float %111 2
        %115 = OpCompositeConstruct %v4float %112 %113 %114 %float_1
               OpBranch %54
         %56 = OpLabel
               OpBranch %54
         %54 = OpLabel
        %116 = OpPhi %v4float %115 %55 %48 %56
               OpStore %out_var_SV_Target0 %116
               OpReturn
               OpFunctionEnd

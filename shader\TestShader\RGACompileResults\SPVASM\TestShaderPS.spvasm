; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 189
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD6 %in_var_TEXCOORD7 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_2d_image "type.2d.image"
               OpName %DiffuseTexture "DiffuseTexture"
               OpName %NormalTexture "NormalTexture"
               OpName %SpecularTexture "SpecularTexture"
               OpName %EmissiveTexture "EmissiveTexture"
               OpName %type_cube_image "type.cube.image"
               OpName %EnvironmentTexture "EnvironmentTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %type_MaterialConstants "type.MaterialConstants"
               OpMemberName %type_MaterialConstants 0 "WorldMatrix"
               OpMemberName %type_MaterialConstants 1 "ViewMatrix"
               OpMemberName %type_MaterialConstants 2 "ProjectionMatrix"
               OpMemberName %type_MaterialConstants 3 "WorldViewProjectionMatrix"
               OpMemberName %type_MaterialConstants 4 "LightPosition"
               OpMemberName %type_MaterialConstants 5 "LightColor"
               OpMemberName %type_MaterialConstants 6 "MaterialDiffuse"
               OpMemberName %type_MaterialConstants 7 "MaterialSpecular"
               OpMemberName %type_MaterialConstants 8 "CameraPosition"
               OpMemberName %type_MaterialConstants 9 "Time"
               OpMemberName %type_MaterialConstants 10 "SpecularPower"
               OpMemberName %type_MaterialConstants 11 "TextureScale"
               OpName %MaterialConstants "MaterialConstants"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %in_var_TEXCOORD7 "in.var.TEXCOORD7"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %in_var_TEXCOORD7 Location 7
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %DiffuseTexture DescriptorSet 0
               OpDecorate %DiffuseTexture Binding 0
               OpDecorate %NormalTexture DescriptorSet 0
               OpDecorate %NormalTexture Binding 1
               OpDecorate %SpecularTexture DescriptorSet 0
               OpDecorate %SpecularTexture Binding 2
               OpDecorate %EmissiveTexture DescriptorSet 0
               OpDecorate %EmissiveTexture Binding 3
               OpDecorate %EnvironmentTexture DescriptorSet 0
               OpDecorate %EnvironmentTexture Binding 4
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %MaterialConstants DescriptorSet 0
               OpDecorate %MaterialConstants Binding 0
               OpMemberDecorate %type_MaterialConstants 0 Offset 0
               OpMemberDecorate %type_MaterialConstants 0 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 0 RowMajor
               OpMemberDecorate %type_MaterialConstants 1 Offset 64
               OpMemberDecorate %type_MaterialConstants 1 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 1 RowMajor
               OpMemberDecorate %type_MaterialConstants 2 Offset 128
               OpMemberDecorate %type_MaterialConstants 2 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 2 RowMajor
               OpMemberDecorate %type_MaterialConstants 3 Offset 192
               OpMemberDecorate %type_MaterialConstants 3 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 3 RowMajor
               OpMemberDecorate %type_MaterialConstants 4 Offset 256
               OpMemberDecorate %type_MaterialConstants 5 Offset 272
               OpMemberDecorate %type_MaterialConstants 6 Offset 288
               OpMemberDecorate %type_MaterialConstants 7 Offset 304
               OpMemberDecorate %type_MaterialConstants 8 Offset 320
               OpMemberDecorate %type_MaterialConstants 9 Offset 336
               OpMemberDecorate %type_MaterialConstants 10 Offset 340
               OpMemberDecorate %type_MaterialConstants 11 Offset 344
               OpDecorate %type_MaterialConstants Block
      %float = OpTypeFloat 32
    %float_2 = OpConstant %float 2
%float_0_800000012 = OpConstant %float 0.800000012
%float_0_600000024 = OpConstant %float 0.600000024
%float_0_400000006 = OpConstant %float 0.400000006
    %float_1 = OpConstant %float 1
    %v4float = OpTypeVector %float 4
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_0500000007 = OpConstant %float 0.0500000007
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %34 = OpConstantComposite %v3float %float_0_100000001 %float_0_0500000007 %float_0
        %int = OpTypeInt 32 1
      %int_5 = OpConstant %int 5
%float_0_00999999978 = OpConstant %float 0.00999999978
    %v2float = OpTypeVector %float 2
         %39 = OpConstantComposite %v2float %float_0_00999999978 %float_0_00999999978
      %int_9 = OpConstant %int 9
  %float_0_5 = OpConstant %float 0.5
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
       %bool = OpTypeBool
         %45 = OpConstantComposite %v3float %float_1 %float_1 %float_1
      %int_3 = OpConstant %int 3
      %int_7 = OpConstant %int 7
     %int_10 = OpConstant %int 10
    %float_5 = OpConstant %float 5
      %int_6 = OpConstant %int 6
%float_0_300000012 = OpConstant %float 0.300000012
%float_0_454545468 = OpConstant %float 0.454545468
         %53 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
%float_0_899999976 = OpConstant %float 0.899999976
%float_1_00999999 = OpConstant %float 1.00999999
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_cube_image = OpTypeImage %float Cube 2 0 0 1 Unknown
%_ptr_UniformConstant_type_cube_image = OpTypePointer UniformConstant %type_cube_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%mat4v4float = OpTypeMatrix %v4float 4
%type_MaterialConstants = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %mat4v4float %v4float %v4float %v4float %v4float %v4float %float %float %v2float
%_ptr_Uniform_type_MaterialConstants = OpTypePointer Uniform %type_MaterialConstants
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %65 = OpTypeFunction %void
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image = OpTypeSampledImage %type_2d_image
%type_sampled_image_0 = OpTypeSampledImage %type_cube_image
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%DiffuseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%NormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%SpecularTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EmissiveTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EnvironmentTexture = OpVariable %_ptr_UniformConstant_type_cube_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%MaterialConstants = OpVariable %_ptr_Uniform_type_MaterialConstants Uniform
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD7 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
         %69 = OpConstantComposite %v3float %float_0_800000012 %float_0_600000024 %float_0_400000006
%float_2_28000021 = OpConstant %float 2.28000021
   %float_37 = OpConstant %float 37
       %main = OpFunction %void None %65
         %72 = OpLabel
         %73 = OpLoad %v3float %in_var_TEXCOORD1
         %74 = OpLoad %v3float %in_var_TEXCOORD2
         %75 = OpLoad %v3float %in_var_TEXCOORD3
         %76 = OpLoad %v2float %in_var_TEXCOORD4
         %77 = OpLoad %v3float %in_var_TEXCOORD6
         %78 = OpLoad %v3float %in_var_TEXCOORD7
         %79 = OpAccessChain %_ptr_Uniform_float %MaterialConstants %int_9
         %80 = OpLoad %float %79
         %81 = OpFMul %float %80 %float_0_5
         %82 = OpExtInst %float %1 Sin %81
         %83 = OpVectorTimesScalar %v2float %39 %82
         %84 = OpFAdd %v2float %76 %83
         %85 = OpVectorTimesScalar %v2float %84 %float_1
         %86 = OpLoad %type_2d_image %DiffuseTexture
         %87 = OpLoad %type_sampler %LinearSampler
         %88 = OpSampledImage %type_sampled_image %86 %87
         %89 = OpImageSampleImplicitLod %v4float %88 %85 None
         %90 = OpLoad %type_2d_image %NormalTexture
         %91 = OpLoad %type_sampler %LinearSampler
         %92 = OpSampledImage %type_sampled_image %90 %91
         %93 = OpImageSampleImplicitLod %v4float %92 %85 None
         %94 = OpLoad %type_2d_image %SpecularTexture
         %95 = OpLoad %type_sampler %LinearSampler
         %96 = OpSampledImage %type_sampled_image %94 %95
         %97 = OpImageSampleImplicitLod %v4float %96 %85 None
         %98 = OpLoad %type_2d_image %EmissiveTexture
         %99 = OpLoad %type_sampler %LinearSampler
        %100 = OpVectorTimesScalar %v2float %85 %float_1
        %101 = OpSampledImage %type_sampled_image %98 %99
        %102 = OpImageSampleImplicitLod %v4float %101 %100 None
        %103 = OpVectorShuffle %v3float %93 %93 0 1 2
        %104 = OpVectorTimesScalar %v3float %103 %float_2
        %105 = OpFSub %v3float %104 %45
        %106 = OpExtInst %v3float %1 Normalize %105
        %107 = OpExtInst %v3float %1 Normalize %74
        %108 = OpExtInst %v3float %1 Normalize %75
        %109 = OpExtInst %v3float %1 Normalize %73
        %110 = OpCompositeConstruct %mat3v3float %107 %108 %109
        %111 = OpMatrixTimesVector %v3float %110 %106
        %112 = OpVectorTimesScalar %v3float %111 %float_1
        %113 = OpExtInst %v3float %1 Normalize %112
        %114 = OpExtInst %v3float %1 Normalize %78
        %115 = OpExtInst %v3float %1 Normalize %77
        %116 = OpFAdd %v3float %114 %115
        %117 = OpExtInst %v3float %1 Normalize %116
        %118 = OpDot %float %113 %114
        %119 = OpExtInst %float %1 NMax %float_0 %118
        %120 = OpDot %float %113 %117
        %121 = OpExtInst %float %1 NMax %float_0 %120
        %122 = OpAccessChain %_ptr_Uniform_float %MaterialConstants %int_10
        %123 = OpLoad %float %122
        %124 = OpExtInst %float %1 Pow %121 %123
        %125 = OpDot %float %115 %113
        %126 = OpFSub %float %float_1 %125
        %127 = OpExtInst %float %1 Pow %126 %float_5
        %128 = OpFNegate %v3float %115
        %129 = OpExtInst %v3float %1 Reflect %128 %113
        %130 = OpLoad %type_cube_image %EnvironmentTexture
        %131 = OpLoad %type_sampler %LinearSampler
        %132 = OpSampledImage %type_sampled_image_0 %130 %131
        %133 = OpImageSampleImplicitLod %v4float %132 %129 None
        %134 = OpVectorShuffle %v3float %89 %89 0 1 2
        %135 = OpFMul %v3float %134 %69
        %136 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_6
        %137 = OpLoad %v4float %136
        %138 = OpVectorShuffle %v3float %137 %137 0 1 2
        %139 = OpFMul %v3float %135 %138
        %140 = OpVectorTimesScalar %v3float %139 %119
        %141 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_5
        %142 = OpLoad %v4float %141
        %143 = OpVectorShuffle %v3float %142 %142 0 1 2
        %144 = OpFMul %v3float %140 %143
        %145 = OpVectorShuffle %v3float %97 %97 0 1 2
        %146 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_7
        %147 = OpLoad %v4float %146
        %148 = OpVectorShuffle %v3float %147 %147 0 1 2
        %149 = OpFMul %v3float %145 %148
        %150 = OpVectorTimesScalar %v3float %149 %124
        %151 = OpFMul %v3float %150 %143
        %152 = OpVectorTimesScalar %v3float %151 %float_1
        %153 = OpVectorShuffle %v3float %102 %102 0 1 2
        %154 = OpFMul %v3float %153 %34
        %155 = OpFMul %v3float %154 %138
        %156 = OpVectorShuffle %v3float %133 %133 0 1 2
        %157 = OpVectorTimesScalar %v3float %156 %127
        %158 = OpVectorTimesScalar %v3float %157 %float_0_300000012
        %159 = OpFAdd %v3float %144 %152
        %160 = OpFAdd %v3float %159 %155
        %161 = OpFAdd %v3float %160 %158
        %162 = OpCompositeExtract %float %161 0
        %163 = OpFAdd %float %162 %float_2
        %164 = OpCompositeExtract %float %161 1
        %165 = OpFAdd %float %164 %float_2_28000021
        %166 = OpCompositeExtract %float %161 2
        %167 = OpFAdd %float %166 %float_37
        %168 = OpCompositeConstruct %v3float %163 %165 %167
        %169 = OpFAdd %v3float %168 %45
        %170 = OpFDiv %v3float %168 %169
        %171 = OpExtInst %v3float %1 Pow %170 %53
        %172 = OpCompositeExtract %float %89 3
        %173 = OpAccessChain %_ptr_Uniform_float %MaterialConstants %int_6 %int_3
        %174 = OpLoad %float %173
        %175 = OpFMul %float %172 %174
        %176 = OpVectorTimesScalar %v3float %171 %float_0_899999976
               OpBranch %177
        %177 = OpLabel
        %178 = OpPhi %v3float %176 %72 %179 %180
        %181 = OpPhi %int %int_0 %72 %182 %180
        %183 = OpSLessThan %bool %181 %int_3
               OpLoopMerge %184 %180 None
               OpBranchConditional %183 %180 %184
        %180 = OpLabel
        %179 = OpVectorTimesScalar %v3float %178 %float_1_00999999
        %182 = OpIAdd %int %181 %int_1
               OpBranch %177
        %184 = OpLabel
        %185 = OpCompositeExtract %float %178 0
        %186 = OpCompositeExtract %float %178 1
        %187 = OpCompositeExtract %float %178 2
        %188 = OpCompositeConstruct %v4float %185 %186 %187 %175
               OpStore %out_var_SV_TARGET %188
               OpReturn
               OpFunctionEnd

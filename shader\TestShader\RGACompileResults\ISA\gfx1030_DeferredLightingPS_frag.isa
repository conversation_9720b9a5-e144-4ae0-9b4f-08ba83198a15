_amdgpu_ps_main:
	s_mov_b64 s[34:35], exec                                   // 000000000000: BEA2047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s36, s1                                          // 000000000008: BEA40301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s37, s1                                          // 000000000014: BEA50301
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx16 s[0:15], s[36:37], null                    // 00000000001C: F4100012 FA000000
	s_load_dwordx16 s[16:31], s[36:37], 0x60                   // 000000000024: F4100412 FA000060
	v_interp_p1_f32_e32 v8, v0, attr0.x                        // 00000000002C: C8200000
	v_interp_p1_f32_e32 v9, v0, attr0.y                        // 000000000030: C8240100
	v_interp_p2_f32_e32 v8, v1, attr0.x                        // 000000000034: C8210001
	v_interp_p2_f32_e32 v9, v1, attr0.y                        // 000000000038: C8250101
	s_waitcnt lgkmcnt(0)                                       // 00000000003C: BF8CC07F
	image_sample v[0:3], v[8:9], s[0:7], s[8:11] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000040: F0800F08 00400008
	image_sample v[4:7], v[8:9], s[16:23], s[8:11] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000048: F0800F08 00440408
	s_load_dwordx4 s[4:7], s[36:37], null                      // 000000000050: F4080112 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000058: BF8CC07F
	s_buffer_load_dword s2, s[4:7], 0xc                        // 00000000005C: F4200082 FA00000C
	s_waitcnt lgkmcnt(0)                                       // 000000000064: BF8CC07F
	s_cmp_lt_i32 s2, 1                                         // 000000000068: BF048102
	s_cbranch_scc1 _L0                                         // 00000000006C: BF850136
	s_load_dwordx8 s[16:23], s[36:37], 0x30                    // 000000000070: F40C0412 FA000030
	s_and_b64 exec, exec, s[34:35]                             // 000000000078: 87FE227E
	s_waitcnt lgkmcnt(0)                                       // 00000000007C: BF8CC07F
	image_sample v[12:15], v[8:9], s[16:23], s[8:11] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000080: F0800F08 00440C08
	s_clause 0x1                                               // 000000000088: BFA10001
	s_buffer_load_dwordx2 s[0:1], s[4:7], null                 // 00000000008C: F4240002 FA000000
	s_buffer_load_dword s3, s[4:7], 0x8                        // 000000000094: F42000C2 FA000008
	v_mov_b32_e32 v24, 1.0                                     // 00000000009C: 7E3002F2
	s_waitcnt vmcnt(2)                                         // 0000000000A0: BF8C3F72
	v_add_f32_e32 v22, 0xbd23d70a, v0                          // 0000000000A4: 062C00FF BD23D70A
	v_add_f32_e32 v23, 0xbd23d70a, v1                          // 0000000000AC: 062E02FF BD23D70A
	v_add_f32_e32 v26, 0xbd23d70a, v2                          // 0000000000B4: 063404FF BD23D70A
	s_mov_b32 s33, 0                                           // 0000000000BC: BEA10380
	s_mov_b32 s34, 0                                           // 0000000000C0: BEA20380
	s_waitcnt vmcnt(1) lgkmcnt(0)                              // 0000000000C4: BF8C0071
	v_sub_f32_e32 v11, s1, v5                                  // 0000000000C8: 08160A01
	v_sub_f32_e32 v16, s0, v4                                  // 0000000000CC: 08200800
	v_sub_f32_e32 v18, s3, v6                                  // 0000000000D0: 08240C03
	s_mov_b32 s0, 0xbea2f983                                   // 0000000000D4: BE8003FF BEA2F983
	s_movk_i32 s3, 0x40c                                       // 0000000000DC: B003040C
	v_mul_f32_e32 v10, v11, v11                                // 0000000000E0: 1014170B
	v_fmaak_f32 v28, s0, v3, 0x3ea2f983                        // 0000000000E4: 5A380600 3EA2F983
	v_fmac_f32_e32 v10, v16, v16                               // 0000000000EC: 56142110
	v_fmac_f32_e32 v10, v18, v18                               // 0000000000F0: 56142512
	v_cmp_neq_f32_e32 vcc_lo, 0, v10                           // 0000000000F4: 7C1A1480
	s_waitcnt vmcnt(0)                                         // 0000000000F8: BF8C3F70
	v_fma_f32 v13, v13, 2.0, -1.0                              // 0000000000FC: D54B000D 03CDE90D
	v_fma_f32 v17, v12, 2.0, -1.0                              // 000000000104: D54B0011 03CDE90C
	v_fma_f32 v19, v14, 2.0, -1.0                              // 00000000010C: D54B0013 03CDE90E
	v_rsq_f32_e32 v14, v10                                     // 000000000114: 7E1C5D0A
	v_add_f32_e32 v25, 1.0, v15                                // 000000000118: 06321EF2
	v_mul_f32_e32 v12, v13, v13                                // 00000000011C: 10181B0D
	v_mul_f32_e32 v15, v15, v15                                // 000000000120: 101E1F0F
	v_mov_b32_e32 v10, 0                                       // 000000000124: 7E140280
	v_mul_f32_e32 v25, v25, v25                                // 000000000128: 10323319
	v_fmac_f32_e32 v12, v17, v17                               // 00000000012C: 56182311
	v_mul_f32_e32 v30, v15, v15                                // 000000000130: 103C1F0F
	v_fma_f32 v29, v15, v15, -1.0                              // 000000000134: D54B001D 03CE1F0F
	v_cndmask_b32_e32 v21, 0, v14, vcc_lo                      // 00000000013C: 022A1C80
	v_fmamk_f32 v24, v25, 0xbe000000, v24                      // 000000000140: 58303119 BE000000
	v_fmac_f32_e32 v12, v19, v19                               // 000000000148: 56182713
	v_mul_f32_e32 v25, 0x3e000000, v25                         // 00000000014C: 103232FF 3E000000
	v_mov_b32_e32 v15, 0                                       // 000000000154: 7E1E0280
	v_mul_f32_e32 v14, v21, v16                                // 000000000158: 101C2115
	v_mul_f32_e32 v18, v21, v18                                // 00000000015C: 10242515
	v_rsq_f32_e32 v20, v12                                     // 000000000160: 7E285D0C
	v_cmp_neq_f32_e32 vcc_lo, 0, v12                           // 000000000164: 7C1A1880
	v_mul_f32_e32 v12, v21, v11                                // 000000000168: 10181715
	v_mov_b32_e32 v11, 0                                       // 00000000016C: 7E160280
	v_fmaak_f32 v21, v26, v3, 0x3d23d70a                       // 000000000170: 5A2A071A 3D23D70A
	v_cndmask_b32_e32 v20, 0, v20, vcc_lo                      // 000000000178: 02282880
	v_mul_f32_e32 v13, v20, v13                                // 00000000017C: 101A1B14
	v_mul_f32_e32 v16, v20, v17                                // 000000000180: 10202314
	v_mul_f32_e32 v19, v20, v19                                // 000000000184: 10262714
	v_fmaak_f32 v17, v22, v3, 0x3d23d70a                       // 000000000188: 5A220716 3D23D70A
	v_fmaak_f32 v20, v23, v3, 0x3d23d70a                       // 000000000190: 5A280717 3D23D70A
	v_mul_f32_e32 v27, v12, v13                                // 000000000198: 10361B0C
	v_fma_f32 v22, -v22, v3, 0x3f75c28f                        // 00000000019C: D54B0016 23FE0716 3F75C28F
	v_fma_f32 v23, -v23, v3, 0x3f75c28f                        // 0000000001A8: D54B0017 23FE0717 3F75C28F
	v_fma_f32 v3, -v26, v3, 0x3f75c28f                         // 0000000001B4: D54B0003 23FE071A 3F75C28F
	v_mul_f32_e32 v26, v28, v0                                 // 0000000001C0: 1034011C
	v_fmac_f32_e32 v27, v14, v16                               // 0000000001C4: 5636210E
	v_fmac_f32_e32 v27, v18, v19                               // 0000000001C8: 56362712
	v_max_f32_e32 v31, 0, v27                                  // 0000000001CC: 203E3680
	v_mul_f32_e32 v27, v28, v1                                 // 0000000001D0: 1036031C
	v_mul_f32_e32 v28, v28, v2                                 // 0000000001D4: 1038051C
	v_fma_f32 v32, v31, v24, v25                               // 0000000001D8: D54B0020 0466311F
	v_mul_f32_e32 v30, v31, v30                                // 0000000001E0: 103C3D1F
	v_mul_f32_e32 v31, 4.0, v31                                // 0000000001E4: 103E3EF6
	v_mul_f32_e32 v32, 0x40490fdb, v32                         // 0000000001E8: 104040FF 40490FDB
_L7:
	s_and_b32 s0, s33, -4                                      // 0000000001F0: 8700C421
	s_add_i32 s1, s3, 0xfffffbf4                               // 0000000001F4: 8101FF03 FFFFFBF4
	s_addk_i32 s0, 0x600                                       // 0000000001FC: B7800600
	s_clause 0x1                                               // 000000000200: BFA10001
	s_buffer_load_dwordx4 s[16:19], s[12:15], s1               // 000000000204: F4280406 02000000
	s_buffer_load_dword s20, s[12:15], s0                      // 00000000020C: F4200506 00000000
	s_add_i32 s0, s3, 0xfffffc00                               // 000000000214: 8100FF03 FFFFFC00
	s_waitcnt lgkmcnt(0)                                       // 00000000021C: BF8CC07F
	s_buffer_load_dword s19, s[12:15], s0                      // 000000000220: F42004C6 00000000
	s_cmp_lg_u32 s20, 0                                        // 000000000228: BF078014
	s_cbranch_scc0 _L1                                         // 00000000022C: BF8400C5
	s_cmp_lg_u32 s20, 1                                        // 000000000230: BF078114
	s_mov_b64 s[0:1], -1                                       // 000000000234: BE8004C1
	s_cbranch_scc0 _L2                                         // 000000000238: BF84003C
	v_mov_b32_e32 v36, 1.0                                     // 00000000023C: 7E4802F2
	s_cmp_lg_u32 s20, 2                                        // 000000000240: BF078214
	s_cbranch_scc1 _L3                                         // 000000000244: BF850038
	s_add_i32 s0, s3, -12                                      // 000000000248: 8100CC03
	v_sub_f32_e32 v35, s17, v5                                 // 00000000024C: 08460A11
	s_clause 0x1                                               // 000000000250: BFA10001
	s_buffer_load_dwordx4 s[20:23], s[12:15], s0               // 000000000254: F4280506 00000000
	s_buffer_load_dword s0, s[12:15], s3                       // 00000000025C: F4200006 06000000
	v_sub_f32_e32 v33, s16, v4                                 // 000000000264: 08420810
	v_sub_f32_e32 v37, s18, v6                                 // 000000000268: 084A0C12
	v_mul_f32_e32 v34, v35, v35                                // 00000000026C: 10444723
	s_waitcnt lgkmcnt(0)                                       // 000000000270: BF8CC07F
	v_mul_f32_e64 v40, s19, s19                                // 000000000274: D5080028 00002613
	v_fmac_f32_e32 v34, v33, v33                               // 00000000027C: 56444321
	v_rcp_f32_e32 v40, v40                                     // 000000000280: 7E505528
	v_fmac_f32_e32 v34, v37, v37                               // 000000000284: 56444B25
	v_rsq_f32_e32 v38, v34                                     // 000000000288: 7E4C5D22
	v_mul_f32_e64 v36, s21, s21                                // 00000000028C: D5080024 00002A15
	v_mul_f32_e64 v41, s0, 0.15915494                          // 000000000294: D5080029 0001F000
	v_mul_f32_e64 v42, 0x3e4391d1, s0                          // 00000000029C: D508002A 000000FF 3E4391D1
	v_cmp_neq_f32_e32 vcc_lo, 0, v34                           // 0000000002A8: 7C1A4480
	v_sqrt_f32_e32 v34, v34                                    // 0000000002AC: 7E446722
	v_fmac_f32_e64 v36, s20, s20                               // 0000000002B0: D52B0024 00002814
	v_cndmask_b32_e32 v38, 0, v38, vcc_lo                      // 0000000002B8: 024C4C80
	v_fmac_f32_e64 v36, s22, s22                               // 0000000002BC: D52B0024 00002C16
	v_mul_f32_e32 v43, v34, v34                                // 0000000002C4: 10564522
	v_rsq_f32_e32 v39, v36                                     // 0000000002C8: 7E4E5D24
	v_cmp_neq_f32_e64 s0, 0, v36                               // 0000000002CC: D40D0000 00024880
	v_cos_f32_e32 v36, v41                                     // 0000000002D4: 7E486D29
	v_cos_f32_e32 v41, v42                                     // 0000000002D8: 7E526D2A
	v_mul_f32_e32 v33, v38, v33                                // 0000000002DC: 10424326
	v_mul_f32_e32 v34, v38, v37                                // 0000000002E0: 10444B26
	v_mul_f32_e32 v35, v38, v35                                // 0000000002E4: 10464726
	v_fma_f32 v38, v43, v40, 1.0                               // 0000000002E8: D54B0026 03CA512B
	v_cndmask_b32_e64 v39, 0, v39, s0                          // 0000000002F0: D5010027 00024E80
	v_sub_f32_e32 v36, v41, v36                                // 0000000002F8: 08484929
	v_mul_f32_e32 v42, s20, v39                                // 0000000002FC: 10544E14
	v_mul_f32_e32 v37, s22, v39                                // 000000000300: 104A4E16
	v_mul_f32_e32 v39, s21, v39                                // 000000000304: 104E4E15
	v_rcp_f32_e32 v36, v36                                     // 000000000308: 7E485524
	v_fmac_f32_e32 v41, v33, v42                               // 00000000030C: 56525521
	v_fmac_f32_e32 v41, v34, v37                               // 000000000310: 56524B22
	v_rcp_f32_e32 v37, v38                                     // 000000000314: 7E4A5526
	v_fmac_f32_e32 v41, v39, v35                               // 000000000318: 56524727
	v_mul_f32_e64 v36, v41, v36 clamp                          // 00000000031C: D5088024 00024929
	v_mul_f32_e32 v36, v36, v37                                // 000000000324: 10484B24
_L3:
	s_mov_b64 s[0:1], 0                                        // 000000000328: BE800480
_L2:
	s_andn2_b64 vcc, exec, s[0:1]                              // 00000000032C: 8AEA007E
	s_cbranch_vccnz _L4                                        // 000000000330: BF870014
	s_add_i32 s0, s3, -12                                      // 000000000334: 8100CC03
	v_mov_b32_e32 v36, 1.0                                     // 000000000338: 7E4802F2
	s_buffer_load_dwordx4 s[20:23], s[12:15], s0               // 00000000033C: F4280506 00000000
	s_waitcnt lgkmcnt(0)                                       // 000000000344: BF8CC07F
	v_mul_f32_e64 v33, s21, s21                                // 000000000348: D5080021 00002A15
	v_fmac_f32_e64 v33, s20, s20                               // 000000000350: D52B0021 00002814
	v_fmac_f32_e64 v33, s22, s22                               // 000000000358: D52B0021 00002C16
	v_rsq_f32_e32 v34, v33                                     // 000000000360: 7E445D21
	v_cmp_neq_f32_e32 vcc_lo, 0, v33                           // 000000000364: 7C1A4280
	v_cndmask_b32_e32 v34, 0, v34, vcc_lo                      // 000000000368: 02444480
	v_mul_f32_e64 v33, v34, -s20                               // 00000000036C: D5080021 40002922
	v_mul_f32_e64 v35, v34, -s21                               // 000000000374: D5080023 40002B22
	v_mul_f32_e64 v34, v34, -s22                               // 00000000037C: D5080022 40002D22
_L4:
	s_cbranch_execnz _L5                                       // 000000000384: BF890015
_L8:
	v_sub_f32_e32 v34, s17, v5                                 // 000000000388: 08440A11
	v_sub_f32_e32 v33, s16, v4                                 // 00000000038C: 08420810
	v_sub_f32_e32 v36, s18, v6                                 // 000000000390: 08480C12
	v_mul_f32_e32 v35, v34, v34                                // 000000000394: 10464522
	v_fmac_f32_e32 v35, v33, v33                               // 000000000398: 56464321
	v_fmac_f32_e32 v35, v36, v36                               // 00000000039C: 56464924
	v_rsq_f32_e32 v37, v35                                     // 0000000003A0: 7E4A5D23
	v_cmp_neq_f32_e32 vcc_lo, 0, v35                           // 0000000003A4: 7C1A4680
	v_sqrt_f32_e32 v38, v35                                    // 0000000003A8: 7E4C6723
	v_cndmask_b32_e32 v37, 0, v37, vcc_lo                      // 0000000003AC: 024A4A80
	v_mul_f32_e32 v33, v37, v33                                // 0000000003B0: 10424325
	v_mul_f32_e32 v35, v37, v34                                // 0000000003B4: 10464525
	v_mul_f32_e32 v34, v37, v36                                // 0000000003B8: 10444925
	s_waitcnt lgkmcnt(0)                                       // 0000000003BC: BF8CC07F
	v_mul_f32_e64 v37, s19, s19                                // 0000000003C0: D5080025 00002613
	v_mul_f32_e32 v36, v38, v38                                // 0000000003C8: 10484D26
	v_rcp_f32_e32 v37, v37                                     // 0000000003CC: 7E4A5525
	v_fma_f32 v36, v36, v37, 1.0                               // 0000000003D0: D54B0024 03CA4B24
	v_rcp_f32_e32 v36, v36                                     // 0000000003D8: 7E485524
_L5:
	v_add_f32_e32 v37, v35, v12                                // 0000000003DC: 064A1923
	v_add_f32_e32 v38, v33, v14                                // 0000000003E0: 064C1D21
	v_add_f32_e32 v40, v34, v18                                // 0000000003E4: 06502522
	v_mul_f32_e32 v42, v35, v13                                // 0000000003E8: 10541B23
	s_add_i32 s0, s3, 0xfffffe00                               // 0000000003EC: 8100FF03 FFFFFE00
	v_mul_f32_e32 v39, v37, v37                                // 0000000003F4: 104E4B25
	s_buffer_load_dword s1, s[12:15], s0                       // 0000000003F8: F4200046 00000000
	s_add_i32 s0, s3, 0xfffffdf4                               // 000000000400: 8100FF03 FFFFFDF4
	v_fmac_f32_e32 v42, v33, v16                               // 000000000408: 56542121
	s_waitcnt lgkmcnt(0)                                       // 00000000040C: BF8CC07F
	s_buffer_load_dwordx4 s[16:19], s[12:15], s0               // 000000000410: F4280406 00000000
	v_fmac_f32_e32 v39, v38, v38                               // 000000000418: 564E4D26
	s_add_i32 s0, s34, 1                                       // 00000000041C: 81008122
	v_fmac_f32_e32 v42, v34, v19                               // 000000000420: 56542722
	s_cmp_lt_u32 s0, s2                                        // 000000000424: BF0A0200
	v_fmac_f32_e32 v39, v40, v40                               // 000000000428: 564E5128
	s_cselect_b64 s[20:21], -1, 0                              // 00000000042C: 859480C1
	s_cmp_lt_u32 s34, 31                                       // 000000000430: BF0A9F22
	s_cselect_b64 s[22:23], -1, 0                              // 000000000434: 859680C1
	v_rsq_f32_e32 v41, v39                                     // 000000000438: 7E525D27
	v_cmp_neq_f32_e32 vcc_lo, 0, v39                           // 00000000043C: 7C1A4E80
	s_add_i32 s3, s3, 16                                       // 000000000440: 81039003
	s_add_i32 s33, s33, 4                                      // 000000000444: 81218421
	v_mul_f32_e32 v36, s1, v36                                 // 000000000448: 10484801
	v_cndmask_b32_e32 v39, 0, v41, vcc_lo                      // 00000000044C: 024E5280
	v_mul_f32_e32 v37, v39, v37                                // 000000000450: 104A4B27
	v_mul_f32_e32 v38, v39, v38                                // 000000000454: 104C4D27
	v_mul_f32_e32 v39, v39, v40                                // 000000000458: 104E5127
	v_mul_f32_e32 v41, v37, v13                                // 00000000045C: 10521B25
	v_mul_f32_e32 v37, v37, v12                                // 000000000460: 104A1925
	v_fmac_f32_e32 v41, v38, v16                               // 000000000464: 56522126
	v_fmac_f32_e32 v37, v38, v14                               // 000000000468: 564A1D26
	v_max_f32_e32 v38, 0, v42                                  // 00000000046C: 204C5480
	v_fmac_f32_e32 v41, v39, v19                               // 000000000470: 56522727
	v_fmac_f32_e32 v37, v39, v18                               // 000000000474: 564A2527
	v_fma_f32 v39, v38, v24, v25                               // 000000000478: D54B0027 04663126
	v_mul_f32_e32 v36, v38, v36                                // 000000000480: 10484926
	v_max_f32_e32 v40, 0, v41                                  // 000000000484: 20505280
	v_max_f32_e32 v37, 0, v37                                  // 000000000488: 204A4A80
	v_mul_f32_e32 v39, v32, v39                                // 00000000048C: 104E4F20
	v_fmaak_f32 v41, v38, v31, 0x38d1b717                      // 000000000490: 5A523F26 38D1B717
	v_mul_f32_e32 v38, v38, v30                                // 000000000498: 104C3D26
	v_mul_f32_e32 v40, v40, v40                                // 00000000049C: 10505128
	v_sub_f32_e64 v37, 1.0, v37 clamp                          // 0000000004A0: D5048025 00024AF2
	v_fma_f32 v40, v40, v29, 1.0                               // 0000000004A8: D54B0028 03CA3B28
	v_mul_f32_e32 v40, v40, v40                                // 0000000004B0: 10505128
	v_mul_f32_e32 v39, v40, v39                                // 0000000004B4: 104E4F28
	v_mul_f32_e32 v40, v37, v37                                // 0000000004B8: 10504B25
	v_mul_f32_e32 v39, v39, v41                                // 0000000004BC: 104E5327
	v_mul_f32_e32 v40, v40, v40                                // 0000000004C0: 10505128
	v_rcp_f32_e32 v39, v39                                     // 0000000004C4: 7E4E5527
	v_mul_f32_e32 v37, v37, v40                                // 0000000004C8: 104A5125
	s_waitcnt lgkmcnt(0)                                       // 0000000004CC: BF8CC07F
	v_mul_f32_e32 v40, s16, v36                                // 0000000004D0: 10504810
	v_fma_f32 v41, v37, v22, v17                               // 0000000004D4: D54B0029 04462D25
	v_fma_f32 v42, v37, v23, v20                               // 0000000004DC: D54B002A 04522F25
	v_fma_f32 v37, v37, v3, v21                                // 0000000004E4: D54B0025 04560725
	v_fma_f32 v43, v38, v39, -v26                              // 0000000004EC: D54B002B 846A4F26
	v_fma_f32 v44, v38, v39, -v27                              // 0000000004F4: D54B002C 846E4F26
	v_fma_f32 v38, v38, v39, -v28                              // 0000000004FC: D54B0026 84724F26
	v_mul_f32_e32 v39, s17, v36                                // 000000000504: 104E4811
	v_mul_f32_e32 v36, s18, v36                                // 000000000508: 10484812
	v_fma_f32 v41, v41, v43, v26                               // 00000000050C: D54B0029 046A5729
	v_fma_f32 v42, v42, v44, v27                               // 000000000514: D54B002A 046E592A
	v_fma_f32 v37, v37, v38, v28                               // 00000000051C: D54B0025 04724D25
	s_and_b64 s[16:17], s[20:21], s[22:23]                     // 000000000524: 87901614
	s_and_b64 vcc, exec, s[16:17]                              // 000000000528: 87EA107E
	v_fmac_f32_e32 v10, v40, v41                               // 00000000052C: 56145328
	v_fmac_f32_e32 v11, v39, v42                               // 000000000530: 56165527
	v_fmac_f32_e32 v15, v36, v37                               // 000000000534: 561E4B24
	s_cbranch_vccz _L6                                         // 000000000538: BF860007
	s_mov_b32 s34, s0                                          // 00000000053C: BEA20300
	s_branch _L7                                               // 000000000540: BF82FF2B
_L1:
	s_branch _L8                                               // 000000000544: BF82FF90
_L0:
	s_and_b64 exec, exec, s[34:35]                             // 000000000548: 87FE227E
	v_mov_b32_e32 v15, 0                                       // 00000000054C: 7E1E0280
	v_mov_b32_e32 v11, 0                                       // 000000000550: 7E160280
	v_mov_b32_e32 v10, 0                                       // 000000000554: 7E140280
_L6:
	image_sample v[3:5], v[8:9], s[24:31], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000558: F0800708 00460308
	s_buffer_load_dwordx4 s[0:3], s[4:7], 0x10                 // 000000000560: F4280002 FA000010
	s_waitcnt vmcnt(1)                                         // 000000000568: BF8C3F71
	v_mul_f32_e32 v0, v7, v0                                   // 00000000056C: 10000107
	v_mul_f32_e32 v1, v7, v1                                   // 000000000570: 10020307
	v_mul_f32_e32 v2, v7, v2                                   // 000000000574: 10040507
	s_waitcnt lgkmcnt(0)                                       // 000000000578: BF8CC07F
	v_mul_f32_e32 v0, s0, v0                                   // 00000000057C: 10000000
	v_mul_f32_e32 v1, s1, v1                                   // 000000000580: 10020201
	v_mul_f32_e32 v2, s2, v2                                   // 000000000584: 10040402
	s_waitcnt vmcnt(0)                                         // 000000000588: BF8C3F70
	v_add_f32_e32 v3, v10, v3                                  // 00000000058C: 0606070A
	v_add_f32_e32 v4, v11, v4                                  // 000000000590: 0608090B
	v_add_f32_e32 v5, v15, v5                                  // 000000000594: 060A0B0F
	v_fmac_f32_e32 v3, s3, v0                                  // 000000000598: 56060003
	v_fmac_f32_e32 v4, s3, v1                                  // 00000000059C: 56080203
	v_fmac_f32_e32 v5, s3, v2                                  // 0000000005A0: 560A0403
	v_add_f32_e32 v0, 1.0, v3                                  // 0000000005A4: 060006F2
	v_add_f32_e32 v1, 1.0, v4                                  // 0000000005A8: 060208F2
	v_add_f32_e32 v2, 1.0, v5                                  // 0000000005AC: 06040AF2
	v_rcp_f32_e32 v0, v0                                       // 0000000005B0: 7E005500
	v_rcp_f32_e32 v1, v1                                       // 0000000005B4: 7E025501
	v_rcp_f32_e32 v2, v2                                       // 0000000005B8: 7E045502
	v_mul_f32_e32 v0, v3, v0                                   // 0000000005BC: 10000103
	v_mul_f32_e32 v1, v4, v1                                   // 0000000005C0: 10020304
	v_mul_f32_e32 v2, v5, v2                                   // 0000000005C4: 10040505
	v_mov_b32_e32 v3, 1.0                                      // 0000000005C8: 7E0602F2
	v_log_f32_e32 v0, v0                                       // 0000000005CC: 7E004F00
	v_log_f32_e32 v1, v1                                       // 0000000005D0: 7E024F01
	v_log_f32_e32 v2, v2                                       // 0000000005D4: 7E044F02
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 0000000005D8: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 0000000005E0: 0E0202FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 0000000005E8: 0E0404FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 0000000005F0: 7E004B00
	v_exp_f32_e32 v1, v1                                       // 0000000005F4: 7E024B01
	v_exp_f32_e32 v2, v2                                       // 0000000005F8: 7E044B02
	exp mrt0 v0, v1, v2, v3 done vm                            // 0000000005FC: F800180F 03020100
	s_endpgm                                                   // 000000000604: BF810000

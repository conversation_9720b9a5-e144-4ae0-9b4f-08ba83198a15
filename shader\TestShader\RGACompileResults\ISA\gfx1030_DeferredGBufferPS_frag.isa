_amdgpu_ps_main:
	s_mov_b64 s[40:41], exec                                   // 000000000000: BEA8047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s34, s1                                          // 000000000008: BEA20301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s35, s1                                          // 000000000014: BEA30301
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx4 s[36:39], s[34:35], 0x20                    // 00000000001C: F4080911 FA000020
	s_load_dwordx16 s[0:15], s[34:35], 0x30                    // 000000000024: F4100011 FA000030
	v_interp_p1_f32_e32 v8, v0, attr4.x                        // 00000000002C: C8201000
	v_interp_p1_f32_e32 v9, v0, attr4.y                        // 000000000030: C8241100
	v_interp_p2_f32_e32 v8, v1, attr4.x                        // 000000000034: C8211001
	v_interp_p2_f32_e32 v9, v1, attr4.y                        // 000000000038: C8251101
	s_waitcnt lgkmcnt(0)                                       // 00000000003C: BF8CC07F
	image_sample v[2:4], v[8:9], s[0:7], s[36:39] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000040: F0800708 01200208
	s_clause 0x2                                               // 000000000048: BFA10002
	s_load_dwordx8 s[0:7], s[34:35], null                      // 00000000004C: F40C0011 FA000000
	s_load_dwordx16 s[16:31], s[34:35], 0x70                   // 000000000054: F4100411 FA000070
	s_load_dwordx8 s[44:51], s[34:35], 0xb0                    // 00000000005C: F40C0B11 FA0000B0
	s_and_b64 exec, exec, s[40:41]                             // 000000000064: 87FE287E
	s_waitcnt lgkmcnt(0)                                       // 000000000068: BF8CC07F
	image_sample v[5:7], v[8:9], s[0:7], s[36:39] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000006C: F0800708 01200508
	image_sample v11, v[8:9], s[8:15], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000074: F0800108 01220B08
	image_sample v12, v[8:9], s[16:23], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000007C: F0800108 01240C08
	image_sample v13, v[8:9], s[24:31], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000084: F0800108 01260D08
	image_sample v[8:10], v[8:9], s[44:51], s[36:39] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000008C: F0800708 012B0808
	s_load_dwordx4 s[4:7], s[34:35], null                      // 000000000094: F4080111 FA000000
	v_interp_p1_f32_e32 v25, v0, attr1.y                       // 00000000009C: C8640500
	v_interp_p1_f32_e32 v19, v0, attr3.y                       // 0000000000A0: C84C0D00
	v_interp_p1_f32_e32 v24, v0, attr1.x                       // 0000000000A4: C8600400
	s_waitcnt vmcnt(5)                                         // 0000000000A8: BF8C3F75
	v_fma_f32 v3, v3, 2.0, -1.0                                // 0000000000AC: D54B0003 03CDE903
	v_fma_f32 v4, v4, 2.0, -1.0                                // 0000000000B4: D54B0004 03CDE904
	v_interp_p2_f32_e32 v25, v1, attr1.y                       // 0000000000BC: C8650501
	v_interp_p1_f32_e32 v18, v0, attr3.x                       // 0000000000C0: C8480C00
	v_interp_p1_f32_e32 v22, v0, attr2.y                       // 0000000000C4: C8580900
	v_fma_f32 v2, v2, 2.0, -1.0                                // 0000000000C8: D54B0002 03CDE902
	v_interp_p1_f32_e32 v26, v0, attr1.z                       // 0000000000D0: C8680600
	v_interp_p2_f32_e32 v19, v1, attr3.y                       // 0000000000D4: C84D0D01
	v_interp_p2_f32_e32 v24, v1, attr1.x                       // 0000000000D8: C8610401
	v_mul_f32_e32 v25, v4, v25                                 // 0000000000DC: 10323304
	v_interp_p1_f32_e32 v20, v0, attr3.z                       // 0000000000E0: C8500E00
	v_interp_p1_f32_e32 v21, v0, attr2.x                       // 0000000000E4: C8540800
	v_interp_p2_f32_e32 v18, v1, attr3.x                       // 0000000000E8: C8490C01
	v_interp_p2_f32_e32 v22, v1, attr2.y                       // 0000000000EC: C8590901
	v_interp_p2_f32_e32 v26, v1, attr1.z                       // 0000000000F0: C8690601
	s_waitcnt lgkmcnt(0)                                       // 0000000000F4: BF8CC07F
	s_clause 0x1                                               // 0000000000F8: BFA10001
	s_buffer_load_dwordx4 s[0:3], s[4:7], 0x20                 // 0000000000FC: F4280002 FA000020
	s_buffer_load_dword s8, s[4:7], 0x30                       // 000000000104: F4200202 FA000030
	v_mul_f32_e32 v24, v4, v24                                 // 00000000010C: 10303104
	v_interp_p1_f32_e32 v23, v0, attr2.z                       // 000000000110: C85C0A00
	v_interp_p2_f32_e32 v20, v1, attr3.z                       // 000000000114: C8510E01
	v_interp_p2_f32_e32 v21, v1, attr2.x                       // 000000000118: C8550801
	v_mul_f32_e32 v4, v4, v26                                  // 00000000011C: 10083504
	v_interp_p1_f32_e32 v15, v0, attr5.x                       // 000000000120: C83C1400
	v_interp_p2_f32_e32 v23, v1, attr2.z                       // 000000000124: C85D0A01
	v_interp_p1_f32_e32 v16, v0, attr5.y                       // 000000000128: C8401500
	v_interp_p1_f32_e32 v17, v0, attr5.z                       // 00000000012C: C8441600
	v_interp_p1_f32_e32 v14, v0, attr6.x                       // 000000000130: C8381800
	v_interp_p1_f32_e32 v27, v0, attr0.x                       // 000000000134: C86C0000
	v_interp_p2_f32_e32 v15, v1, attr5.x                       // 000000000138: C83D1401
	v_interp_p2_f32_e32 v16, v1, attr5.y                       // 00000000013C: C8411501
	v_interp_p2_f32_e32 v17, v1, attr5.z                       // 000000000140: C8451601
	v_interp_p2_f32_e32 v14, v1, attr6.x                       // 000000000144: C8391801
	v_interp_p2_f32_e32 v27, v1, attr0.x                       // 000000000148: C86D0001
	s_waitcnt lgkmcnt(0)                                       // 00000000014C: BF8CC07F
	v_mul_f32_e32 v3, s8, v3                                   // 000000000150: 10060608
	v_mul_f32_e32 v2, s8, v2                                   // 000000000154: 10040408
	s_clause 0x1                                               // 000000000158: BFA10001
	s_buffer_load_dwordx4 s[8:11], s[4:7], null                // 00000000015C: F4280202 FA000000
	s_buffer_load_dwordx2 s[4:5], s[4:7], 0x10                 // 000000000164: F4240102 FA000010
	v_fmac_f32_e32 v25, v3, v19                                // 00000000016C: 56322703
	v_fmac_f32_e32 v24, v3, v18                                // 000000000170: 56302503
	v_fmac_f32_e32 v4, v3, v20                                 // 000000000174: 56082903
	v_interp_p1_f32_e32 v18, v0, attr0.y                       // 000000000178: C8480100
	v_interp_p1_f32_e32 v0, v0, attr0.z                        // 00000000017C: C8000200
	v_fmac_f32_e32 v25, v2, v22                                // 000000000180: 56322D02
	v_fmac_f32_e32 v24, v2, v21                                // 000000000184: 56302B02
	v_fmac_f32_e32 v4, v2, v23                                 // 000000000188: 56082F02
	v_interp_p2_f32_e32 v18, v1, attr0.y                       // 00000000018C: C8490101
	v_interp_p2_f32_e32 v0, v1, attr0.z                        // 000000000190: C8010201
	v_mul_f32_e32 v3, v25, v25                                 // 000000000194: 10063319
	v_fmac_f32_e32 v3, v24, v24                                // 000000000198: 56063118
	v_fmac_f32_e32 v3, v4, v4                                  // 00000000019C: 56060904
	v_rsq_f32_e32 v3, v3                                       // 0000000001A0: 7E065D03
	s_waitcnt vmcnt(4)                                         // 0000000001A4: BF8C3F74
	v_mul_f32_e32 v1, v5, v15                                  // 0000000001A8: 10021F05
	v_mul_f32_e32 v2, v6, v16                                  // 0000000001AC: 10042106
	v_mul_f32_e32 v5, v7, v17                                  // 0000000001B0: 100A2307
	s_waitcnt vmcnt(3) lgkmcnt(0)                              // 0000000001B4: BF8C0073
	v_mul_f32_e32 v6, s11, v11                                 // 0000000001B8: 100C160B
	s_waitcnt vmcnt(2)                                         // 0000000001BC: BF8C3F72
	v_mul_f32_e32 v7, s4, v12                                  // 0000000001C0: 100E1804
	s_waitcnt vmcnt(1)                                         // 0000000001C4: BF8C3F71
	v_mul_f32_e32 v11, s5, v13                                 // 0000000001C8: 10161A05
	s_waitcnt vmcnt(0)                                         // 0000000001CC: BF8C3F70
	v_mul_f32_e32 v8, s0, v8                                   // 0000000001D0: 10101000
	v_mul_legacy_f32_e32 v12, v24, v3                          // 0000000001D4: 0E180718
	v_mul_legacy_f32_e32 v13, v25, v3                          // 0000000001D8: 0E1A0719
	v_mul_legacy_f32_e32 v3, v4, v3                            // 0000000001DC: 0E060704
	v_mul_f32_e32 v9, s1, v9                                   // 0000000001E0: 10121201
	v_mul_f32_e32 v10, s2, v10                                 // 0000000001E4: 10141402
	v_mul_f32_e32 v1, s8, v1                                   // 0000000001E8: 10020208
	v_mul_f32_e32 v2, s9, v2                                   // 0000000001EC: 10040409
	v_mul_f32_e32 v4, s10, v5                                  // 0000000001F0: 10080A0A
	v_mul_f32_e32 v5, s3, v8                                   // 0000000001F4: 100A1003
	v_fma_f32 v8, v12, 0.5, 0.5                                // 0000000001F8: D54B0008 03C1E10C
	v_fma_f32 v12, v13, 0.5, 0.5                               // 000000000200: D54B000C 03C1E10D
	v_fma_f32 v3, v3, 0.5, 0.5                                 // 000000000208: D54B0003 03C1E103
	v_mul_f32_e32 v9, s3, v9                                   // 000000000210: 10121203
	v_mul_f32_e32 v10, s3, v10                                 // 000000000214: 10141403
	exp mrt0 v1, v2, v4, v6 vm                                 // 000000000218: F800100F 06040201
	exp mrt1 v8, v12, v3, v7 vm                                // 000000000220: F800101F 07030C08
	exp mrt2 v27, v18, v0, v11 vm                              // 000000000228: F800102F 0B00121B
	exp mrt3 v5, v9, v10, v14 done vm                          // 000000000230: F800183F 0E0A0905
	s_endpgm                                                   // 000000000238: BF810000

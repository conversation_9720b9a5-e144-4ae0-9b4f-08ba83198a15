; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 352
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_TEXCOORD1 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_PostProcessParams "type.PostProcessParams"
               OpMemberName %type_PostProcessParams 0 "TexelSize"
               OpMemberName %type_PostProcessParams 1 "BlurRadius"
               OpMemberName %type_PostProcessParams 2 "Time"
               OpMemberName %type_PostProcessParams 3 "Brightness"
               OpMemberName %type_PostProcessParams 4 "Contrast"
               OpMemberName %type_PostProcessParams 5 "Saturation"
               OpMemberName %type_PostProcessParams 6 "Gamma"
               OpMemberName %type_PostProcessParams 7 "VignetteStrength"
               OpMemberName %type_PostProcessParams 8 "ChromaticAberration"
               OpMemberName %type_PostProcessParams 9 "FilmGrain"
               OpMemberName %type_PostProcessParams 10 "EffectType"
               OpName %PostProcessParams "PostProcessParams"
               OpName %type_2d_image "type.2d.image"
               OpName %SceneTexture "SceneTexture"
               OpName %BloomTexture "BloomTexture"
               OpName %DepthTexture "DepthTexture"
               OpName %NoiseTexture "NoiseTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %PointSampler "PointSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %PostProcessParams DescriptorSet 0
               OpDecorate %PostProcessParams Binding 0
               OpDecorate %SceneTexture DescriptorSet 0
               OpDecorate %SceneTexture Binding 0
               OpDecorate %BloomTexture DescriptorSet 0
               OpDecorate %BloomTexture Binding 1
               OpDecorate %DepthTexture DescriptorSet 0
               OpDecorate %DepthTexture Binding 2
               OpDecorate %NoiseTexture DescriptorSet 0
               OpDecorate %NoiseTexture Binding 3
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %PointSampler DescriptorSet 0
               OpDecorate %PointSampler Binding 1
               OpMemberDecorate %type_PostProcessParams 0 Offset 0
               OpMemberDecorate %type_PostProcessParams 1 Offset 8
               OpMemberDecorate %type_PostProcessParams 2 Offset 12
               OpMemberDecorate %type_PostProcessParams 3 Offset 16
               OpMemberDecorate %type_PostProcessParams 4 Offset 20
               OpMemberDecorate %type_PostProcessParams 5 Offset 24
               OpMemberDecorate %type_PostProcessParams 6 Offset 28
               OpMemberDecorate %type_PostProcessParams 7 Offset 32
               OpMemberDecorate %type_PostProcessParams 8 Offset 36
               OpMemberDecorate %type_PostProcessParams 9 Offset 40
               OpMemberDecorate %type_PostProcessParams 10 Offset 44
               OpDecorate %type_PostProcessParams Block
        %int = OpTypeInt 32 1
     %int_10 = OpConstant %int 10
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %float = OpTypeFloat 32
%float_0_300000012 = OpConstant %float 0.300000012
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_8 = OpConstant %int 8
    %float_0 = OpConstant %float 0
  %float_0_5 = OpConstant %float 0.5
    %v3float = OpTypeVector %float 3
         %29 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
      %int_5 = OpConstant %int 5
       %uint = OpTypeInt 32 0
    %v2float = OpTypeVector %float 2
         %33 = OpConstantComposite %v2float %float_0_5 %float_0_5
    %float_1 = OpConstant %float 1
%float_0_800000012 = OpConstant %float 0.800000012
      %int_7 = OpConstant %int 7
      %int_9 = OpConstant %int 9
   %float_10 = OpConstant %float 10
%float_0_100000001 = OpConstant %float 0.100000001
      %int_6 = OpConstant %int 6
    %v4float = OpTypeVector %float 4
         %42 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
         %43 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
%float_0_0625 = OpConstant %float 0.0625
%float_0_125 = OpConstant %float 0.125
 %float_0_25 = OpConstant %float 0.25
      %int_0 = OpConstant %int 0
%float_0_200000003 = OpConstant %float 0.200000003
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
         %51 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_1
%float_n0_333333343 = OpConstant %float -0.333333343
%float_0_666666687 = OpConstant %float 0.666666687
   %float_n1 = OpConstant %float -1
%float_1_00000001en10 = OpConstant %float 1.00000001e-10
    %float_6 = OpConstant %float 6
%float_0_333333343 = OpConstant %float 0.333333343
    %float_3 = OpConstant %float 3
         %59 = OpConstantComposite %v3float %float_0 %float_0 %float_0
         %60 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%type_PostProcessParams = OpTypeStruct %v2float %float %float %float %float %float %float %float %float %float %int
%_ptr_Uniform_type_PostProcessParams = OpTypePointer Uniform %type_PostProcessParams
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v2float = OpTypePointer Input %v2float
     %uint_8 = OpConstant %uint 8
%_arr_v2float_uint_8 = OpTypeArray %v2float %uint_8
%_ptr_Input__arr_v2float_uint_8 = OpTypePointer Input %_arr_v2float_uint_8
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %70 = OpTypeFunction %void
%_ptr_Function_float = OpTypePointer Function %float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_float = OpTypePointer Uniform %float
     %uint_9 = OpConstant %uint 9
%_arr_float_uint_9 = OpTypeArray %float %uint_9
%_ptr_Function__arr_float_uint_9 = OpTypePointer Function %_arr_float_uint_9
%PostProcessParams = OpVariable %_ptr_Uniform_type_PostProcessParams Uniform
%SceneTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%BloomTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%DepthTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%NoiseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%PointSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input__arr_v2float_uint_8 Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
     %uint_0 = OpConstant %uint 0
         %78 = OpConstantComposite %_arr_float_uint_9 %float_0_0625 %float_0_125 %float_0_0625 %float_0_125 %float_0_25 %float_0_125 %float_0_0625 %float_0_125 %float_0_0625
         %79 = OpConstantComposite %v3float %float_1 %float_0_666666687 %float_0_333333343
         %80 = OpConstantComposite %v3float %float_3 %float_3 %float_3
    %float_5 = OpConstant %float 5
         %82 = OpConstantNull %v4float
       %main = OpFunction %void None %70
         %83 = OpLabel
         %84 = OpVariable %_ptr_Function__arr_float_uint_9 Function
         %85 = OpVariable %_ptr_Function__arr_float_uint_9 Function
         %86 = OpLoad %v2float %in_var_TEXCOORD0
         %87 = OpAccessChain %_ptr_Uniform_int %PostProcessParams %int_10
         %88 = OpLoad %int %87
         %89 = OpIEqual %bool %88 %int_1
               OpSelectionMerge %90 None
               OpBranchConditional %89 %91 %92
         %91 = OpLabel
               OpStore %85 %78
         %93 = OpLoad %type_2d_image %SceneTexture
         %94 = OpLoad %type_sampler %LinearSampler
         %95 = OpSampledImage %type_sampled_image %93 %94
         %96 = OpImageSampleImplicitLod %v4float %95 %86 None
         %97 = OpAccessChain %_ptr_Function_float %85 %int_4
         %98 = OpLoad %float %97
         %99 = OpVectorTimesScalar %v4float %96 %98
               OpBranch %100
        %100 = OpLabel
        %101 = OpPhi %v4float %99 %91 %102 %103
        %104 = OpPhi %int %int_0 %91 %105 %103
        %106 = OpSLessThan %bool %104 %int_8
               OpLoopMerge %107 %103 None
               OpBranchConditional %106 %103 %107
        %103 = OpLabel
        %108 = OpLoad %type_2d_image %SceneTexture
        %109 = OpLoad %type_sampler %LinearSampler
        %110 = OpAccessChain %_ptr_Input_v2float %in_var_TEXCOORD1 %104
        %111 = OpLoad %v2float %110
        %112 = OpSampledImage %type_sampled_image %108 %109
        %113 = OpImageSampleImplicitLod %v4float %112 %111 None
        %114 = OpAccessChain %_ptr_Function_float %85 %104
        %115 = OpLoad %float %114
        %116 = OpVectorTimesScalar %v4float %113 %115
        %102 = OpFAdd %v4float %101 %116
        %105 = OpIAdd %int %104 %int_1
               OpBranch %100
        %107 = OpLabel
               OpBranch %90
         %92 = OpLabel
        %117 = OpIEqual %bool %88 %int_2
               OpSelectionMerge %118 None
               OpBranchConditional %117 %119 %120
        %119 = OpLabel
        %121 = OpLoad %type_2d_image %SceneTexture
        %122 = OpLoad %type_sampler %LinearSampler
        %123 = OpSampledImage %type_sampled_image %121 %122
        %124 = OpImageSampleImplicitLod %v4float %123 %86 None
        %125 = OpLoad %type_2d_image %BloomTexture
        %126 = OpLoad %type_sampler %LinearSampler
        %127 = OpSampledImage %type_sampled_image %125 %126
        %128 = OpImageSampleImplicitLod %v4float %127 %86 None
        %129 = OpVectorTimesScalar %v4float %128 %float_0_300000012
        %130 = OpFAdd %v4float %124 %129
               OpBranch %118
        %120 = OpLabel
        %131 = OpIEqual %bool %88 %int_3
               OpSelectionMerge %132 None
               OpBranchConditional %131 %133 %134
        %133 = OpLabel
        %135 = OpLoad %type_2d_image %DepthTexture
        %136 = OpLoad %type_sampler %PointSampler
        %137 = OpSampledImage %type_sampled_image %135 %136
        %138 = OpImageSampleImplicitLod %v4float %137 %86 None
        %139 = OpCompositeExtract %float %138 0
        %140 = OpFSub %float %139 %float_0_5
        %141 = OpExtInst %float %1 FAbs %140
        %142 = OpFMul %float %141 %float_5
        %143 = OpExtInst %float %1 FClamp %142 %float_0 %float_1
        %144 = OpLoad %type_2d_image %SceneTexture
        %145 = OpLoad %type_sampler %LinearSampler
        %146 = OpSampledImage %type_sampled_image %144 %145
        %147 = OpImageSampleImplicitLod %v4float %146 %86 None
               OpStore %84 %78
        %148 = OpLoad %type_2d_image %SceneTexture
        %149 = OpLoad %type_sampler %LinearSampler
        %150 = OpSampledImage %type_sampled_image %148 %149
        %151 = OpImageSampleImplicitLod %v4float %150 %86 None
        %152 = OpAccessChain %_ptr_Function_float %84 %int_4
        %153 = OpLoad %float %152
        %154 = OpVectorTimesScalar %v4float %151 %153
               OpBranch %155
        %155 = OpLabel
        %156 = OpPhi %v4float %154 %133 %157 %158
        %159 = OpPhi %int %int_0 %133 %160 %158
        %161 = OpSLessThan %bool %159 %int_8
               OpLoopMerge %162 %158 None
               OpBranchConditional %161 %158 %162
        %158 = OpLabel
        %163 = OpLoad %type_2d_image %SceneTexture
        %164 = OpLoad %type_sampler %LinearSampler
        %165 = OpAccessChain %_ptr_Input_v2float %in_var_TEXCOORD1 %159
        %166 = OpLoad %v2float %165
        %167 = OpSampledImage %type_sampled_image %163 %164
        %168 = OpImageSampleImplicitLod %v4float %167 %166 None
        %169 = OpAccessChain %_ptr_Function_float %84 %159
        %170 = OpLoad %float %169
        %171 = OpVectorTimesScalar %v4float %168 %170
        %157 = OpFAdd %v4float %156 %171
        %160 = OpIAdd %int %159 %int_1
               OpBranch %155
        %162 = OpLabel
        %172 = OpCompositeConstruct %v4float %143 %143 %143 %143
        %173 = OpExtInst %v4float %1 FMix %147 %156 %172
               OpBranch %132
        %134 = OpLabel
        %174 = OpIEqual %bool %88 %int_4
               OpSelectionMerge %175 None
               OpBranchConditional %174 %176 %177
        %176 = OpLabel
               OpSelectionMerge %178 None
               OpSwitch %uint_0 %179
        %179 = OpLabel
        %180 = OpFSub %v2float %86 %33
        %181 = OpDot %float %180 %180
        %182 = OpFMul %float %float_0_200000003 %181
        %183 = OpFAdd %float %float_1 %182
        %184 = OpVectorTimesScalar %v2float %180 %183
        %185 = OpFAdd %v2float %33 %184
        %186 = OpCompositeExtract %float %185 0
        %187 = OpFOrdLessThan %bool %186 %float_0
        %188 = OpLogicalNot %bool %187
               OpSelectionMerge %189 None
               OpBranchConditional %188 %190 %189
        %190 = OpLabel
        %191 = OpFOrdGreaterThan %bool %186 %float_1
               OpBranch %189
        %189 = OpLabel
        %192 = OpPhi %bool %true %179 %191 %190
        %193 = OpLogicalNot %bool %192
               OpSelectionMerge %194 None
               OpBranchConditional %193 %195 %194
        %195 = OpLabel
        %196 = OpCompositeExtract %float %185 1
        %197 = OpFOrdLessThan %bool %196 %float_0
               OpBranch %194
        %194 = OpLabel
        %198 = OpPhi %bool %true %189 %197 %195
        %199 = OpLogicalNot %bool %198
               OpSelectionMerge %200 None
               OpBranchConditional %199 %201 %200
        %201 = OpLabel
        %202 = OpCompositeExtract %float %185 1
        %203 = OpFOrdGreaterThan %bool %202 %float_1
               OpBranch %200
        %200 = OpLabel
        %204 = OpPhi %bool %true %194 %203 %201
               OpSelectionMerge %205 None
               OpBranchConditional %204 %206 %205
        %206 = OpLabel
               OpBranch %178
        %205 = OpLabel
        %207 = OpLoad %type_2d_image %SceneTexture
        %208 = OpLoad %type_sampler %LinearSampler
        %209 = OpSampledImage %type_sampled_image %207 %208
        %210 = OpImageSampleImplicitLod %v4float %209 %185 None
               OpBranch %178
        %178 = OpLabel
        %211 = OpPhi %v4float %51 %206 %210 %205
               OpBranch %175
        %177 = OpLabel
        %212 = OpLoad %type_2d_image %SceneTexture
        %213 = OpLoad %type_sampler %LinearSampler
        %214 = OpSampledImage %type_sampled_image %212 %213
        %215 = OpImageSampleImplicitLod %v4float %214 %86 None
               OpBranch %175
        %175 = OpLabel
        %216 = OpPhi %v4float %211 %178 %215 %177
               OpBranch %132
        %132 = OpLabel
        %217 = OpPhi %v4float %173 %162 %216 %175
               OpBranch %118
        %118 = OpLabel
        %218 = OpPhi %v4float %130 %119 %217 %132
               OpBranch %90
         %90 = OpLabel
        %219 = OpPhi %v4float %101 %107 %218 %118
        %220 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_8
        %221 = OpLoad %float %220
        %222 = OpFOrdGreaterThan %bool %221 %float_0
               OpSelectionMerge %223 None
               OpBranchConditional %222 %224 %223
        %224 = OpLabel
        %225 = OpFSub %v2float %86 %33
        %226 = OpExtInst %float %1 Length %225
        %227 = OpVectorTimesScalar %v2float %225 %221
        %228 = OpVectorTimesScalar %v2float %227 %226
        %229 = OpVectorTimesScalar %v2float %228 %float_0_5
        %230 = OpLoad %type_2d_image %SceneTexture
        %231 = OpLoad %type_sampler %LinearSampler
        %232 = OpFAdd %v2float %86 %228
        %233 = OpSampledImage %type_sampled_image %230 %231
        %234 = OpImageSampleImplicitLod %v4float %233 %232 None
        %235 = OpCompositeExtract %float %234 0
        %236 = OpLoad %type_2d_image %SceneTexture
        %237 = OpLoad %type_sampler %LinearSampler
        %238 = OpFAdd %v2float %86 %229
        %239 = OpSampledImage %type_sampled_image %236 %237
        %240 = OpImageSampleImplicitLod %v4float %239 %238 None
        %241 = OpCompositeExtract %float %240 1
        %242 = OpLoad %type_2d_image %SceneTexture
        %243 = OpLoad %type_sampler %LinearSampler
        %244 = OpSampledImage %type_sampled_image %242 %243
        %245 = OpImageSampleImplicitLod %v4float %244 %86 None
        %246 = OpCompositeExtract %float %245 2
        %247 = OpLoad %type_2d_image %SceneTexture
        %248 = OpLoad %type_sampler %LinearSampler
        %249 = OpSampledImage %type_sampled_image %247 %248
        %250 = OpImageSampleImplicitLod %v4float %249 %86 None
        %251 = OpCompositeExtract %float %250 3
        %252 = OpCompositeConstruct %v4float %235 %241 %246 %251
               OpBranch %223
        %223 = OpLabel
        %253 = OpPhi %v4float %219 %90 %252 %224
        %254 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_3
        %255 = OpLoad %float %254
        %256 = OpVectorShuffle %v3float %253 %253 0 1 2
        %257 = OpVectorTimesScalar %v3float %256 %255
        %258 = OpVectorShuffle %v3float %257 %82 0 1 2
        %259 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_4
        %260 = OpLoad %float %259
        %261 = OpFMul %float %float_0_5 %260
        %262 = OpCompositeConstruct %v3float %261 %261 %261
        %263 = OpFSub %v3float %258 %262
        %264 = OpFAdd %v3float %263 %29
        %265 = OpCompositeExtract %float %264 2
        %266 = OpCompositeExtract %float %264 1
        %267 = OpCompositeConstruct %v4float %265 %266 %float_n1 %float_0_666666687
        %268 = OpCompositeConstruct %v4float %266 %265 %float_0 %float_n0_333333343
        %269 = OpExtInst %float %1 Step %265 %266
        %270 = OpCompositeConstruct %v4float %269 %269 %269 %269
        %271 = OpExtInst %v4float %1 FMix %267 %268 %270
        %272 = OpCompositeExtract %float %264 0
        %273 = OpCompositeExtract %float %271 0
        %274 = OpCompositeExtract %float %271 1
        %275 = OpCompositeExtract %float %271 3
        %276 = OpCompositeConstruct %v4float %273 %274 %275 %272
        %277 = OpCompositeExtract %float %271 2
        %278 = OpCompositeConstruct %v4float %272 %274 %277 %273
        %279 = OpExtInst %float %1 Step %273 %272
        %280 = OpCompositeConstruct %v4float %279 %279 %279 %279
        %281 = OpExtInst %v4float %1 FMix %276 %278 %280
        %282 = OpCompositeExtract %float %281 0
        %283 = OpCompositeExtract %float %281 3
        %284 = OpCompositeExtract %float %281 1
        %285 = OpExtInst %float %1 NMin %283 %284
        %286 = OpFSub %float %282 %285
        %287 = OpCompositeExtract %float %281 2
        %288 = OpFAdd %float %287 %283
        %289 = OpFMul %float %float_6 %286
        %290 = OpFDiv %float %284 %289
        %291 = OpFSub %float %288 %290
        %292 = OpFAdd %float %291 %float_1_00000001en10
        %293 = OpExtInst %float %1 FAbs %292
        %294 = OpFDiv %float %286 %282
        %295 = OpFAdd %float %294 %float_1_00000001en10
        %296 = OpCompositeConstruct %v3float %293 %295 %282
        %297 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_5
        %298 = OpLoad %float %297
        %299 = OpFMul %float %295 %298
        %300 = OpVectorShuffle %v3float %296 %296 0 0 0
        %301 = OpFAdd %v3float %300 %79
        %302 = OpExtInst %v3float %1 Fract %301
        %303 = OpVectorTimesScalar %v3float %302 %float_6
        %304 = OpFSub %v3float %303 %80
        %305 = OpExtInst %v3float %1 FAbs %304
        %306 = OpFSub %v3float %305 %60
        %307 = OpExtInst %v3float %1 FClamp %306 %59 %60
        %308 = OpCompositeConstruct %v3float %299 %299 %299
        %309 = OpExtInst %v3float %1 FMix %60 %307 %308
        %310 = OpVectorTimesScalar %v3float %309 %282
        %311 = OpFSub %v2float %86 %33
        %312 = OpExtInst %float %1 Length %311
        %313 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_7
        %314 = OpLoad %float %313
        %315 = OpFMul %float %312 %314
        %316 = OpExtInst %float %1 SmoothStep %float_0_300000012 %float_0_800000012 %315
        %317 = OpFSub %float %float_1 %316
        %318 = OpVectorShuffle %v3float %310 %82 0 1 2
        %319 = OpVectorTimesScalar %v3float %318 %317
        %320 = OpVectorShuffle %v4float %253 %319 4 5 6 3
        %321 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_9
        %322 = OpLoad %float %321
        %323 = OpFOrdGreaterThan %bool %322 %float_0
               OpSelectionMerge %324 None
               OpBranchConditional %323 %325 %324
        %325 = OpLabel
        %326 = OpVectorTimesScalar %v2float %86 %float_10
        %327 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_2
        %328 = OpLoad %float %327
        %329 = OpFMul %float %328 %float_0_100000001
        %330 = OpCompositeConstruct %v2float %329 %329
        %331 = OpFAdd %v2float %326 %330
        %332 = OpLoad %type_2d_image %NoiseTexture
        %333 = OpLoad %type_sampler %LinearSampler
        %334 = OpSampledImage %type_sampled_image %332 %333
        %335 = OpImageSampleImplicitLod %v4float %334 %331 None
        %336 = OpCompositeExtract %float %335 0
        %337 = OpFMul %float %float_0_5 %322
        %338 = OpFSub %float %336 %337
        %339 = OpCompositeConstruct %v3float %338 %338 %338
        %340 = OpVectorShuffle %v3float %319 %82 0 1 2
        %341 = OpFAdd %v3float %340 %339
        %342 = OpVectorShuffle %v4float %253 %341 4 5 6 3
               OpBranch %324
        %324 = OpLabel
        %343 = OpPhi %v4float %320 %223 %342 %325
        %344 = OpVectorShuffle %v3float %343 %343 0 1 2
        %345 = OpExtInst %v3float %1 FAbs %344
        %346 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_6
        %347 = OpLoad %float %346
        %348 = OpCompositeConstruct %v3float %347 %347 %347
        %349 = OpExtInst %v3float %1 Pow %345 %348
        %350 = OpVectorShuffle %v4float %343 %349 4 5 6 3
        %351 = OpExtInst %v4float %1 FClamp %350 %42 %43
               OpStore %out_var_SV_TARGET %351
               OpReturn
               OpFunctionEnd

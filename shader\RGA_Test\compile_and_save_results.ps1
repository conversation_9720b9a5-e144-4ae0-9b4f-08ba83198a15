# Compile all shaders and save results to CompileResults folder
Write-Host "Compiling all shaders and saving results..." -ForegroundColor Green

$hlslOutputDir = "result\CompileResults"

# Create output directory if it doesn't exist
if (-not (Test-Path $hlslOutputDir)) {
    New-Item -ItemType Directory -Path $hlslOutputDir -Force | Out-Null
}

# Get all shader files from current directory (TestShader)
$hlslFiles = Get-ChildItem -Path "result" -Filter "*.hlsl"

Write-Host "Found $($hlslFiles.Count) HLSL files" -ForegroundColor Cyan

# Compile HLSL files to SPIR-V
Write-Host "`nCompiling HLSL files to SPIR-V..." -ForegroundColor Yellow
foreach ($file in $hlslFiles) {
    Write-Host "Compiling: $($file.Name)" -ForegroundColor White

    # Determine shader profile and entry point based on filename
    $profile = ""
    $entryPoint = "main"

    if ($file.Name -like "*VS*" -or $file.Name -like "*Vert*") {
        $profile = "vs_6_0"
    } elseif ($file.Name -like "*PS*" -or $file.Name -like "*Pixel*") {
        $profile = "ps_6_0"
    } elseif ($file.Name -like "*CS*" -or $file.Name -like "*Compute*") {
        $profile = "cs_6_0"
    } else {
        $profile = "vs_6_0"  # Default fallback
    }

    $outputFile = Join-Path $hlslOutputDir "$($file.BaseName).spvasm"
    $logFile = Join-Path $hlslOutputDir "$($file.BaseName).log"

    try {
        # Compile to SPIR-V for Vulkan
        $result = & dxc -T $profile -E $entryPoint -spirv -Fc $outputFile $file.FullName

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  [OK] SUCCESS - SPIR-V saved to $outputFile" -ForegroundColor Green
            "COMPILATION SUCCESSFUL (SPIR-V)`n$result" | Out-File -FilePath $logFile -Encoding UTF8
        } else {
            # Try without entry point if failed
            Write-Host "  Retrying without entry point..." -ForegroundColor Yellow
            $result2 = & dxc -T $profile -spirv $file.FullName -Fo $outputFile

            if ($LASTEXITCODE -eq 0) {
                Write-Host "  [OK] SUCCESS (no entry point) - SPIR-V saved to $outputFile" -ForegroundColor Green
                "COMPILATION SUCCESSFUL (SPIR-V, no entry point specified)`n$result2" | Out-File -FilePath $logFile -Encoding UTF8
            } else {
                Write-Host "  [FAIL] FAILED" -ForegroundColor Red
                "COMPILATION FAILED`nFirst attempt with entry point '$entryPoint':`n$result`n`nSecond attempt without entry point:`n$result2" | Out-File -FilePath $logFile -Encoding UTF8
            }
        }
    } catch {
        Write-Host "  [ERROR] EXCEPTION: $($_.Exception.Message)" -ForegroundColor Red
        "COMPILATION EXCEPTION: $($_.Exception.Message)" | Out-File -FilePath $logFile -Encoding UTF8
    }
}

Write-Host "`n[DONE] Compilation complete! Results saved in CompileResults\" -ForegroundColor Green
Write-Host "  - HLSL SPIR-V files and logs: result\CompileResults\" -ForegroundColor Cyan


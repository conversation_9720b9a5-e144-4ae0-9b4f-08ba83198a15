_amdgpu_ps_main:
	s_getpc_b64 s[4:5]                                         // 000000000000: BE841F00
	s_mov_b32 s0, s1                                           // 000000000004: BE800301
	s_mov_b32 s1, s5                                           // 000000000008: BE810305
	s_load_dwordx4 s[4:7], s[0:1], null                        // 00000000000C: F4080100 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000014: BF8CC07F
	s_buffer_load_dword s0, s[4:7], 0xc                        // 000000000018: F4200002 FA00000C
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_cmp_lg_u32 s0, 11                                        // 000000000024: BF078B00
	s_cbranch_scc0 _L0                                         // 000000000028: BF84000B
	s_mov_b32 m0, s2                                           // 00000000002C: BEFC0302
	v_interp_p1_f32_e32 v3, v0, attr0.x                        // 000000000030: C80C0000
	v_interp_p1_f32_e32 v4, v0, attr0.y                        // 000000000034: C8100100
	v_interp_p1_f32_e32 v5, v0, attr0.z                        // 000000000038: C8140200
	v_interp_p1_f32_e32 v2, v0, attr0.w                        // 00000000003C: C8080300
	v_interp_p2_f32_e32 v3, v1, attr0.x                        // 000000000040: C80D0001
	v_interp_p2_f32_e32 v4, v1, attr0.y                        // 000000000044: C8110101
	v_interp_p2_f32_e32 v5, v1, attr0.z                        // 000000000048: C8150201
	v_interp_p2_f32_e32 v2, v1, attr0.w                        // 00000000004C: C8090301
	s_cbranch_execz _L0                                        // 000000000050: BF880001
	s_branch _L1                                               // 000000000054: BF82007B
_L0:
	s_mov_b32 m0, s2                                           // 000000000058: BEFC0302
	s_clause 0x1                                               // 00000000005C: BFA10001
	s_buffer_load_dwordx2 s[0:1], s[4:7], null                 // 000000000060: F4240002 FA000000
	s_buffer_load_dword s2, s[4:7], 0x8                        // 000000000068: F4200082 FA000008
	v_interp_p1_f32_e32 v2, v0, attr2.y                        // 000000000070: C8080900
	v_interp_p1_f32_e32 v3, v0, attr2.x                        // 000000000074: C80C0800
	v_interp_p1_f32_e32 v4, v0, attr2.z                        // 000000000078: C8100A00
	v_interp_p1_f32_e32 v15, v0, attr1.y                       // 00000000007C: C83C0500
	v_interp_p1_f32_e32 v16, v0, attr1.x                       // 000000000080: C8400400
	v_interp_p2_f32_e32 v2, v1, attr2.y                        // 000000000084: C8090901
	v_interp_p2_f32_e32 v3, v1, attr2.x                        // 000000000088: C80D0801
	v_interp_p2_f32_e32 v4, v1, attr2.z                        // 00000000008C: C8110A01
	v_interp_p2_f32_e32 v15, v1, attr1.y                       // 000000000090: C83D0501
	v_interp_p1_f32_e32 v0, v0, attr1.z                        // 000000000094: C8000600
	v_interp_p2_f32_e32 v16, v1, attr1.x                       // 000000000098: C8410401
	v_interp_p2_f32_e32 v0, v1, attr1.z                        // 00000000009C: C8010601
	s_waitcnt lgkmcnt(0)                                       // 0000000000A0: BF8CC07F
	v_sub_f32_e32 v5, s1, v2                                   // 0000000000A4: 080A0401
	v_sub_f32_e32 v2, 0x40400000, v2                           // 0000000000A8: 080404FF 40400000
	v_sub_f32_e32 v6, s0, v3                                   // 0000000000B0: 080C0600
	v_sub_f32_e32 v3, 2.0, v3                                  // 0000000000B4: 080606F4
	v_sub_f32_e32 v9, s2, v4                                   // 0000000000B8: 08120802
	v_mul_f32_e32 v7, v5, v5                                   // 0000000000BC: 100E0B05
	v_mul_f32_e32 v8, v2, v2                                   // 0000000000C0: 10100502
	v_sub_f32_e32 v4, 2.0, v4                                  // 0000000000C4: 080808F4
	s_mov_b32 s0, 0x3f6b851f                                   // 0000000000C8: BE8003FF 3F6B851F
	v_fmac_f32_e32 v7, v6, v6                                  // 0000000000D0: 560E0D06
	v_fmac_f32_e32 v8, v3, v3                                  // 0000000000D4: 56100703
	v_fmac_f32_e32 v7, v9, v9                                  // 0000000000D8: 560E1309
	v_fmac_f32_e32 v8, v4, v4                                  // 0000000000DC: 56100904
	v_rsq_f32_e32 v10, v7                                      // 0000000000E0: 7E145D07
	v_rsq_f32_e32 v11, v8                                      // 0000000000E4: 7E165D08
	v_cmp_neq_f32_e32 vcc_lo, 0, v7                            // 0000000000E8: 7C1A0E80
	v_cndmask_b32_e32 v7, 0, v10, vcc_lo                       // 0000000000EC: 020E1480
	v_cmp_neq_f32_e32 vcc_lo, 0, v8                            // 0000000000F0: 7C1A1080
	v_mul_f32_e32 v5, v7, v5                                   // 0000000000F4: 100A0B07
	v_cndmask_b32_e32 v10, 0, v11, vcc_lo                      // 0000000000F8: 02141680
	v_mul_f32_e32 v6, v7, v6                                   // 0000000000FC: 100C0D07
	v_mul_f32_e32 v7, v7, v9                                   // 000000000100: 100E1307
	v_fma_f32 v11, v10, v2, v5                                 // 000000000104: D54B000B 0416050A
	v_fma_f32 v9, v10, v3, v6                                  // 00000000010C: D54B0009 041A070A
	v_fma_f32 v13, v10, v4, v7                                 // 000000000114: D54B000D 041E090A
	v_mul_f32_e32 v2, v10, v2                                  // 00000000011C: 1004050A
	v_mul_f32_e32 v3, v10, v3                                  // 000000000120: 1006070A
	v_mul_f32_e32 v12, v11, v11                                // 000000000124: 1018170B
	v_mul_f32_e32 v1, v10, v4                                  // 000000000128: 1002090A
	v_fmac_f32_e32 v12, v9, v9                                 // 00000000012C: 56181309
	v_fmac_f32_e32 v12, v13, v13                               // 000000000130: 56181B0D
	v_rsq_f32_e32 v14, v12                                     // 000000000134: 7E1C5D0C
	v_cmp_neq_f32_e32 vcc_lo, 0, v12                           // 000000000138: 7C1A1880
	v_cndmask_b32_e32 v12, 0, v14, vcc_lo                      // 00000000013C: 02181C80
	v_mul_f32_e32 v14, v2, v15                                 // 000000000140: 101C1F02
	v_mul_f32_e32 v2, v12, v11                                 // 000000000144: 1004170C
	v_fmac_f32_e32 v14, v3, v16                                // 000000000148: 561C2103
	v_mul_f32_e32 v3, v12, v9                                  // 00000000014C: 1006130C
	v_mul_f32_e32 v4, v2, v5                                   // 000000000150: 10080B02
	v_mul_f32_e32 v9, v2, v15                                  // 000000000154: 10121F02
	v_mul_f32_e32 v5, v5, v15                                  // 000000000158: 100A1F05
	v_fmac_f32_e32 v14, v1, v0                                 // 00000000015C: 561C0101
	v_mul_f32_e32 v1, v12, v13                                 // 000000000160: 10021B0C
	v_fmac_f32_e32 v4, v3, v6                                  // 000000000164: 56080D03
	v_fmac_f32_e32 v9, v3, v16                                 // 000000000168: 56122103
	v_fmac_f32_e32 v5, v6, v16                                 // 00000000016C: 560A2106
	v_mov_b32_e32 v2, 1.0                                      // 000000000170: 7E0402F2
	v_max_f32_e64 v3, v14, v14 clamp                           // 000000000174: D5108003 00021D0E
	v_fma_f32 v4, v1, v7, v4 clamp                             // 00000000017C: D54B8004 04120F01
	v_fma_f32 v1, v1, v0, v9 clamp                             // 000000000184: D54B8001 04260101
	v_fma_f32 v0, v7, v0, v5 clamp                             // 00000000018C: D54B8000 04160107
	v_max_f32_e32 v5, 0x3a83126f, v3                           // 000000000194: 200A06FF 3A83126F
	v_sub_f32_e32 v4, 1.0, v4                                  // 00000000019C: 080808F2
	v_mul_f32_e32 v1, v1, v1                                   // 0000000001A0: 10020301
	v_fmaak_f32 v3, s0, v3, 0x3da3d70b                         // 0000000001A4: 5A060600 3DA3D70B
	v_max_f32_e32 v6, 0x3a83126f, v0                           // 0000000001AC: 200C00FF 3A83126F
	v_fmaak_f32 v0, s0, v0, 0x3da3d70b                         // 0000000001B4: 5A000000 3DA3D70B
	v_mul_f32_e32 v7, v4, v4                                   // 0000000001BC: 100E0904
	v_fmamk_f32 v1, v1, 0xbf797247, v2                         // 0000000001C0: 58020501 BF797247
	s_mov_b32 s0, 0x3f75c28f                                   // 0000000001C8: BE8003FF 3F75C28F
	v_mul_f32_e32 v5, v6, v5                                   // 0000000001D0: 100A0B06
	v_mul_f32_e32 v0, v0, v3                                   // 0000000001D4: 10000700
	v_mul_f32_e32 v3, v7, v7                                   // 0000000001D8: 10060F07
	v_mul_f32_e32 v1, v1, v1                                   // 0000000001DC: 10020301
	v_mul_f32_e32 v6, 4.0, v5                                  // 0000000001E0: 100C0AF6
	v_mul_f32_e32 v3, v4, v3                                   // 0000000001E4: 10060704
	v_mul_f32_e32 v0, v1, v0                                   // 0000000001E8: 10000101
	v_sqrt_f32_e32 v4, v8                                      // 0000000001EC: 7E086708
	v_fmaak_f32 v1, s0, v3, 0x3d23d70a                         // 0000000001F0: 5A020600 3D23D70A
	v_mul_f32_e32 v0, v0, v6                                   // 0000000001F8: 10000D00
	v_mul_f32_e32 v1, v5, v1                                   // 0000000001FC: 10020305
	v_rcp_f32_e32 v0, v0                                       // 000000000200: 7E005500
	v_mul_f32_e32 v1, 0x3c058244, v1                           // 000000000204: 100202FF 3C058244
	v_fmaak_f32 v0, v1, v0, 0x3e7a543e                         // 00000000020C: 5A000101 3E7A543E
	v_fma_f32 v1, v4, v4, 1.0                                  // 000000000214: D54B0001 03CA0904
	v_fmamk_f32 v0, v3, 0xbe7a543e, v0                         // 00000000021C: 58000103 BE7A543E
	v_rcp_f32_e32 v1, v1                                       // 000000000224: 7E025501
	v_max_f32_e32 v3, 0, v14                                   // 000000000228: 20061C80
	v_add_f32_e32 v0, v0, v0                                   // 00000000022C: 06000100
	v_mul_f32_e32 v0, v3, v0                                   // 000000000230: 10000103
	v_fmaak_f32 v3, v0, v1, 0x3ca3d70a                         // 000000000234: 5A060300 3CA3D70A
	v_mov_b32_e32 v4, v3                                       // 00000000023C: 7E080303
	v_mov_b32_e32 v5, v3                                       // 000000000240: 7E0A0303
_L1:
	exp mrt0 v3, v4, v5, v2 done vm                            // 000000000244: F800180F 02050403
	s_endpgm                                                   // 00000000024C: BF810000

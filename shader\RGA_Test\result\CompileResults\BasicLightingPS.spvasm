; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 80
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "DiffuseColor"
               OpMemberName %type_Material 1 "SpecularColor"
               OpMemberName %type_Material 2 "SpecularPower"
               OpMemberName %type_Material 3 "AmbientColor"
               OpName %Material "Material"
               OpName %type_2d_image "type.2d.image"
               OpName %DiffuseTexture "DiffuseTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %DiffuseTexture DescriptorSet 0
               OpDecorate %DiffuseTexture Binding 0
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 16
               OpMemberDecorate %type_Material 2 Offset 32
               OpMemberDecorate %type_Material 3 Offset 36
               OpDecorate %type_Material Block
        %int = OpTypeInt 32 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
    %v4float = OpTypeVector %float 4
    %v3float = OpTypeVector %float 3
%type_Material = OpTypeStruct %v4float %v4float %float %v3float
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %32 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_ptr_Uniform_float = OpTypePointer Uniform %float
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
%DiffuseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
       %main = OpFunction %void None %32
         %36 = OpLabel
         %37 = OpLoad %v3float %in_var_TEXCOORD1
         %38 = OpLoad %v2float %in_var_TEXCOORD2
         %39 = OpLoad %v3float %in_var_TEXCOORD4
         %40 = OpLoad %v3float %in_var_TEXCOORD5
         %41 = OpExtInst %v3float %1 Normalize %37
         %42 = OpExtInst %v3float %1 Normalize %40
         %43 = OpExtInst %v3float %1 Normalize %39
         %44 = OpLoad %type_2d_image %DiffuseTexture
         %45 = OpLoad %type_sampler %LinearSampler
         %46 = OpSampledImage %type_sampled_image %44 %45
         %47 = OpImageSampleImplicitLod %v4float %46 %38 None
         %48 = OpAccessChain %_ptr_Uniform_v3float %Material %int_3
         %49 = OpLoad %v3float %48
         %50 = OpVectorShuffle %v3float %47 %47 0 1 2
         %51 = OpFMul %v3float %49 %50
         %52 = OpDot %float %41 %42
         %53 = OpExtInst %float %1 NMax %float_0 %52
         %54 = OpAccessChain %_ptr_Uniform_v4float %Material %int_0
         %55 = OpLoad %v4float %54
         %56 = OpVectorShuffle %v3float %55 %55 0 1 2
         %57 = OpFMul %v3float %56 %50
         %58 = OpVectorTimesScalar %v3float %57 %53
         %59 = OpFNegate %v3float %42
         %60 = OpExtInst %v3float %1 Reflect %59 %41
         %61 = OpDot %float %60 %43
         %62 = OpExtInst %float %1 NMax %float_0 %61
         %63 = OpAccessChain %_ptr_Uniform_v4float %Material %int_1
         %64 = OpLoad %v4float %63
         %65 = OpVectorShuffle %v3float %64 %64 0 1 2
         %66 = OpAccessChain %_ptr_Uniform_float %Material %int_2
         %67 = OpLoad %float %66
         %68 = OpExtInst %float %1 Pow %62 %67
         %69 = OpVectorTimesScalar %v3float %65 %68
         %70 = OpFAdd %v3float %51 %58
         %71 = OpFAdd %v3float %70 %69
         %72 = OpCompositeExtract %float %47 3
         %73 = OpAccessChain %_ptr_Uniform_float %Material %int_0 %int_3
         %74 = OpLoad %float %73
         %75 = OpFMul %float %72 %74
         %76 = OpCompositeExtract %float %71 0
         %77 = OpCompositeExtract %float %71 1
         %78 = OpCompositeExtract %float %71 2
         %79 = OpCompositeConstruct %v4float %76 %77 %78 %75
               OpStore %out_var_SV_TARGET %79
               OpReturn
               OpFunctionEnd

; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 251
; Schema: 0
               OpCapability Shader
               OpCapability ImageQuery
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 4 4 4
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_3d_image "type.3d.image"
               OpName %VolumeTexture "VolumeTexture"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %VolumeTexture DescriptorSet 0
               OpDecorate %VolumeTexture Binding 0
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
        %int = OpTypeInt 32 1
       %uint = OpTypeInt 32 0
     %uint_1 = OpConstant %uint 1
     %v3uint = OpTypeVector %uint 3
         %12 = OpConstantComposite %v3uint %uint_1 %uint_1 %uint_1
      %float = OpTypeFloat 32
  %float_0_5 = OpConstant %float 0.5
    %v3float = OpTypeVector %float 3
         %16 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
  %float_100 = OpConstant %float 100
    %float_0 = OpConstant %float 0
%float_0_00999999978 = OpConstant %float 0.00999999978
      %int_3 = OpConstant %int 3
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_0199999996 = OpConstant %float 0.0199999996
%float_0_0500000007 = OpConstant %float 0.0500000007
%float_0_300000012 = OpConstant %float 0.300000012
%float_0_0399999991 = OpConstant %float 0.0399999991
%float_0_200000003 = OpConstant %float 0.200000003
    %float_1 = OpConstant %float 1
    %float_2 = OpConstant %float 2
    %float_3 = OpConstant %float 3
         %30 = OpConstantComposite %v3float %float_3 %float_3 %float_3
   %float_57 = OpConstant %float 57
  %float_113 = OpConstant %float 113
   %float_58 = OpConstant %float 58
  %float_114 = OpConstant %float 114
  %float_170 = OpConstant %float 170
  %float_171 = OpConstant %float 171
%float_43758_5469 = OpConstant %float 43758.5469
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
%type_3d_image = OpTypeImage %float 3D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_3d_image = OpTypePointer UniformConstant %type_3d_image
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %43 = OpTypeFunction %void
    %v4float = OpTypeVector %float 4
       %bool = OpTypeBool
     %v3bool = OpTypeVector %bool 3
%_ptr_Uniform_float = OpTypePointer Uniform %float
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%VolumeTexture = OpVariable %_ptr_UniformConstant_type_3d_image UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
     %uint_0 = OpConstant %uint 0
       %main = OpFunction %void None %43
         %49 = OpLabel
         %50 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %51 None
               OpSwitch %uint_0 %52
         %52 = OpLabel
         %53 = OpLoad %type_3d_image %VolumeTexture
         %54 = OpImageQuerySize %v3uint %53
         %55 = OpUGreaterThanEqual %v3bool %50 %54
         %56 = OpAny %bool %55
               OpSelectionMerge %57 None
               OpBranchConditional %56 %58 %57
         %58 = OpLabel
               OpBranch %51
         %57 = OpLabel
         %59 = OpConvertUToF %v3float %50
         %60 = OpISub %v3uint %54 %12
         %61 = OpConvertUToF %v3float %60
         %62 = OpFDiv %v3float %59 %61
         %63 = OpFSub %v3float %62 %16
         %64 = OpVectorTimesScalar %v3float %63 %float_100
         %65 = OpVectorTimesScalar %v3float %64 %float_0_00999999978
         %66 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_3
         %67 = OpLoad %float %66
         %68 = OpFMul %float %67 %float_0_100000001
         %69 = OpCompositeConstruct %v3float %68 %68 %68
         %70 = OpFAdd %v3float %65 %69
         %71 = OpExtInst %v3float %1 Floor %70
         %72 = OpExtInst %v3float %1 Fract %70
         %73 = OpFMul %v3float %72 %72
         %74 = OpVectorTimesScalar %v3float %72 %float_2
         %75 = OpFSub %v3float %30 %74
         %76 = OpFMul %v3float %73 %75
         %77 = OpCompositeExtract %float %71 0
         %78 = OpCompositeExtract %float %71 1
         %79 = OpFMul %float %78 %float_57
         %80 = OpFAdd %float %77 %79
         %81 = OpCompositeExtract %float %71 2
         %82 = OpFMul %float %float_113 %81
         %83 = OpFAdd %float %80 %82
         %84 = OpExtInst %float %1 Sin %83
         %85 = OpFMul %float %84 %float_43758_5469
         %86 = OpExtInst %float %1 Fract %85
         %87 = OpFAdd %float %83 %float_1
         %88 = OpExtInst %float %1 Sin %87
         %89 = OpFMul %float %88 %float_43758_5469
         %90 = OpExtInst %float %1 Fract %89
         %91 = OpCompositeExtract %float %76 0
         %92 = OpExtInst %float %1 FMix %86 %90 %91
         %93 = OpFAdd %float %83 %float_57
         %94 = OpExtInst %float %1 Sin %93
         %95 = OpFMul %float %94 %float_43758_5469
         %96 = OpExtInst %float %1 Fract %95
         %97 = OpFAdd %float %83 %float_58
         %98 = OpExtInst %float %1 Sin %97
         %99 = OpFMul %float %98 %float_43758_5469
        %100 = OpExtInst %float %1 Fract %99
        %101 = OpExtInst %float %1 FMix %96 %100 %91
        %102 = OpCompositeExtract %float %76 1
        %103 = OpExtInst %float %1 FMix %92 %101 %102
        %104 = OpFAdd %float %83 %float_113
        %105 = OpExtInst %float %1 Sin %104
        %106 = OpFMul %float %105 %float_43758_5469
        %107 = OpExtInst %float %1 Fract %106
        %108 = OpFAdd %float %83 %float_114
        %109 = OpExtInst %float %1 Sin %108
        %110 = OpFMul %float %109 %float_43758_5469
        %111 = OpExtInst %float %1 Fract %110
        %112 = OpExtInst %float %1 FMix %107 %111 %91
        %113 = OpFAdd %float %83 %float_170
        %114 = OpExtInst %float %1 Sin %113
        %115 = OpFMul %float %114 %float_43758_5469
        %116 = OpExtInst %float %1 Fract %115
        %117 = OpFAdd %float %83 %float_171
        %118 = OpExtInst %float %1 Sin %117
        %119 = OpFMul %float %118 %float_43758_5469
        %120 = OpExtInst %float %1 Fract %119
        %121 = OpExtInst %float %1 FMix %116 %120 %91
        %122 = OpExtInst %float %1 FMix %112 %121 %102
        %123 = OpCompositeExtract %float %76 2
        %124 = OpExtInst %float %1 FMix %103 %122 %123
        %125 = OpFMul %float %124 %float_0_5
        %126 = OpVectorTimesScalar %v3float %64 %float_0_0199999996
        %127 = OpFMul %float %67 %float_0_0500000007
        %128 = OpCompositeConstruct %v3float %127 %127 %127
        %129 = OpFAdd %v3float %126 %128
        %130 = OpExtInst %v3float %1 Floor %129
        %131 = OpExtInst %v3float %1 Fract %129
        %132 = OpFMul %v3float %131 %131
        %133 = OpVectorTimesScalar %v3float %131 %float_2
        %134 = OpFSub %v3float %30 %133
        %135 = OpFMul %v3float %132 %134
        %136 = OpCompositeExtract %float %130 0
        %137 = OpCompositeExtract %float %130 1
        %138 = OpFMul %float %137 %float_57
        %139 = OpFAdd %float %136 %138
        %140 = OpCompositeExtract %float %130 2
        %141 = OpFMul %float %float_113 %140
        %142 = OpFAdd %float %139 %141
        %143 = OpExtInst %float %1 Sin %142
        %144 = OpFMul %float %143 %float_43758_5469
        %145 = OpExtInst %float %1 Fract %144
        %146 = OpFAdd %float %142 %float_1
        %147 = OpExtInst %float %1 Sin %146
        %148 = OpFMul %float %147 %float_43758_5469
        %149 = OpExtInst %float %1 Fract %148
        %150 = OpCompositeExtract %float %135 0
        %151 = OpExtInst %float %1 FMix %145 %149 %150
        %152 = OpFAdd %float %142 %float_57
        %153 = OpExtInst %float %1 Sin %152
        %154 = OpFMul %float %153 %float_43758_5469
        %155 = OpExtInst %float %1 Fract %154
        %156 = OpFAdd %float %142 %float_58
        %157 = OpExtInst %float %1 Sin %156
        %158 = OpFMul %float %157 %float_43758_5469
        %159 = OpExtInst %float %1 Fract %158
        %160 = OpExtInst %float %1 FMix %155 %159 %150
        %161 = OpCompositeExtract %float %135 1
        %162 = OpExtInst %float %1 FMix %151 %160 %161
        %163 = OpFAdd %float %142 %float_113
        %164 = OpExtInst %float %1 Sin %163
        %165 = OpFMul %float %164 %float_43758_5469
        %166 = OpExtInst %float %1 Fract %165
        %167 = OpFAdd %float %142 %float_114
        %168 = OpExtInst %float %1 Sin %167
        %169 = OpFMul %float %168 %float_43758_5469
        %170 = OpExtInst %float %1 Fract %169
        %171 = OpExtInst %float %1 FMix %166 %170 %150
        %172 = OpFAdd %float %142 %float_170
        %173 = OpExtInst %float %1 Sin %172
        %174 = OpFMul %float %173 %float_43758_5469
        %175 = OpExtInst %float %1 Fract %174
        %176 = OpFAdd %float %142 %float_171
        %177 = OpExtInst %float %1 Sin %176
        %178 = OpFMul %float %177 %float_43758_5469
        %179 = OpExtInst %float %1 Fract %178
        %180 = OpExtInst %float %1 FMix %175 %179 %150
        %181 = OpExtInst %float %1 FMix %171 %180 %161
        %182 = OpCompositeExtract %float %135 2
        %183 = OpExtInst %float %1 FMix %162 %181 %182
        %184 = OpFMul %float %183 %float_0_300000012
        %185 = OpFAdd %float %125 %184
        %186 = OpVectorTimesScalar %v3float %64 %float_0_0399999991
        %187 = OpFMul %float %67 %float_0_0199999996
        %188 = OpCompositeConstruct %v3float %187 %187 %187
        %189 = OpFAdd %v3float %186 %188
        %190 = OpExtInst %v3float %1 Floor %189
        %191 = OpExtInst %v3float %1 Fract %189
        %192 = OpFMul %v3float %191 %191
        %193 = OpVectorTimesScalar %v3float %191 %float_2
        %194 = OpFSub %v3float %30 %193
        %195 = OpFMul %v3float %192 %194
        %196 = OpCompositeExtract %float %190 0
        %197 = OpCompositeExtract %float %190 1
        %198 = OpFMul %float %197 %float_57
        %199 = OpFAdd %float %196 %198
        %200 = OpCompositeExtract %float %190 2
        %201 = OpFMul %float %float_113 %200
        %202 = OpFAdd %float %199 %201
        %203 = OpExtInst %float %1 Sin %202
        %204 = OpFMul %float %203 %float_43758_5469
        %205 = OpExtInst %float %1 Fract %204
        %206 = OpFAdd %float %202 %float_1
        %207 = OpExtInst %float %1 Sin %206
        %208 = OpFMul %float %207 %float_43758_5469
        %209 = OpExtInst %float %1 Fract %208
        %210 = OpCompositeExtract %float %195 0
        %211 = OpExtInst %float %1 FMix %205 %209 %210
        %212 = OpFAdd %float %202 %float_57
        %213 = OpExtInst %float %1 Sin %212
        %214 = OpFMul %float %213 %float_43758_5469
        %215 = OpExtInst %float %1 Fract %214
        %216 = OpFAdd %float %202 %float_58
        %217 = OpExtInst %float %1 Sin %216
        %218 = OpFMul %float %217 %float_43758_5469
        %219 = OpExtInst %float %1 Fract %218
        %220 = OpExtInst %float %1 FMix %215 %219 %210
        %221 = OpCompositeExtract %float %195 1
        %222 = OpExtInst %float %1 FMix %211 %220 %221
        %223 = OpFAdd %float %202 %float_113
        %224 = OpExtInst %float %1 Sin %223
        %225 = OpFMul %float %224 %float_43758_5469
        %226 = OpExtInst %float %1 Fract %225
        %227 = OpFAdd %float %202 %float_114
        %228 = OpExtInst %float %1 Sin %227
        %229 = OpFMul %float %228 %float_43758_5469
        %230 = OpExtInst %float %1 Fract %229
        %231 = OpExtInst %float %1 FMix %226 %230 %210
        %232 = OpFAdd %float %202 %float_170
        %233 = OpExtInst %float %1 Sin %232
        %234 = OpFMul %float %233 %float_43758_5469
        %235 = OpExtInst %float %1 Fract %234
        %236 = OpFAdd %float %202 %float_171
        %237 = OpExtInst %float %1 Sin %236
        %238 = OpFMul %float %237 %float_43758_5469
        %239 = OpExtInst %float %1 Fract %238
        %240 = OpExtInst %float %1 FMix %235 %239 %210
        %241 = OpExtInst %float %1 FMix %231 %240 %221
        %242 = OpCompositeExtract %float %195 2
        %243 = OpExtInst %float %1 FMix %222 %241 %242
        %244 = OpFMul %float %243 %float_0_200000003
        %245 = OpFAdd %float %185 %244
        %246 = OpFSub %float %245 %float_0_300000012
        %247 = OpExtInst %float %1 FClamp %246 %float_0 %float_1
        %248 = OpFMul %float %247 %float_2
        %249 = OpCompositeConstruct %v4float %float_1 %float_1 %float_1 %248
        %250 = OpLoad %type_3d_image %VolumeTexture
               OpImageWrite %250 %50 %249 None
               OpBranch %51
         %51 = OpLabel
               OpReturn
               OpFunctionEnd

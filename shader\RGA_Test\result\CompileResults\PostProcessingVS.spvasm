; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 71
; Schema: 0
               OpCapability Shader
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_TEXCOORD0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1
               OpSource HLSL 600
               OpName %type_PostProcessParams "type.PostProcessParams"
               OpMemberName %type_PostProcessParams 0 "TexelSize"
               OpMemberName %type_PostProcessParams 1 "BlurRadius"
               OpMemberName %type_PostProcessParams 2 "Time"
               OpName %PostProcessParams "PostProcessParams"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_TEXCOORD0 Location 1
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %PostProcessParams DescriptorSet 0
               OpDecorate %PostProcessParams Binding 0
               OpMemberDecorate %type_PostProcessParams 0 Offset 0
               OpMemberDecorate %type_PostProcessParams 1 Offset 8
               OpMemberDecorate %type_PostProcessParams 2 Offset 12
               OpDecorate %type_PostProcessParams Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_1 = OpConstant %int 1
   %float_n1 = OpConstant %float -1
    %v2float = OpTypeVector %float 2
         %16 = OpConstantComposite %v2float %float_n1 %float_n1
    %float_0 = OpConstant %float 0
         %18 = OpConstantComposite %v2float %float_0 %float_n1
         %19 = OpConstantComposite %v2float %float_1 %float_n1
         %20 = OpConstantComposite %v2float %float_n1 %float_0
         %21 = OpConstantComposite %v2float %float_1 %float_0
         %22 = OpConstantComposite %v2float %float_n1 %float_1
         %23 = OpConstantComposite %v2float %float_0 %float_1
         %24 = OpConstantComposite %v2float %float_1 %float_1
      %int_8 = OpConstant %int 8
%type_PostProcessParams = OpTypeStruct %v2float %float %float
%_ptr_Uniform_type_PostProcessParams = OpTypePointer Uniform %type_PostProcessParams
    %v3float = OpTypeVector %float 3
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
    %v4float = OpTypeVector %float 4
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v2float = OpTypePointer Output %v2float
       %uint = OpTypeInt 32 0
     %uint_8 = OpConstant %uint 8
%_arr_v2float_uint_8 = OpTypeArray %v2float %uint_8
%_ptr_Output__arr_v2float_uint_8 = OpTypePointer Output %_arr_v2float_uint_8
       %void = OpTypeVoid
         %38 = OpTypeFunction %void
%_ptr_Function__arr_v2float_uint_8 = OpTypePointer Function %_arr_v2float_uint_8
%_ptr_Function_v2float = OpTypePointer Function %v2float
       %bool = OpTypeBool
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%PostProcessParams = OpVariable %_ptr_Uniform_type_PostProcessParams Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output__arr_v2float_uint_8 Output
         %44 = OpConstantComposite %_arr_v2float_uint_8 %16 %18 %19 %20 %21 %22 %23 %24
       %main = OpFunction %void None %38
         %45 = OpLabel
         %46 = OpVariable %_ptr_Function__arr_v2float_uint_8 Function
         %47 = OpVariable %_ptr_Function__arr_v2float_uint_8 Function
         %48 = OpLoad %v3float %in_var_POSITION
         %49 = OpLoad %v2float %in_var_TEXCOORD0
         %50 = OpCompositeExtract %float %48 0
         %51 = OpCompositeExtract %float %48 1
         %52 = OpCompositeExtract %float %48 2
         %53 = OpCompositeConstruct %v4float %50 %51 %52 %float_1
               OpStore %47 %44
               OpBranch %54
         %54 = OpLabel
         %55 = OpPhi %int %int_0 %45 %56 %57
         %58 = OpSLessThan %bool %55 %int_8
               OpLoopMerge %59 %57 None
               OpBranchConditional %58 %57 %59
         %57 = OpLabel
         %60 = OpAccessChain %_ptr_Function_v2float %47 %55
         %61 = OpLoad %v2float %60
         %62 = OpAccessChain %_ptr_Uniform_v2float %PostProcessParams %int_0
         %63 = OpLoad %v2float %62
         %64 = OpFMul %v2float %61 %63
         %65 = OpAccessChain %_ptr_Uniform_float %PostProcessParams %int_1
         %66 = OpLoad %float %65
         %67 = OpVectorTimesScalar %v2float %64 %66
         %68 = OpFAdd %v2float %49 %67
         %69 = OpAccessChain %_ptr_Function_v2float %46 %55
               OpStore %69 %68
         %56 = OpIAdd %int %55 %int_1
               OpBranch %54
         %59 = OpLabel
         %70 = OpLoad %_arr_v2float_uint_8 %46
               OpStore %gl_Position %53
               OpStore %out_var_TEXCOORD0 %49
               OpStore %out_var_TEXCOORD1 %70
               OpReturn
               OpFunctionEnd

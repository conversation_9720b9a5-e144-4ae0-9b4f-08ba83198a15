; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 279
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TEXCOORD0 %in_var_COLOR0 %out_var_POSITION %out_var_NORMAL %out_var_TANGENT %out_var_BITANGENT %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_COLOR0 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "LightDirection"
               OpMemberName %type_PerFrame 6 "TessellationLevel"
               OpMemberName %type_PerFrame 7 "HeightmapSize"
               OpMemberName %type_PerFrame 8 "HeightScale"
               OpMemberName %type_PerFrame 9 "DetailScale"
               OpMemberName %type_PerFrame 10 "LODDistance"
               OpName %PerFrame "PerFrame"
               OpName %type_PerObject "type.PerObject"
               OpMemberName %type_PerObject 0 "WorldMatrix"
               OpMemberName %type_PerObject 1 "NormalMatrix"
               OpMemberName %type_PerObject 2 "BoundingBoxMin"
               OpMemberName %type_PerObject 3 "BoundingBoxMax"
               OpMemberName %type_PerObject 4 "TextureTiling"
               OpMemberName %type_PerObject 5 "DisplacementStrength"
               OpMemberName %type_PerObject 6 "_padding"
               OpName %PerObject "PerObject"
               OpName %type_2d_image "type.2d.image"
               OpName %HeightmapTexture "HeightmapTexture"
               OpName %DetailHeightTexture "DetailHeightTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %out_var_POSITION "out.var.POSITION"
               OpName %out_var_NORMAL "out.var.NORMAL"
               OpName %out_var_TANGENT "out.var.TANGENT"
               OpName %out_var_BITANGENT "out.var.BITANGENT"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_COLOR0 "out.var.COLOR0"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TEXCOORD0 Location 3
               OpDecorate %in_var_COLOR0 Location 4
               OpDecorate %out_var_POSITION Location 0
               OpDecorate %out_var_NORMAL Location 1
               OpDecorate %out_var_TANGENT Location 2
               OpDecorate %out_var_BITANGENT Location 3
               OpDecorate %out_var_TEXCOORD0 Location 4
               OpDecorate %out_var_TEXCOORD1 Location 5
               OpDecorate %out_var_COLOR0 Location 6
               OpDecorate %out_var_TEXCOORD2 Location 7
               OpDecorate %out_var_TEXCOORD3 Location 8
               OpDecorate %out_var_TEXCOORD4 Location 9
               OpDecorate %out_var_TEXCOORD5 Location 10
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %PerObject DescriptorSet 0
               OpDecorate %PerObject Binding 1
               OpDecorate %HeightmapTexture DescriptorSet 0
               OpDecorate %HeightmapTexture Binding 0
               OpDecorate %DetailHeightTexture DescriptorSet 0
               OpDecorate %DetailHeightTexture Binding 2
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 220
               OpMemberDecorate %type_PerFrame 7 Offset 224
               OpMemberDecorate %type_PerFrame 8 Offset 232
               OpMemberDecorate %type_PerFrame 9 Offset 236
               OpMemberDecorate %type_PerFrame 10 Offset 240
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %type_PerObject 0 Offset 0
               OpMemberDecorate %type_PerObject 0 MatrixStride 16
               OpMemberDecorate %type_PerObject 0 RowMajor
               OpMemberDecorate %type_PerObject 1 Offset 64
               OpMemberDecorate %type_PerObject 1 MatrixStride 16
               OpMemberDecorate %type_PerObject 1 RowMajor
               OpMemberDecorate %type_PerObject 2 Offset 128
               OpMemberDecorate %type_PerObject 3 Offset 144
               OpMemberDecorate %type_PerObject 4 Offset 160
               OpMemberDecorate %type_PerObject 5 Offset 168
               OpMemberDecorate %type_PerObject 6 Offset 172
               OpDecorate %type_PerObject Block
        %int = OpTypeInt 32 1
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_7 = OpConstant %int 7
%float_0_800000012 = OpConstant %float 0.800000012
    %v3float = OpTypeVector %float 3
         %38 = OpConstantComposite %v3float %float_0_800000012 %float_0_800000012 %float_0_800000012
      %int_9 = OpConstant %int 9
      %int_5 = OpConstant %int 5
     %int_10 = OpConstant %int 10
      %int_8 = OpConstant %int 8
    %float_0 = OpConstant %float 0
%float_0_200000003 = OpConstant %float 0.200000003
%float_0_400000006 = OpConstant %float 0.400000006
%float_0_100000001 = OpConstant %float 0.100000001
         %47 = OpConstantComposite %v3float %float_0_200000003 %float_0_400000006 %float_0_100000001
%float_0_899999976 = OpConstant %float 0.899999976
         %49 = OpConstantComposite %v3float %float_0_800000012 %float_0_800000012 %float_0_899999976
  %float_0_5 = OpConstant %float 0.5
%float_0_300000012 = OpConstant %float 0.300000012
         %52 = OpConstantComposite %v3float %float_0_5 %float_0_400000006 %float_0_300000012
    %float_2 = OpConstant %float 2
      %int_6 = OpConstant %int 6
    %v2float = OpTypeVector %float 2
         %56 = OpConstantComposite %v2float %float_1 %float_1
   %float_64 = OpConstant %float 64
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %v3float %float %v2float %float %float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%type_PerObject = OpTypeStruct %mat4v4float %mat4v4float %v3float %v3float %v2float %float %float
%_ptr_Uniform_type_PerObject = OpTypePointer Uniform %type_PerObject
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %72 = OpTypeFunction %void
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_float = OpTypePointer Uniform %float
       %bool = OpTypeBool
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
  %PerObject = OpVariable %_ptr_Uniform_type_PerObject Uniform
%HeightmapTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%DetailHeightTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%out_var_POSITION = OpVariable %_ptr_Output_v3float Output
%out_var_NORMAL = OpVariable %_ptr_Output_v3float Output
%out_var_TANGENT = OpVariable %_ptr_Output_v3float Output
%out_var_BITANGENT = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v2float Output
%out_var_COLOR0 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_float Output
         %79 = OpConstantNull %v4float
       %main = OpFunction %void None %72
         %80 = OpLabel
         %81 = OpLoad %v3float %in_var_POSITION
         %82 = OpLoad %v3float %in_var_NORMAL
         %83 = OpLoad %v2float %in_var_TEXCOORD0
         %84 = OpLoad %v4float %in_var_COLOR0
         %85 = OpAccessChain %_ptr_Uniform_v2float %PerObject %int_4
         %86 = OpLoad %v2float %85
         %87 = OpFMul %v2float %83 %86
         %88 = OpLoad %type_2d_image %HeightmapTexture
         %89 = OpLoad %type_sampler %LinearSampler
         %90 = OpSampledImage %type_sampled_image %88 %89
         %91 = OpImageSampleExplicitLod %v4float %90 %87 Lod %float_0
         %92 = OpCompositeExtract %float %91 0
         %93 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_9
         %94 = OpLoad %float %93
         %95 = OpVectorTimesScalar %v2float %87 %94
         %96 = OpLoad %type_2d_image %DetailHeightTexture
         %97 = OpLoad %type_sampler %LinearSampler
         %98 = OpSampledImage %type_sampled_image %96 %97
         %99 = OpImageSampleExplicitLod %v4float %98 %95 Lod %float_0
        %100 = OpCompositeExtract %float %99 0
        %101 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_8
        %102 = OpLoad %float %101
        %103 = OpFMul %float %92 %102
        %104 = OpAccessChain %_ptr_Uniform_float %PerObject %int_5
        %105 = OpLoad %float %104
        %106 = OpFMul %float %float_0_5 %105
        %107 = OpFSub %float %100 %106
        %108 = OpFAdd %float %103 %107
        %109 = OpCompositeExtract %float %81 1
        %110 = OpFAdd %float %109 %108
        %111 = OpCompositeInsert %v3float %110 %81 1
        %112 = OpCompositeExtract %float %81 0
        %113 = OpCompositeExtract %float %81 2
        %114 = OpCompositeConstruct %v4float %112 %110 %113 %float_1
        %115 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_0
        %116 = OpLoad %mat4v4float %115
        %117 = OpMatrixTimesVector %v4float %116 %114
        %118 = OpVectorShuffle %v3float %117 %117 0 1 2
        %119 = OpAccessChain %_ptr_Uniform_v2float %PerFrame %int_7
        %120 = OpLoad %v2float %119
        %121 = OpFDiv %v2float %56 %120
        %122 = OpCompositeExtract %float %121 0
        %123 = OpFNegate %float %122
        %124 = OpCompositeConstruct %v2float %123 %float_0
        %125 = OpFAdd %v2float %87 %124
        %126 = OpLoad %type_2d_image %HeightmapTexture
        %127 = OpLoad %type_sampler %LinearSampler
        %128 = OpSampledImage %type_sampled_image %126 %127
        %129 = OpImageSampleExplicitLod %v4float %128 %125 Lod %float_0
        %130 = OpCompositeExtract %float %129 0
        %131 = OpVectorTimesScalar %v2float %125 %94
        %132 = OpLoad %type_2d_image %DetailHeightTexture
        %133 = OpLoad %type_sampler %LinearSampler
        %134 = OpSampledImage %type_sampled_image %132 %133
        %135 = OpImageSampleExplicitLod %v4float %134 %131 Lod %float_0
        %136 = OpCompositeExtract %float %135 0
        %137 = OpFMul %float %130 %102
        %138 = OpFSub %float %136 %106
        %139 = OpFAdd %float %137 %138
        %140 = OpCompositeConstruct %v2float %122 %float_0
        %141 = OpFAdd %v2float %87 %140
        %142 = OpLoad %type_2d_image %HeightmapTexture
        %143 = OpLoad %type_sampler %LinearSampler
        %144 = OpSampledImage %type_sampled_image %142 %143
        %145 = OpImageSampleExplicitLod %v4float %144 %141 Lod %float_0
        %146 = OpCompositeExtract %float %145 0
        %147 = OpVectorTimesScalar %v2float %141 %94
        %148 = OpLoad %type_2d_image %DetailHeightTexture
        %149 = OpLoad %type_sampler %LinearSampler
        %150 = OpSampledImage %type_sampled_image %148 %149
        %151 = OpImageSampleExplicitLod %v4float %150 %147 Lod %float_0
        %152 = OpCompositeExtract %float %151 0
        %153 = OpFMul %float %146 %102
        %154 = OpFSub %float %152 %106
        %155 = OpFAdd %float %153 %154
        %156 = OpCompositeExtract %float %121 1
        %157 = OpFNegate %float %156
        %158 = OpCompositeConstruct %v2float %float_0 %157
        %159 = OpFAdd %v2float %87 %158
        %160 = OpLoad %type_2d_image %HeightmapTexture
        %161 = OpLoad %type_sampler %LinearSampler
        %162 = OpSampledImage %type_sampled_image %160 %161
        %163 = OpImageSampleExplicitLod %v4float %162 %159 Lod %float_0
        %164 = OpCompositeExtract %float %163 0
        %165 = OpVectorTimesScalar %v2float %159 %94
        %166 = OpLoad %type_2d_image %DetailHeightTexture
        %167 = OpLoad %type_sampler %LinearSampler
        %168 = OpSampledImage %type_sampled_image %166 %167
        %169 = OpImageSampleExplicitLod %v4float %168 %165 Lod %float_0
        %170 = OpCompositeExtract %float %169 0
        %171 = OpFMul %float %164 %102
        %172 = OpFSub %float %170 %106
        %173 = OpFAdd %float %171 %172
        %174 = OpCompositeConstruct %v2float %float_0 %156
        %175 = OpFAdd %v2float %87 %174
        %176 = OpLoad %type_2d_image %HeightmapTexture
        %177 = OpLoad %type_sampler %LinearSampler
        %178 = OpSampledImage %type_sampled_image %176 %177
        %179 = OpImageSampleExplicitLod %v4float %178 %175 Lod %float_0
        %180 = OpCompositeExtract %float %179 0
        %181 = OpVectorTimesScalar %v2float %175 %94
        %182 = OpLoad %type_2d_image %DetailHeightTexture
        %183 = OpLoad %type_sampler %LinearSampler
        %184 = OpSampledImage %type_sampled_image %182 %183
        %185 = OpImageSampleExplicitLod %v4float %184 %181 Lod %float_0
        %186 = OpCompositeExtract %float %185 0
        %187 = OpFMul %float %180 %102
        %188 = OpFSub %float %186 %106
        %189 = OpFAdd %float %187 %188
        %190 = OpFMul %float %float_2 %122
        %191 = OpFDiv %float %155 %190
        %192 = OpFSub %float %139 %191
        %193 = OpFMul %float %float_2 %156
        %194 = OpFDiv %float %189 %193
        %195 = OpFSub %float %173 %194
        %196 = OpCompositeConstruct %v3float %192 %float_1 %195
        %197 = OpExtInst %v3float %1 Normalize %196
        %198 = OpExtInst %v3float %1 FMix %82 %197 %38
        %199 = OpExtInst %v3float %1 Normalize %198
        %200 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_1
        %201 = OpLoad %mat4v4float %200
        %202 = OpCompositeExtract %v4float %201 0
        %203 = OpVectorShuffle %v3float %202 %202 0 1 2
        %204 = OpCompositeExtract %v4float %201 1
        %205 = OpVectorShuffle %v3float %204 %204 0 1 2
        %206 = OpCompositeExtract %v4float %201 2
        %207 = OpVectorShuffle %v3float %206 %206 0 1 2
        %208 = OpCompositeConstruct %mat3v3float %203 %205 %207
        %209 = OpMatrixTimesVector %v3float %208 %199
        %210 = OpExtInst %v3float %1 Normalize %209
        %211 = OpLoad %type_2d_image %HeightmapTexture
        %212 = OpLoad %type_sampler %LinearSampler
        %213 = OpSampledImage %type_sampled_image %211 %212
        %214 = OpImageSampleExplicitLod %v4float %213 %87 Lod %float_0
        %215 = OpCompositeExtract %float %214 0
        %216 = OpLoad %type_2d_image %DetailHeightTexture
        %217 = OpLoad %type_sampler %LinearSampler
        %218 = OpSampledImage %type_sampled_image %216 %217
        %219 = OpImageSampleExplicitLod %v4float %218 %95 Lod %float_0
        %220 = OpCompositeExtract %float %219 0
        %221 = OpFMul %float %215 %102
        %222 = OpFSub %float %220 %106
        %223 = OpFAdd %float %221 %222
        %224 = OpLoad %type_2d_image %HeightmapTexture
        %225 = OpLoad %type_sampler %LinearSampler
        %226 = OpSampledImage %type_sampled_image %224 %225
        %227 = OpImageSampleExplicitLod %v4float %226 %141 Lod %float_0
        %228 = OpCompositeExtract %float %227 0
        %229 = OpLoad %type_2d_image %DetailHeightTexture
        %230 = OpLoad %type_sampler %LinearSampler
        %231 = OpSampledImage %type_sampled_image %229 %230
        %232 = OpImageSampleExplicitLod %v4float %231 %147 Lod %float_0
        %233 = OpCompositeExtract %float %232 0
        %234 = OpFMul %float %228 %102
        %235 = OpFSub %float %233 %106
        %236 = OpFAdd %float %234 %235
        %237 = OpFSub %float %236 %223
        %238 = OpCompositeConstruct %v3float %122 %237 %float_0
        %239 = OpExtInst %v3float %1 Normalize %238
        %240 = OpDot %float %239 %199
        %241 = OpVectorTimesScalar %v3float %199 %240
        %242 = OpFSub %v3float %239 %241
        %243 = OpExtInst %v3float %1 Normalize %242
        %244 = OpMatrixTimesVector %v3float %208 %243
        %245 = OpExtInst %v3float %1 Normalize %244
        %246 = OpExtInst %v3float %1 Cross %210 %245
        %247 = OpExtInst %v3float %1 Normalize %246
        %248 = OpCompositeExtract %float %210 1
        %249 = OpFSub %float %float_1 %248
        %250 = OpFDiv %float %108 %102
        %251 = OpExtInst %float %1 FClamp %250 %float_0 %float_1
        %252 = OpCompositeConstruct %v3float %251 %251 %251
        %253 = OpExtInst %v3float %1 FMix %47 %49 %252
        %254 = OpVectorShuffle %v4float %84 %253 4 5 6 3
        %255 = OpFOrdGreaterThan %bool %249 %float_0_5
               OpSelectionMerge %256 None
               OpBranchConditional %255 %257 %256
        %257 = OpLabel
        %258 = OpVectorShuffle %v3float %253 %79 0 1 2
        %259 = OpFNegate %float %248
        %260 = OpCompositeConstruct %v3float %259 %259 %259
        %261 = OpExtInst %v3float %1 FMix %258 %52 %260
        %262 = OpVectorShuffle %v4float %84 %261 4 5 6 3
               OpBranch %256
        %256 = OpLabel
        %263 = OpPhi %v4float %254 %80 %262 %257
        %264 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_3
        %265 = OpLoad %v3float %264
        %266 = OpFSub %v3float %265 %118
        %267 = OpExtInst %float %1 Length %266
        %268 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_6
        %269 = OpLoad %float %268
        %270 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_10
        %271 = OpLoad %float %270
        %272 = OpFMul %float %269 %271
        %273 = OpExtInst %float %1 NMax %267 %float_1
        %274 = OpFDiv %float %272 %273
        %275 = OpExtInst %float %1 FClamp %274 %float_1 %float_64
        %276 = OpFMul %float %249 %float_2
        %277 = OpFAdd %float %float_1 %276
        %278 = OpFMul %float %275 %277
               OpStore %out_var_POSITION %111
               OpStore %out_var_NORMAL %210
               OpStore %out_var_TANGENT %245
               OpStore %out_var_BITANGENT %247
               OpStore %out_var_TEXCOORD0 %87
               OpStore %out_var_TEXCOORD1 %95
               OpStore %out_var_COLOR0 %263
               OpStore %out_var_TEXCOORD2 %118
               OpStore %out_var_TEXCOORD3 %278
               OpStore %out_var_TEXCOORD4 %108
               OpStore %out_var_TEXCOORD5 %249
               OpReturn
               OpFunctionEnd

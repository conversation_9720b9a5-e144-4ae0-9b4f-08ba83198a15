_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v6, s3, 5, v1                                // 000000000040: D76F0006 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v6                            // 000000000048: 7D880C01
	s_and_saveexec_b32 s34, vcc_lo                             // 00000000004C: BEA23C6A
	s_cbranch_execz _L1                                        // 000000000050: BF88009C
	s_getpc_b64 s[6:7]                                         // 000000000054: BE861F00
	v_add_nc_u32_e32 v1, s0, v5                                // 000000000058: 4A020A00
	s_mov_b32 s11, s7                                          // 00000000005C: BE8B0307
	s_mov_b32 s5, s7                                           // 000000000060: BE850307
	s_load_dwordx16 s[12:27], s[10:11], null                   // 000000000064: F4100305 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xyz v[11:13], v1, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80030B01
	tbuffer_load_format_xyz v[14:16], v1, s[16:19], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000078: EA522000 80040E01
	tbuffer_load_format_xyz v[17:19], v1, s[20:23], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000080: EA522000 80051101
	tbuffer_load_format_xy v[20:21], v1, s[24:27], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000088: EA012000 80061401
	s_load_dwordx4 s[0:3], s[10:11], 0x40                      // 000000000090: F4080005 FA000040
	s_load_dwordx4 s[52:55], s[4:5], null                      // 000000000098: F4080D02 FA000000
	s_waitcnt lgkmcnt(0)                                       // 0000000000A0: BF8CC07F
	tbuffer_load_format_xyzw v[1:4], v1, s[0:3], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 0000000000A4: EA6B2000 80000101
	s_clause 0x8                                               // 0000000000AC: BFA10008
	s_buffer_load_dwordx8 s[0:7], s[52:55], null               // 0000000000B0: F42C001A FA000000
	s_buffer_load_dwordx8 s[8:15], s[52:55], 0x20              // 0000000000B8: F42C021A FA000020
	s_buffer_load_dwordx2 s[60:61], s[52:55], 0x158            // 0000000000C0: F4240F1A FA000158
	s_buffer_load_dwordx8 s[44:51], s[52:55], 0x40             // 0000000000C8: F42C0B1A FA000040
	s_buffer_load_dwordx8 s[36:43], s[52:55], 0x60             // 0000000000D0: F42C091A FA000060
	s_buffer_load_dwordx8 s[24:31], s[52:55], 0x80             // 0000000000D8: F42C061A FA000080
	s_buffer_load_dwordx8 s[16:23], s[52:55], 0xa0             // 0000000000E0: F42C041A FA0000A0
	s_buffer_load_dwordx4 s[56:59], s[52:55], 0x140            // 0000000000E8: F4280E1A FA000140
	s_buffer_load_dwordx4 s[52:55], s[52:55], 0x100            // 0000000000F0: F4280D1A FA000100
	s_waitcnt vmcnt(4) lgkmcnt(0)                              // 0000000000F8: BF8C0074
	v_fma_f32 v7, s0, v11, s3                                  // 0000000000FC: D54B0007 000E1600
	s_waitcnt vmcnt(3)                                         // 000000000104: BF8C3F73
	v_mul_f32_e32 v23, s4, v14                                 // 000000000108: 102E1C04
	s_waitcnt vmcnt(2)                                         // 00000000010C: BF8C3F72
	v_mul_f32_e32 v26, s4, v17                                 // 000000000110: 10342204
	v_fma_f32 v8, s4, v11, s7                                  // 000000000114: D54B0008 001E1604
	v_fma_f32 v9, s8, v11, s11                                 // 00000000011C: D54B0009 002E1608
	v_fma_f32 v5, s12, v11, s15                                // 000000000124: D54B0005 003E160C
	v_mul_f32_e32 v22, s0, v14                                 // 00000000012C: 102C1C00
	v_mul_f32_e32 v24, s8, v14                                 // 000000000130: 10301C08
	v_mul_f32_e32 v25, s0, v17                                 // 000000000134: 10322200
	v_fmac_f32_e32 v7, s1, v12                                 // 000000000138: 560E1801
	v_fmac_f32_e32 v23, s5, v15                                // 00000000013C: 562E1E05
	v_fmac_f32_e32 v26, s5, v18                                // 000000000140: 56342405
	v_mul_f32_e32 v27, s8, v17                                 // 000000000144: 10362208
	v_fmac_f32_e32 v8, s5, v12                                 // 000000000148: 56101805
	v_fmac_f32_e32 v9, s9, v12                                 // 00000000014C: 56121809
	v_fmac_f32_e32 v5, s13, v12                                // 000000000150: 560A180D
	v_fmac_f32_e32 v22, s1, v15                                // 000000000154: 562C1E01
	v_fmac_f32_e32 v24, s9, v15                                // 000000000158: 56301E09
	v_fmac_f32_e32 v25, s1, v18                                // 00000000015C: 56322401
	v_fmac_f32_e32 v7, s2, v13                                 // 000000000160: 560E1A02
	v_fmac_f32_e32 v23, s6, v16                                // 000000000164: 562E2006
	v_fmac_f32_e32 v26, s6, v19                                // 000000000168: 56342606
	v_fmac_f32_e32 v27, s9, v18                                // 00000000016C: 56362409
	v_fmac_f32_e32 v8, s6, v13                                 // 000000000170: 56101A06
	v_fmac_f32_e32 v9, s10, v13                                // 000000000174: 56121A0A
	v_fmac_f32_e32 v5, s14, v13                                // 000000000178: 560A1A0E
	v_fmac_f32_e32 v22, s2, v16                                // 00000000017C: 562C2002
	v_fmac_f32_e32 v24, s10, v16                               // 000000000180: 5630200A
	v_fmac_f32_e32 v25, s2, v19                                // 000000000184: 56322602
	v_mul_f32_e32 v15, s44, v7                                 // 000000000188: 101E0E2C
	v_mul_f32_e32 v16, s48, v7                                 // 00000000018C: 10200E30
	v_mul_f32_e32 v28, s36, v7                                 // 000000000190: 10380E24
	v_mul_f32_e32 v30, s40, v7                                 // 000000000194: 103C0E28
	v_mul_f32_e32 v12, v23, v23                                // 000000000198: 10182F17
	v_mul_f32_e32 v13, v26, v26                                // 00000000019C: 101A351A
	v_fmac_f32_e32 v27, s10, v19                               // 0000000001A0: 5636260A
	v_sub_f32_e32 v31, s57, v8                                 // 0000000001A4: 083E1039
	v_sub_f32_e32 v34, s53, v8                                 // 0000000001A8: 08441035
	v_fmac_f32_e32 v15, s45, v8                                // 0000000001AC: 561E102D
	v_fmac_f32_e32 v16, s49, v8                                // 0000000001B0: 56201031
	v_fmac_f32_e32 v28, s37, v8                                // 0000000001B4: 56381025
	v_fmac_f32_e32 v30, s41, v8                                // 0000000001B8: 563C1029
	v_fmac_f32_e32 v12, v22, v22                               // 0000000001BC: 56182D16
	v_fmac_f32_e32 v13, v25, v25                               // 0000000001C0: 561A3319
	v_sub_f32_e32 v29, s56, v7                                 // 0000000001C4: 083A0E38
	v_sub_f32_e32 v33, s52, v7                                 // 0000000001C8: 08420E34
	v_mul_f32_e32 v17, v31, v31                                // 0000000001CC: 10223F1F
	v_mul_f32_e32 v18, v34, v34                                // 0000000001D0: 10244522
	v_fmac_f32_e32 v15, s46, v9                                // 0000000001D4: 561E122E
	v_fmac_f32_e32 v16, s50, v9                                // 0000000001D8: 56201232
	v_fmac_f32_e32 v28, s38, v9                                // 0000000001DC: 56381226
	v_fmac_f32_e32 v30, s42, v9                                // 0000000001E0: 563C122A
	v_fmac_f32_e32 v12, v24, v24                               // 0000000001E4: 56183118
	v_fmac_f32_e32 v13, v27, v27                               // 0000000001E8: 561A371B
	s_waitcnt vmcnt(1)                                         // 0000000001EC: BF8C3F71
	v_mul_f32_e32 v11, s61, v21                                // 0000000001F0: 10162A3D
	v_sub_f32_e32 v32, s58, v9                                 // 0000000001F4: 0840123A
	v_sub_f32_e32 v35, s54, v9                                 // 0000000001F8: 08461236
	v_fmac_f32_e32 v17, v29, v29                               // 0000000001FC: 56223B1D
	v_fmac_f32_e32 v18, v33, v33                               // 000000000200: 56244321
	v_fmac_f32_e32 v15, s47, v5                                // 000000000204: 561E0A2F
	v_fmac_f32_e32 v16, s51, v5                                // 000000000208: 56200A33
	v_fmac_f32_e32 v28, s39, v5                                // 00000000020C: 56380A27
	v_fmac_f32_e32 v30, s43, v5                                // 000000000210: 563C0A2B
	v_rsq_f32_e32 v5, v12                                      // 000000000214: 7E0A5D0C
	v_rsq_f32_e32 v21, v13                                     // 000000000218: 7E2A5D0D
	v_fmac_f32_e32 v17, v32, v32                               // 00000000021C: 56224120
	v_fmac_f32_e32 v18, v35, v35                               // 000000000220: 56244723
	v_mul_f32_e32 v12, s24, v15                                // 000000000224: 10181E18
	v_mul_f32_e32 v13, s28, v15                                // 000000000228: 101A1E1C
	v_mul_f32_e32 v14, s16, v15                                // 00000000022C: 101C1E10
	v_mul_f32_e32 v15, s20, v15                                // 000000000230: 101E1E14
	v_mul_f32_e32 v10, s60, v20                                // 000000000234: 1014283C
	v_rsq_f32_e32 v36, v17                                     // 000000000238: 7E485D11
	v_rsq_f32_e32 v37, v18                                     // 00000000023C: 7E4A5D12
	v_fmac_f32_e32 v12, s25, v16                               // 000000000240: 56182019
	v_fmac_f32_e32 v13, s29, v16                               // 000000000244: 561A201D
	v_fmac_f32_e32 v14, s17, v16                               // 000000000248: 561C2011
	v_fmac_f32_e32 v15, s21, v16                               // 00000000024C: 561E2015
	v_mul_legacy_f32_e32 v16, v22, v5                          // 000000000250: 0E200B16
	v_mul_legacy_f32_e32 v17, v23, v5                          // 000000000254: 0E220B17
	v_mul_legacy_f32_e32 v18, v24, v5                          // 000000000258: 0E240B18
	v_mul_legacy_f32_e32 v19, v25, v21                         // 00000000025C: 0E262B19
	v_mul_legacy_f32_e32 v20, v26, v21                         // 000000000260: 0E282B1A
	v_mul_legacy_f32_e32 v21, v27, v21                         // 000000000264: 0E2A2B1B
	v_fmac_f32_e32 v12, s26, v28                               // 000000000268: 5618381A
	v_fmac_f32_e32 v13, s30, v28                               // 00000000026C: 561A381E
	v_fmac_f32_e32 v14, s18, v28                               // 000000000270: 561C3812
	v_fmac_f32_e32 v15, s22, v28                               // 000000000274: 561E3816
	v_mul_f32_e32 v5, v18, v20                                 // 000000000278: 100A2912
	v_mul_f32_e32 v26, v16, v21                                // 00000000027C: 10342B10
	v_mul_f32_e32 v28, v17, v19                                // 000000000280: 10382711
	v_mul_legacy_f32_e32 v22, v29, v36                         // 000000000284: 0E2C491D
	v_mul_legacy_f32_e32 v23, v31, v36                         // 000000000288: 0E2E491F
	v_mul_legacy_f32_e32 v24, v32, v36                         // 00000000028C: 0E304920
	v_mul_legacy_f32_e32 v27, v33, v37                         // 000000000290: 0E364B21
	v_mul_legacy_f32_e32 v29, v34, v37                         // 000000000294: 0E3A4B22
	v_fmac_f32_e32 v12, s27, v30                               // 000000000298: 56183C1B
	v_fmac_f32_e32 v13, s31, v30                               // 00000000029C: 561A3C1F
	v_fmac_f32_e32 v14, s19, v30                               // 0000000002A0: 561C3C13
	v_fmac_f32_e32 v15, s23, v30                               // 0000000002A4: 561E3C17
	v_fma_f32 v25, v17, v21, -v5                               // 0000000002A8: D54B0019 84162B11
	v_fma_f32 v26, v18, v19, -v26                              // 0000000002B0: D54B001A 846A2712
	v_fma_f32 v28, v16, v20, -v28                              // 0000000002B8: D54B001C 84722910
	v_mul_legacy_f32_e32 v30, v35, v37                         // 0000000002C0: 0E3C4B23
_L1:
	s_or_b32 exec_lo, exec_lo, s34                             // 0000000002C4: 887E227E
	s_mov_b32 s1, exec_lo                                      // 0000000002C8: BE81037E
	v_cmpx_gt_u32_e64 s33, v6                                  // 0000000002CC: D4D4007E 00020C21
	s_cbranch_execz _L2                                        // 0000000002D4: BF880002
	exp prim v0, off, off, off done                            // 0000000002D8: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000002E0: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000002E4: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000002E8: BE803C6A
	s_cbranch_execz _L3                                        // 0000000002EC: BF880013
	exp pos0 v12, v13, v14, v15 done                           // 0000000002F0: F80008CF 0F0E0D0C
	s_waitcnt vmcnt(0)                                         // 0000000002F8: BF8C3F70
	exp param5 v1, v2, v3, v4                                  // 0000000002FC: F800025F 04030201
	exp param3 v25, v26, v28, off                              // 000000000304: F8000237 001C1A19
	exp param1 v16, v17, v18, off                              // 00000000030C: F8000217 00121110
	exp param6 v22, v23, v24, off                              // 000000000314: F8000267 00181716
	exp param4 v10, v11, off, off                              // 00000000031C: F8000243 00000B0A
	exp param2 v19, v20, v21, off                              // 000000000324: F8000227 00151413
	exp param7 v27, v29, v30, off                              // 00000000032C: F8000277 001E1D1B
	exp param0 v7, v8, v9, off                                 // 000000000334: F8000207 00090807
_L3:
	s_endpgm                                                   // 00000000033C: BF810000

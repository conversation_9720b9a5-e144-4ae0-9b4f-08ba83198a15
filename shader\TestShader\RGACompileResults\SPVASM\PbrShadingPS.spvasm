; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 235
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD6 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "Albedo"
               OpMemberName %type_Material 1 "Metallic"
               OpMemberName %type_Material 2 "Roughness"
               OpMemberName %type_Material 3 "AO"
               OpMemberName %type_Material 4 "EmissiveColor"
               OpMemberName %type_Material 5 "EmissiveStrength"
               OpName %Material "Material"
               OpName %type_Lighting "type.Lighting"
               OpMemberName %type_Lighting 0 "LightPositions"
               OpMemberName %type_Lighting 1 "LightColors"
               OpMemberName %type_Lighting 2 "LightIntensities"
               OpMemberName %type_Lighting 3 "NumLights"
               OpName %Lighting "Lighting"
               OpName %type_2d_image "type.2d.image"
               OpName %AlbedoTexture "AlbedoTexture"
               OpName %NormalTexture "NormalTexture"
               OpName %MetallicTexture "MetallicTexture"
               OpName %RoughnessTexture "RoughnessTexture"
               OpName %AOTexture "AOTexture"
               OpName %EmissiveTexture "EmissiveTexture"
               OpName %type_cube_image "type.cube.image"
               OpName %EnvironmentMap "EnvironmentMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %Lighting DescriptorSet 0
               OpDecorate %Lighting Binding 1
               OpDecorate %AlbedoTexture DescriptorSet 0
               OpDecorate %AlbedoTexture Binding 0
               OpDecorate %NormalTexture DescriptorSet 0
               OpDecorate %NormalTexture Binding 1
               OpDecorate %MetallicTexture DescriptorSet 0
               OpDecorate %MetallicTexture Binding 2
               OpDecorate %RoughnessTexture DescriptorSet 0
               OpDecorate %RoughnessTexture Binding 3
               OpDecorate %AOTexture DescriptorSet 0
               OpDecorate %AOTexture Binding 4
               OpDecorate %EmissiveTexture DescriptorSet 0
               OpDecorate %EmissiveTexture Binding 5
               OpDecorate %EnvironmentMap DescriptorSet 0
               OpDecorate %EnvironmentMap Binding 6
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 12
               OpMemberDecorate %type_Material 2 Offset 16
               OpMemberDecorate %type_Material 3 Offset 20
               OpMemberDecorate %type_Material 4 Offset 32
               OpMemberDecorate %type_Material 5 Offset 44
               OpDecorate %type_Material Block
               OpDecorate %_arr_v3float_uint_4 ArrayStride 16
               OpDecorate %_arr_float_uint_4 ArrayStride 16
               OpMemberDecorate %type_Lighting 0 Offset 0
               OpMemberDecorate %type_Lighting 1 Offset 64
               OpMemberDecorate %type_Lighting 2 Offset 128
               OpMemberDecorate %type_Lighting 3 Offset 192
               OpDecorate %type_Lighting Block
      %float = OpTypeFloat 32
%float_3_14159274 = OpConstant %float 3.14159274
        %int = OpTypeInt 32 1
      %int_5 = OpConstant %int 5
      %int_0 = OpConstant %int 0
%float_2_20000005 = OpConstant %float 2.20000005
    %v3float = OpTypeVector %float 3
         %36 = OpConstantComposite %v3float %float_2_20000005 %float_2_20000005 %float_2_20000005
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
%float_0_0399999991 = OpConstant %float 0.0399999991
         %42 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
    %float_0 = OpConstant %float 0
         %44 = OpConstantComposite %v3float %float_0 %float_0 %float_0
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
    %float_1 = OpConstant %float 1
         %48 = OpConstantComposite %v3float %float_1 %float_1 %float_1
    %float_4 = OpConstant %float 4
%float_9_99999975en05 = OpConstant %float 9.99999975e-05
%float_0_454545468 = OpConstant %float 0.454545468
         %52 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
    %float_2 = OpConstant %float 2
    %float_5 = OpConstant %float 5
%type_Material = OpTypeStruct %v3float %float %float %float %v3float %float
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
       %uint = OpTypeInt 32 0
     %uint_4 = OpConstant %uint 4
%_arr_v3float_uint_4 = OpTypeArray %v3float %uint_4
%_arr_float_uint_4 = OpTypeArray %float %uint_4
%type_Lighting = OpTypeStruct %_arr_v3float_uint_4 %_arr_v3float_uint_4 %_arr_float_uint_4 %int
%_ptr_Uniform_type_Lighting = OpTypePointer Uniform %type_Lighting
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_cube_image = OpTypeImage %float Cube 2 0 0 1 Unknown
%_ptr_UniformConstant_type_cube_image = OpTypePointer UniformConstant %type_cube_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v4float = OpTypeVector %float 4
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %68 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%type_sampled_image_0 = OpTypeSampledImage %type_cube_image
%mat3v3float = OpTypeMatrix %v3float 3
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
   %Lighting = OpVariable %_ptr_Uniform_type_Lighting Uniform
%AlbedoTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%NormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%MetallicTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%RoughnessTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
  %AOTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EmissiveTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EnvironmentMap = OpVariable %_ptr_UniformConstant_type_cube_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
%float_0_125 = OpConstant %float 0.125
%float_0_318309873 = OpConstant %float 0.318309873
         %75 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
       %main = OpFunction %void None %68
         %76 = OpLabel
         %77 = OpLoad %v3float %in_var_TEXCOORD0
         %78 = OpLoad %v3float %in_var_TEXCOORD1
         %79 = OpLoad %v3float %in_var_TEXCOORD2
         %80 = OpLoad %v3float %in_var_TEXCOORD3
         %81 = OpLoad %v2float %in_var_TEXCOORD4
         %82 = OpLoad %v3float %in_var_TEXCOORD6
         %83 = OpLoad %type_2d_image %AlbedoTexture
         %84 = OpLoad %type_sampler %LinearSampler
         %85 = OpSampledImage %type_sampled_image %83 %84
         %86 = OpImageSampleImplicitLod %v4float %85 %81 None
         %87 = OpVectorShuffle %v3float %86 %86 0 1 2
         %88 = OpAccessChain %_ptr_Uniform_v3float %Material %int_0
         %89 = OpLoad %v3float %88
         %90 = OpFMul %v3float %87 %89
         %91 = OpExtInst %v3float %1 Pow %90 %36
         %92 = OpLoad %type_2d_image %MetallicTexture
         %93 = OpLoad %type_sampler %LinearSampler
         %94 = OpSampledImage %type_sampled_image %92 %93
         %95 = OpImageSampleImplicitLod %v4float %94 %81 None
         %96 = OpCompositeExtract %float %95 0
         %97 = OpAccessChain %_ptr_Uniform_float %Material %int_1
         %98 = OpLoad %float %97
         %99 = OpFMul %float %96 %98
        %100 = OpLoad %type_2d_image %RoughnessTexture
        %101 = OpLoad %type_sampler %LinearSampler
        %102 = OpSampledImage %type_sampled_image %100 %101
        %103 = OpImageSampleImplicitLod %v4float %102 %81 None
        %104 = OpCompositeExtract %float %103 0
        %105 = OpAccessChain %_ptr_Uniform_float %Material %int_2
        %106 = OpLoad %float %105
        %107 = OpFMul %float %104 %106
        %108 = OpLoad %type_2d_image %AOTexture
        %109 = OpLoad %type_sampler %LinearSampler
        %110 = OpSampledImage %type_sampled_image %108 %109
        %111 = OpImageSampleImplicitLod %v4float %110 %81 None
        %112 = OpCompositeExtract %float %111 0
        %113 = OpAccessChain %_ptr_Uniform_float %Material %int_3
        %114 = OpLoad %float %113
        %115 = OpFMul %float %112 %114
        %116 = OpLoad %type_2d_image %EmissiveTexture
        %117 = OpLoad %type_sampler %LinearSampler
        %118 = OpSampledImage %type_sampled_image %116 %117
        %119 = OpImageSampleImplicitLod %v4float %118 %81 None
        %120 = OpVectorShuffle %v3float %119 %119 0 1 2
        %121 = OpAccessChain %_ptr_Uniform_v3float %Material %int_4
        %122 = OpLoad %v3float %121
        %123 = OpFMul %v3float %120 %122
        %124 = OpAccessChain %_ptr_Uniform_float %Material %int_5
        %125 = OpLoad %float %124
        %126 = OpVectorTimesScalar %v3float %123 %125
        %127 = OpLoad %type_2d_image %NormalTexture
        %128 = OpLoad %type_sampler %LinearSampler
        %129 = OpSampledImage %type_sampled_image %127 %128
        %130 = OpImageSampleImplicitLod %v4float %129 %81 None
        %131 = OpVectorShuffle %v3float %130 %130 0 1 2
        %132 = OpVectorTimesScalar %v3float %131 %float_2
        %133 = OpFSub %v3float %132 %48
        %134 = OpCompositeConstruct %mat3v3float %79 %80 %78
        %135 = OpMatrixTimesVector %v3float %134 %133
        %136 = OpExtInst %v3float %1 Normalize %135
        %137 = OpExtInst %v3float %1 Normalize %82
        %138 = OpCompositeConstruct %v3float %99 %99 %99
        %139 = OpExtInst %v3float %1 FMix %42 %91 %138
               OpBranch %140
        %140 = OpLabel
        %141 = OpPhi %v3float %44 %76 %142 %143
        %144 = OpPhi %int %int_0 %76 %145 %143
               OpLoopMerge %146 %143 None
               OpBranch %147
        %147 = OpLabel
        %148 = OpAccessChain %_ptr_Uniform_int %Lighting %int_3
        %149 = OpLoad %int %148
        %150 = OpSLessThan %bool %144 %149
               OpSelectionMerge %151 None
               OpBranchConditional %150 %152 %151
        %152 = OpLabel
        %153 = OpSLessThan %bool %144 %int_4
               OpBranch %151
        %151 = OpLabel
        %154 = OpPhi %bool %false %147 %153 %152
               OpBranchConditional %154 %143 %146
        %143 = OpLabel
        %155 = OpAccessChain %_ptr_Uniform_v3float %Lighting %int_0 %144
        %156 = OpLoad %v3float %155
        %157 = OpFSub %v3float %156 %77
        %158 = OpExtInst %v3float %1 Normalize %157
        %159 = OpFAdd %v3float %137 %158
        %160 = OpExtInst %v3float %1 Normalize %159
        %161 = OpExtInst %float %1 Length %157
        %162 = OpFMul %float %161 %161
        %163 = OpFDiv %float %float_1 %162
        %164 = OpAccessChain %_ptr_Uniform_v3float %Lighting %int_1 %144
        %165 = OpLoad %v3float %164
        %166 = OpAccessChain %_ptr_Uniform_float %Lighting %int_2 %144
        %167 = OpLoad %float %166
        %168 = OpVectorTimesScalar %v3float %165 %167
        %169 = OpVectorTimesScalar %v3float %168 %163
        %170 = OpFMul %float %107 %107
        %171 = OpFMul %float %170 %170
        %172 = OpDot %float %136 %160
        %173 = OpExtInst %float %1 NMax %172 %float_0
        %174 = OpFMul %float %173 %173
        %175 = OpFSub %float %171 %float_1
        %176 = OpFMul %float %174 %175
        %177 = OpFAdd %float %176 %float_1
        %178 = OpFMul %float %float_3_14159274 %177
        %179 = OpFMul %float %178 %177
        %180 = OpFDiv %float %171 %179
        %181 = OpDot %float %136 %137
        %182 = OpExtInst %float %1 NMax %181 %float_0
        %183 = OpDot %float %136 %158
        %184 = OpExtInst %float %1 NMax %183 %float_0
        %185 = OpFAdd %float %107 %float_1
        %186 = OpFMul %float %185 %185
        %187 = OpFMul %float %186 %float_0_125
        %188 = OpFSub %float %float_1 %187
        %189 = OpFMul %float %182 %188
        %190 = OpFAdd %float %189 %187
        %191 = OpFDiv %float %182 %190
        %192 = OpFMul %float %184 %188
        %193 = OpFAdd %float %192 %187
        %194 = OpFDiv %float %184 %193
        %195 = OpFMul %float %194 %191
        %196 = OpDot %float %160 %137
        %197 = OpExtInst %float %1 NMax %196 %float_0
        %198 = OpFSub %v3float %48 %139
        %199 = OpFSub %float %float_1 %197
        %200 = OpExtInst %float %1 FClamp %199 %float_0 %float_1
        %201 = OpExtInst %float %1 Pow %200 %float_5
        %202 = OpVectorTimesScalar %v3float %198 %201
        %203 = OpFAdd %v3float %139 %202
        %204 = OpFSub %v3float %48 %203
        %205 = OpFSub %float %float_1 %99
        %206 = OpVectorTimesScalar %v3float %204 %205
        %207 = OpFMul %float %180 %195
        %208 = OpVectorTimesScalar %v3float %203 %207
        %209 = OpFMul %float %float_4 %182
        %210 = OpFMul %float %209 %184
        %211 = OpFAdd %float %210 %float_9_99999975en05
        %212 = OpCompositeConstruct %v3float %211 %211 %211
        %213 = OpFDiv %v3float %208 %212
        %214 = OpFMul %v3float %206 %91
        %215 = OpFMul %v3float %214 %75
        %216 = OpFAdd %v3float %215 %213
        %217 = OpFMul %v3float %216 %169
        %218 = OpVectorTimesScalar %v3float %217 %184
        %142 = OpFAdd %v3float %141 %218
        %145 = OpIAdd %int %144 %int_1
               OpBranch %140
        %146 = OpLabel
        %219 = OpLoad %type_cube_image %EnvironmentMap
        %220 = OpLoad %type_sampler %LinearSampler
        %221 = OpSampledImage %type_sampled_image_0 %219 %220
        %222 = OpImageSampleImplicitLod %v4float %221 %136 None
        %223 = OpVectorShuffle %v3float %222 %222 0 1 2
        %224 = OpFMul %v3float %223 %91
        %225 = OpVectorTimesScalar %v3float %224 %115
        %226 = OpFAdd %v3float %225 %141
        %227 = OpFAdd %v3float %226 %126
        %228 = OpFAdd %v3float %227 %48
        %229 = OpFDiv %v3float %227 %228
        %230 = OpExtInst %v3float %1 Pow %229 %52
        %231 = OpCompositeExtract %float %230 0
        %232 = OpCompositeExtract %float %230 1
        %233 = OpCompositeExtract %float %230 2
        %234 = OpCompositeConstruct %v4float %231 %232 %233 %float_1
               OpStore %out_var_SV_TARGET %234
               OpReturn
               OpFunctionEnd

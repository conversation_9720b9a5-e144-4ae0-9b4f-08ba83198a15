_amdgpu_ps_main:
	s_mov_b32 m0, s2                                           // 000000000000: BEFC0302
	s_mov_b32 s8, s1                                           // 000000000004: BE880301
	v_interp_p1_f32_e32 v5, v0, attr0.y                        // 000000000008: C8140100
	v_interp_p1_f32_e32 v4, v0, attr0.x                        // 00000000000C: C8100000
	s_getpc_b64 s[0:1]                                         // 000000000010: BE801F00
	v_interp_p1_f32_e32 v6, v0, attr0.z                        // 000000000014: C8180200
	s_mov_b32 s9, s1                                           // 000000000018: BE890301
	v_interp_p2_f32_e32 v5, v1, attr0.y                        // 00000000001C: C8150101
	s_load_dwordx4 s[16:19], s[8:9], null                      // 000000000020: F4080404 FA000000
	v_interp_p2_f32_e32 v4, v1, attr0.x                        // 000000000028: C8110001
	v_interp_p2_f32_e32 v6, v1, attr0.z                        // 00000000002C: C8190201
	v_interp_p1_f32_e32 v2, v0, attr1.x                        // 000000000030: C8080400
	v_mul_f32_e32 v3, v5, v5                                   // 000000000034: 10060B05
	s_mov_b64 s[4:5], exec                                     // 000000000038: BE84047E
	s_waitcnt lgkmcnt(0)                                       // 00000000003C: BF8CC07F
	s_clause 0x2                                               // 000000000040: BFA10002
	s_buffer_load_dword s33, s[16:19], 0x8                     // 000000000044: F4200848 FA000008
	s_buffer_load_dwordx2 s[2:3], s[16:19], 0x10               // 00000000004C: F4240088 FA000010
	s_buffer_load_dword s30, s[16:19], 0x18                    // 000000000054: F4200788 FA000018
	v_fmac_f32_e32 v3, v4, v4                                  // 00000000005C: 56060904
	s_buffer_load_dwordx2 s[24:25], s[16:19], null             // 000000000060: F4240608 FA000000
	v_interp_p2_f32_e32 v2, v1, attr1.x                        // 000000000068: C8090401
	v_fmac_f32_e32 v3, v6, v6                                  // 00000000006C: 56060D06
	v_rsq_f32_e32 v7, v3                                       // 000000000070: 7E0E5D03
	v_cmp_neq_f32_e32 vcc_lo, 0, v3                            // 000000000074: 7C1A0680
	v_interp_p1_f32_e32 v3, v0, attr1.y                        // 000000000078: C80C0500
	v_interp_p1_f32_e32 v0, v0, attr1.z                        // 00000000007C: C8000600
	v_interp_p2_f32_e32 v3, v1, attr1.y                        // 000000000080: C80D0501
	v_interp_p2_f32_e32 v0, v1, attr1.z                        // 000000000084: C8010601
	s_waitcnt lgkmcnt(0)                                       // 000000000088: BF8CC07F
	v_sub_f32_e32 v12, s2, v2                                  // 00000000008C: 08180402
	v_cndmask_b32_e32 v7, 0, v7, vcc_lo                        // 000000000090: 020E0E80
	v_sub_f32_e32 v9, s24, v2                                  // 000000000094: 08120418
	v_sub_f32_e32 v10, s25, v3                                 // 000000000098: 08140619
	v_sub_f32_e32 v11, s33, v0                                 // 00000000009C: 08160021
	v_sub_f32_e32 v13, s3, v3                                  // 0000000000A0: 081A0603
	v_mul_f32_e32 v4, v7, v4                                   // 0000000000A4: 10080907
	v_mul_f32_e32 v5, v7, v5                                   // 0000000000A8: 100A0B07
	v_mul_f32_e32 v6, v7, v6                                   // 0000000000AC: 100C0D07
	v_sub_f32_e32 v14, s30, v0                                 // 0000000000B0: 081C001E
	v_rcp_f32_e32 v1, v4                                       // 0000000000B4: 7E025504
	v_rcp_f32_e32 v7, v5                                       // 0000000000B8: 7E0E5505
	v_rcp_f32_e32 v8, v6                                       // 0000000000BC: 7E105506
	v_mul_f32_e32 v9, v9, v1                                   // 0000000000C0: 10120309
	v_mul_f32_e32 v10, v10, v7                                 // 0000000000C4: 10140F0A
	v_mul_f32_e32 v11, v11, v8                                 // 0000000000C8: 1016110B
	v_mul_f32_e32 v1, v12, v1                                  // 0000000000CC: 1002030C
	v_mul_f32_e32 v7, v13, v7                                  // 0000000000D0: 100E0F0D
	v_mul_f32_e32 v8, v14, v8                                  // 0000000000D4: 1010110E
	v_min_f32_e32 v12, v9, v1                                  // 0000000000D8: 1E180309
	v_min_f32_e32 v13, v10, v7                                 // 0000000000DC: 1E1A0F0A
	v_min_f32_e32 v14, v11, v8                                 // 0000000000E0: 1E1C110B
	v_max_f32_e32 v1, v9, v1                                   // 0000000000E4: 20020309
	v_max_f32_e32 v9, v10, v7                                  // 0000000000E8: 20120F0A
	v_max_f32_e32 v8, v11, v8                                  // 0000000000EC: 2010110B
	v_max3_f32 v7, v12, v13, v14                               // 0000000000F0: D5540007 043A1B0C
	v_min3_f32 v1, v1, v9, v8                                  // 0000000000F8: D5510001 04221301
	v_cmp_gt_f32_e32 vcc_lo, v1, v7                            // 000000000100: 7C080F01
	v_cmp_lt_f32_e64 s0, 0, v1                                 // 000000000104: D4010000 00020280
	s_and_b64 s[0:1], vcc, s[0:1]                              // 00000000010C: 8780006A
	s_andn2_b64 s[0:1], exec, s[0:1]                           // 000000000110: 8A80007E
	s_andn2_b64 s[4:5], s[4:5], s[0:1]                         // 000000000114: 8A840004
	s_cbranch_scc0 _L0                                         // 000000000118: BF8400D1
	s_and_b64 exec, exec, s[4:5]                               // 00000000011C: 87FE047E
	s_buffer_load_dword s40, s[16:19], 0x1c                    // 000000000120: F4200A08 FA00001C
	v_max_f32_e32 v7, 0, v7                                    // 000000000128: 200E0E80
	v_mov_b32_e32 v10, 0                                       // 00000000012C: 7E140280
	v_mov_b32_e32 v8, 1.0                                      // 000000000130: 7E1002F2
	v_mov_b32_e32 v11, 0                                       // 000000000134: 7E160280
	v_mov_b32_e32 v9, 0                                        // 000000000138: 7E120280
	v_cmp_lt_f32_e32 vcc_lo, v7, v1                            // 00000000013C: 7C020307
	s_waitcnt lgkmcnt(0)                                       // 000000000140: BF8CC07F
	s_cmp_gt_i32 s40, 0                                        // 000000000144: BF028028
	s_cselect_b64 s[0:1], -1, 0                                // 000000000148: 858080C1
	s_and_b64 s[0:1], vcc, s[0:1]                              // 00000000014C: 8780006A
	s_and_saveexec_b64 s[26:27], s[0:1]                        // 000000000150: BE9A2400
	s_cbranch_execz _L1                                        // 000000000154: BF8800BD
	s_clause 0x1                                               // 000000000158: BFA10001
	s_buffer_load_dwordx2 s[28:29], s[16:19], 0x50             // 00000000015C: F4240708 FA000050
	s_buffer_load_dword s31, s[16:19], 0x58                    // 000000000164: F42007C8 FA000058
	v_sub_f32_e64 v10, s2, s24                                 // 00000000016C: D504000A 00003002
	v_sub_f32_e64 v11, s3, s25                                 // 000000000174: D504000B 00003203
	s_clause 0x2                                               // 00000000017C: BFA10002
	s_load_dwordx8 s[0:7], s[8:9], null                        // 000000000180: F40C0004 FA000000
	s_load_dwordx4 s[20:23], s[8:9], 0x20                      // 000000000188: F4080504 FA000020
	s_load_dwordx8 s[8:15], s[8:9], 0x30                       // 000000000190: F40C0204 FA000030
	s_buffer_load_dword s41, s[16:19], 0x2c                    // 000000000198: F4200A48 FA00002C
	v_sub_f32_e64 v14, s30, s33                                // 0000000001A0: D504000E 0000421E
	v_mov_b32_e32 v9, 0                                        // 0000000001A8: 7E120280
	v_rcp_f32_e32 v12, v10                                     // 0000000001AC: 7E18550A
	v_rcp_f32_e32 v13, v11                                     // 0000000001B0: 7E1A550B
	v_mov_b32_e32 v8, 1.0                                      // 0000000001B4: 7E1002F2
	v_rcp_f32_e32 v14, v14                                     // 0000000001B8: 7E1C550E
	v_mov_b32_e32 v11, 0                                       // 0000000001BC: 7E160280
	v_mov_b32_e32 v10, 0                                       // 0000000001C0: 7E140280
	s_mov_b32 s42, 0xbf19999a                                  // 0000000001C4: BEAA03FF BF19999A
	s_waitcnt lgkmcnt(0)                                       // 0000000001CC: BF8CC07F
	v_mul_f32_e64 v15, 0x3dcccccd, s28                         // 0000000001D0: D508000F 000038FF 3DCCCCCD
	v_mul_f32_e64 v16, 0x3d4ccccd, s28                         // 0000000001DC: D5080010 000038FF 3D4CCCCD
	v_mul_f32_e64 v17, 0x3da3d70a, s28                         // 0000000001E8: D5080011 000038FF 3DA3D70A
	v_mul_f32_e64 v18, s31, 0.5                                // 0000000001F4: D5080012 0001E01F
	v_mul_f32_e64 v19, 0x3e800000, s31                         // 0000000001FC: D5080013 00003EFF 3E800000
	v_mul_f32_e64 v20, 0x3e000000, s31                         // 000000000208: D5080014 00003EFF 3E000000
	v_mul_f32_e64 v21, 0x3d800000, s31                         // 000000000214: D5080015 00003EFF 3D800000
	s_mov_b32 s28, 1                                           // 000000000220: BE9C0381
	s_mov_b64 s[30:31], 0                                      // 000000000224: BE9E0480
	s_branch _L2                                               // 000000000228: BF820005
_L5:
	s_or_b64 exec, exec, s[36:37]                              // 00000000022C: 88FE247E
	s_and_b64 s[34:35], exec, s[34:35]                         // 000000000230: 87A2227E
	s_or_b64 s[30:31], s[34:35], s[30:31]                      // 000000000234: 889E1E22
	s_andn2_b64 exec, exec, s[30:31]                           // 000000000238: 8AFE1E7E
	s_cbranch_execz _L3                                        // 00000000023C: BF880082
_L2:
	v_fma_f32 v22, v7, v6, v0                                  // 000000000240: D54B0016 04020D07
	v_fma_f32 v24, v7, v5, v3                                  // 000000000248: D54B0018 040E0B07
	v_fma_f32 v26, v7, v4, v2                                  // 000000000250: D54B001A 040A0907
	s_mov_b64 s[34:35], -1                                     // 000000000258: BEA204C1
	s_mov_b64 s[38:39], -1                                     // 00000000025C: BEA604C1
	v_fma_f32 v23, s29, v22, v17                               // 000000000260: D54B0017 04462C1D
	v_fma_f32 v25, s29, v24, v16                               // 000000000268: D54B0019 0442301D
	v_fma_f32 v27, s29, v26, v15                               // 000000000270: D54B001B 043E341D
	v_subrev_f32_e32 v26, s24, v26                             // 000000000278: 0A343418
	v_subrev_f32_e32 v24, s25, v24                             // 00000000027C: 0A303019
	v_subrev_f32_e32 v22, s33, v22                             // 000000000280: 0A2C2C21
	s_mov_b64 s[36:37], exec                                   // 000000000284: BEA4047E
	v_mul_f32_e32 v28, v26, v12                                // 000000000288: 1038191A
	v_mul_f32_e32 v29, v24, v13                                // 00000000028C: 103A1B18
	v_mul_f32_e32 v30, v22, v14                                // 000000000290: 103C1D16
	image_sample_lz v22, v[28:30], s[0:7], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 000000000294: F09C0110 00A0161C
	image_sample_lz  v24, [v27, v25, v23], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 00000000029C: F09C0112 00A2181B 00001719
	v_add_f32_e32 v28, v27, v27                                // 0000000002A8: 0638371B
	v_add_f32_e32 v29, v25, v25                                // 0000000002AC: 063A3319
	v_add_f32_e32 v30, v23, v23                                // 0000000002B0: 063C2F17
	image_sample_lz v26, v[28:30], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 0000000002B4: F09C0110 00A21A1C
	v_mul_f32_e32 v28, 4.0, v27                                // 0000000002BC: 103836F6
	v_mul_f32_e32 v29, 4.0, v25                                // 0000000002C0: 103A32F6
	v_mul_f32_e32 v30, 4.0, v23                                // 0000000002C4: 103C2EF6
	v_mul_f32_e32 v25, 0x41000000, v25                         // 0000000002C8: 103232FF 41000000
	s_waitcnt vmcnt(1)                                         // 0000000002D0: BF8C3F71
	v_fmac_f32_e32 v22, v24, v18                               // 0000000002D4: 562C2518
	image_sample_lz v24, v[28:30], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 0000000002D8: F09C0110 00A2181C
	s_waitcnt vmcnt(1)                                         // 0000000002E0: BF8C3F71
	v_fmac_f32_e32 v22, v26, v19                               // 0000000002E4: 562C271A
	v_mul_f32_e32 v26, 0x41000000, v23                         // 0000000002E8: 10342EFF 41000000
	s_waitcnt vmcnt(0)                                         // 0000000002F0: BF8C3F70
	v_fmac_f32_e32 v22, v24, v20                               // 0000000002F4: 562C2918
	v_mul_f32_e32 v24, 0x41000000, v27                         // 0000000002F8: 103036FF 41000000
	image_sample_lz v23, v[24:26], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 000000000300: F09C0110 00A21718
	s_waitcnt vmcnt(0)                                         // 000000000308: BF8C3F70
	v_fmac_f32_e32 v22, v23, v21                               // 00000000030C: 562C2B17
	v_mul_f32_e32 v22, s41, v22                                // 000000000310: 102C2C29
	v_max_f32_e32 v22, 0, v22                                  // 000000000314: 202C2C80
	v_cmpx_lt_f32_e32 0x3a83126f, v22                          // 000000000318: 7C222CFF 3A83126F
	s_cbranch_execz _L4                                        // 000000000320: BF88003B
	s_clause 0x3                                               // 000000000324: BFA10003
	s_buffer_load_dwordx2 s[38:39], s[16:19], 0x20             // 000000000328: F4240988 FA000020
	s_buffer_load_dword s43, s[16:19], 0x28                    // 000000000330: F4200AC8 FA000028
	s_buffer_load_dword s52, s[16:19], 0xc                     // 000000000338: F4200D08 FA00000C
	s_buffer_load_dwordx8 s[44:51], s[16:19], 0x30             // 000000000340: F42C0B08 FA000030
	s_waitcnt lgkmcnt(0)                                       // 000000000348: BF8CC07F
	v_mul_f32_e64 v23, s39, s39                                // 00000000034C: D5080017 00004E27
	v_fmac_f32_e64 v23, s38, s38                               // 000000000354: D52B0017 00004C26
	v_fmac_f32_e64 v23, s43, s43                               // 00000000035C: D52B0017 0000562B
	v_rsq_f32_e32 v24, v23                                     // 000000000364: 7E305D17
	v_cmp_neq_f32_e32 vcc_lo, 0, v23                           // 000000000368: 7C1A2E80
	v_cndmask_b32_e32 v23, 0, v24, vcc_lo                      // 00000000036C: 022E3080
	v_mul_f32_e32 v24, s43, v23                                // 000000000370: 10302E2B
	v_mul_f32_e32 v25, s38, v23                                // 000000000374: 10322E26
	v_mul_f32_e32 v23, s39, v23                                // 000000000378: 102E2E27
	v_mul_f32_e32 v24, v6, v24                                 // 00000000037C: 10303106
	v_fmac_f32_e32 v24, v4, v25                                // 000000000380: 56303304
	v_add_f32_e64 v25, s47, s51                                // 000000000384: D5030019 0000662F
	v_fmac_f32_e32 v24, v23, v5                                // 00000000038C: 56300B17
	v_mul_f32_e64 v23, 0xbfb8aa3b, s52                         // 000000000390: D5080017 000068FF BFB8AA3B
	v_mul_f32_e32 v25, v25, v22                                // 00000000039C: 10322D19
	v_mul_f32_e32 v22, v8, v22                                 // 0000000003A0: 102C2D08
	v_fmaak_f32 v24, s42, v24, 0x3f8b851f                      // 0000000003A4: 5A30302A 3F8B851F
	v_mul_f32_e32 v23, v23, v25                                // 0000000003AC: 102E3317
	v_mul_f32_e32 v22, s51, v22                                // 0000000003B0: 102C2C33
	v_sqrt_f32_e32 v26, v24                                    // 0000000003B4: 7E346718
	v_exp_f32_e32 v23, v23                                     // 0000000003B8: 7E2E4B17
	v_mul_f32_e32 v24, v26, v24                                // 0000000003BC: 1030311A
	v_sub_f32_e32 v26, 1.0, v23                                // 0000000003C0: 08342EF2
	v_mul_f32_e32 v8, v23, v8                                  // 0000000003C4: 10101117
	v_mul_f32_e64 v23, s49, s45                                // 0000000003C8: D5080017 00005A31
	v_mul_f32_e32 v24, v24, v25                                // 0000000003D0: 10303318
	v_mul_f32_e64 v25, s48, s44                                // 0000000003D4: D5080019 00005830
	v_mul_f32_e32 v22, v22, v26                                // 0000000003DC: 102C3516
	v_cmp_ngt_f32_e32 vcc_lo, 0x3c23d70a, v8                   // 0000000003E0: 7C1610FF 3C23D70A
	v_rcp_f32_e32 v24, v24                                     // 0000000003E8: 7E305518
	v_mul_f32_e32 v22, 0x3d944e93, v22                         // 0000000003EC: 102C2CFF 3D944E93
	s_orn2_b64 s[38:39], vcc, exec                             // 0000000003F4: 8BA67E6A
	v_mul_f32_e32 v22, v22, v24                                // 0000000003F8: 102C3116
	v_mul_f32_e64 v24, s50, s46                                // 0000000003FC: D5080018 00005C32
	v_fmac_f32_e32 v9, v25, v22                                // 000000000404: 56122D19
	v_fmac_f32_e32 v11, v23, v22                               // 000000000408: 56162D17
	v_fmac_f32_e32 v10, v24, v22                               // 00000000040C: 56142D18
_L4:
	s_or_b64 exec, exec, s[36:37]                              // 000000000410: 88FE247E
	s_and_saveexec_b64 s[36:37], s[38:39]                      // 000000000414: BEA42426
	s_cbranch_execz _L5                                        // 000000000418: BF88FF84
	s_buffer_load_dword s34, s[16:19], 0xc                     // 00000000041C: F4200888 FA00000C
	s_cmp_ge_i32 s28, s40                                      // 000000000424: BF03281C
	s_waitcnt lgkmcnt(0)                                       // 000000000428: BF8CC07F
	v_add_f32_e32 v7, s34, v7                                  // 00000000042C: 060E0E22
	s_cselect_b64 s[34:35], -1, 0                              // 000000000430: 85A280C1
	s_add_i32 s28, s28, 1                                      // 000000000434: 811C811C
	v_cmp_nlt_f32_e32 vcc_lo, v7, v1                           // 000000000438: 7C1C0307
	s_or_b64 s[34:35], vcc, s[34:35]                           // 00000000043C: 88A2226A
	s_orn2_b64 s[34:35], s[34:35], exec                        // 000000000440: 8BA27E22
	s_branch _L5                                               // 000000000444: BF82FF79
_L3:
	s_or_b64 exec, exec, s[30:31]                              // 000000000448: 88FE1E7E
_L1:
	s_or_b64 exec, exec, s[26:27]                              // 00000000044C: 88FE1A7E
	v_sub_f32_e32 v0, 1.0, v8                                  // 000000000450: 080010F2
	exp mrt0 v9, v11, v10, v0 done vm                          // 000000000454: F800180F 000A0B09
	s_endpgm                                                   // 00000000045C: BF810000
_L0:
	s_mov_b64 exec, 0                                          // 000000000460: BEFE0480
	exp null off, off, off, off done vm                        // 000000000464: F8001890 00000000
	s_endpgm                                                   // 00000000046C: BF810000

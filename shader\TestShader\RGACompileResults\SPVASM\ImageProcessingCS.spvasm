; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 351
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ProcessingParams "type.ProcessingParams"
               OpMemberName %type_ProcessingParams 0 "ImageWidth"
               OpMemberName %type_ProcessingParams 1 "ImageHeight"
               OpMemberName %type_ProcessingParams 2 "BlurRadius"
               OpMemberName %type_ProcessingParams 3 "Brightness"
               OpMemberName %type_ProcessingParams 4 "Contrast"
               OpMemberName %type_ProcessingParams 5 "Saturation"
               OpMemberName %type_ProcessingParams 6 "Gamma"
               OpMemberName %type_ProcessingParams 7 "FilterType"
               OpName %ProcessingParams "ProcessingParams"
               OpName %type_2d_image "type.2d.image"
               OpName %InputTexture "InputTexture"
               OpName %type_2d_image_0 "type.2d.image"
               OpName %OutputTexture "OutputTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %GaussianKernel "GaussianKernel"
               OpName %SharpenKernel "SharpenKernel"
               OpName %EdgeKernelX "EdgeKernelX"
               OpName %EdgeKernelY "EdgeKernelY"
               OpName %EmbossKernel "EmbossKernel"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %ProcessingParams DescriptorSet 0
               OpDecorate %ProcessingParams Binding 0
               OpDecorate %InputTexture DescriptorSet 0
               OpDecorate %InputTexture Binding 0
               OpDecorate %OutputTexture DescriptorSet 0
               OpDecorate %OutputTexture Binding 0
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_ProcessingParams 0 Offset 0
               OpMemberDecorate %type_ProcessingParams 1 Offset 4
               OpMemberDecorate %type_ProcessingParams 2 Offset 8
               OpMemberDecorate %type_ProcessingParams 3 Offset 12
               OpMemberDecorate %type_ProcessingParams 4 Offset 16
               OpMemberDecorate %type_ProcessingParams 5 Offset 20
               OpMemberDecorate %type_ProcessingParams 6 Offset 24
               OpMemberDecorate %type_ProcessingParams 7 Offset 28
               OpDecorate %type_ProcessingParams Block
      %float = OpTypeFloat 32
%float_0_00376500003 = OpConstant %float 0.00376500003
%float_0_0150189996 = OpConstant %float 0.0150189996
%float_0_0237920005 = OpConstant %float 0.0237920005
%float_0_0599119999 = OpConstant %float 0.0599119999
%float_0_0949070007 = OpConstant %float 0.0949070007
%float_0_150342003 = OpConstant %float 0.150342003
    %float_0 = OpConstant %float 0
   %float_n1 = OpConstant %float -1
    %float_5 = OpConstant %float 5
    %float_1 = OpConstant %float 1
   %float_n2 = OpConstant %float -2
    %float_2 = OpConstant %float 2
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
      %int_1 = OpConstant %int 1
      %int_7 = OpConstant %int 7
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
    %v4float = OpTypeVector %float 4
         %40 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
    %v2float = OpTypeVector %float 2
         %42 = OpConstantComposite %v2float %float_1 %float_1
     %int_n2 = OpConstant %int -2
      %int_2 = OpConstant %int 2
      %int_5 = OpConstant %int 5
     %uint_1 = OpConstant %uint 1
     %int_n1 = OpConstant %int -1
      %int_3 = OpConstant %int 3
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
  %float_0_5 = OpConstant %float 0.5
         %52 = OpConstantComposite %v4float %float_0_5 %float_0_5 %float_0_5 %float_0_5
    %v3float = OpTypeVector %float 3
         %54 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
      %int_4 = OpConstant %int 4
      %int_6 = OpConstant %int 6
         %57 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
%float_n0_333333343 = OpConstant %float -0.333333343
%float_0_666666687 = OpConstant %float 0.666666687
%float_1_00000001en10 = OpConstant %float 1.00000001e-10
    %float_6 = OpConstant %float 6
%float_0_333333343 = OpConstant %float 0.333333343
    %float_3 = OpConstant %float 3
         %64 = OpConstantComposite %v3float %float_0 %float_0 %float_0
         %65 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%type_ProcessingParams = OpTypeStruct %uint %uint %float %float %float %float %float %uint
%_ptr_Uniform_type_ProcessingParams = OpTypePointer Uniform %type_ProcessingParams
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_2d_image_0 = OpTypeImage %float 2D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_2d_image_0 = OpTypePointer UniformConstant %type_2d_image_0
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %uint_25 = OpConstant %uint 25
%_arr_float_uint_25 = OpTypeArray %float %uint_25
     %uint_9 = OpConstant %uint 9
%_arr_float_uint_9 = OpTypeArray %float %uint_9
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %77 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
     %v2uint = OpTypeVector %uint 2
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Function_float = OpTypePointer Function %float
%ProcessingParams = OpVariable %_ptr_Uniform_type_ProcessingParams Uniform
%InputTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%OutputTexture = OpVariable %_ptr_UniformConstant_type_2d_image_0 UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%_ptr_Function__arr_float_uint_25 = OpTypePointer Function %_arr_float_uint_25
%_ptr_Function__arr_float_uint_9 = OpTypePointer Function %_arr_float_uint_9
         %84 = OpConstantComposite %_arr_float_uint_25 %float_0_00376500003 %float_0_0150189996 %float_0_0237920005 %float_0_0150189996 %float_0_00376500003 %float_0_0150189996 %float_0_0599119999 %float_0_0949070007 %float_0_0599119999 %float_0_0150189996 %float_0_0237920005 %float_0_0949070007 %float_0_150342003 %float_0_0949070007 %float_0_0237920005 %float_0_0150189996 %float_0_0599119999 %float_0_0949070007 %float_0_0599119999 %float_0_0150189996 %float_0_00376500003 %float_0_0150189996 %float_0_0237920005 %float_0_0150189996 %float_0_00376500003
         %85 = OpConstantComposite %_arr_float_uint_9 %float_0 %float_n1 %float_0 %float_n1 %float_5 %float_n1 %float_0 %float_n1 %float_0
         %86 = OpConstantComposite %_arr_float_uint_9 %float_n1 %float_0 %float_1 %float_n2 %float_0 %float_2 %float_n1 %float_0 %float_1
         %87 = OpConstantComposite %_arr_float_uint_9 %float_n1 %float_n2 %float_n1 %float_0 %float_0 %float_0 %float_1 %float_2 %float_1
         %88 = OpConstantComposite %_arr_float_uint_9 %float_n2 %float_n1 %float_0 %float_n1 %float_1 %float_1 %float_0 %float_1 %float_2
         %89 = OpConstantComposite %v3float %float_1 %float_0_666666687 %float_0_333333343
         %90 = OpConstantComposite %v3float %float_3 %float_3 %float_3
         %91 = OpConstantNull %v4float
       %main = OpFunction %void None %77
         %92 = OpLabel
%EmbossKernel = OpVariable %_ptr_Function__arr_float_uint_9 Function
%EdgeKernelY = OpVariable %_ptr_Function__arr_float_uint_9 Function
%EdgeKernelX = OpVariable %_ptr_Function__arr_float_uint_9 Function
%SharpenKernel = OpVariable %_ptr_Function__arr_float_uint_9 Function
%GaussianKernel = OpVariable %_ptr_Function__arr_float_uint_25 Function
               OpStore %GaussianKernel %84
               OpStore %SharpenKernel %85
               OpStore %EdgeKernelX %86
               OpStore %EdgeKernelY %87
               OpStore %EmbossKernel %88
         %93 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %94 None
               OpSwitch %uint_0 %95
         %95 = OpLabel
         %96 = OpCompositeExtract %uint %93 0
         %97 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_0
         %98 = OpLoad %uint %97
         %99 = OpUGreaterThanEqual %bool %96 %98
        %100 = OpLogicalNot %bool %99
               OpSelectionMerge %101 None
               OpBranchConditional %100 %102 %101
        %102 = OpLabel
        %103 = OpCompositeExtract %uint %93 1
        %104 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_1
        %105 = OpLoad %uint %104
        %106 = OpUGreaterThanEqual %bool %103 %105
               OpBranch %101
        %101 = OpLabel
        %107 = OpPhi %bool %true %95 %106 %102
               OpSelectionMerge %108 None
               OpBranchConditional %107 %109 %108
        %109 = OpLabel
               OpBranch %94
        %108 = OpLabel
        %110 = OpVectorShuffle %v2uint %93 %93 0 1
        %111 = OpConvertUToF %v2float %110
        %112 = OpConvertUToF %float %98
        %113 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_1
        %114 = OpLoad %uint %113
        %115 = OpConvertUToF %float %114
        %116 = OpCompositeConstruct %v2float %112 %115
        %117 = OpFDiv %v2float %111 %116
        %118 = OpLoad %type_2d_image %InputTexture
        %119 = OpLoad %type_sampler %LinearSampler
        %120 = OpSampledImage %type_sampled_image %118 %119
        %121 = OpImageSampleExplicitLod %v4float %120 %117 Lod %float_0
        %122 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_7
        %123 = OpLoad %uint %122
        %124 = OpIEqual %bool %123 %uint_0
               OpSelectionMerge %125 None
               OpBranchConditional %124 %126 %127
        %126 = OpLabel
        %128 = OpFDiv %v2float %42 %116
               OpBranch %129
        %129 = OpLabel
        %130 = OpPhi %v4float %40 %126 %131 %132
        %133 = OpPhi %int %int_n2 %126 %134 %132
        %135 = OpSLessThanEqual %bool %133 %int_2
               OpLoopMerge %136 %132 None
               OpBranchConditional %135 %137 %136
        %137 = OpLabel
               OpBranch %138
        %138 = OpLabel
        %131 = OpPhi %v4float %130 %137 %139 %140
        %141 = OpPhi %int %int_n2 %137 %142 %140
        %143 = OpSLessThanEqual %bool %141 %int_2
               OpLoopMerge %144 %140 None
               OpBranchConditional %143 %140 %144
        %140 = OpLabel
        %145 = OpConvertSToF %float %141
        %146 = OpConvertSToF %float %133
        %147 = OpCompositeConstruct %v2float %145 %146
        %148 = OpFMul %v2float %147 %128
        %149 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_2
        %150 = OpLoad %float %149
        %151 = OpVectorTimesScalar %v2float %148 %150
        %152 = OpFAdd %v2float %117 %151
        %153 = OpLoad %type_2d_image %InputTexture
        %154 = OpLoad %type_sampler %LinearSampler
        %155 = OpSampledImage %type_sampled_image %153 %154
        %156 = OpImageSampleExplicitLod %v4float %155 %152 Lod %float_0
        %157 = OpIAdd %int %133 %int_2
        %158 = OpIMul %int %157 %int_5
        %159 = OpIAdd %int %141 %int_2
        %160 = OpIAdd %int %158 %159
        %161 = OpAccessChain %_ptr_Function_float %GaussianKernel %160
        %162 = OpLoad %float %161
        %163 = OpVectorTimesScalar %v4float %156 %162
        %139 = OpFAdd %v4float %131 %163
        %142 = OpIAdd %int %141 %int_1
               OpBranch %138
        %144 = OpLabel
               OpBranch %132
        %132 = OpLabel
        %134 = OpIAdd %int %133 %int_1
               OpBranch %129
        %136 = OpLabel
               OpBranch %125
        %127 = OpLabel
        %164 = OpIEqual %bool %123 %uint_1
               OpSelectionMerge %165 None
               OpBranchConditional %164 %166 %167
        %166 = OpLabel
        %168 = OpFDiv %v2float %42 %116
               OpBranch %169
        %169 = OpLabel
        %170 = OpPhi %v4float %40 %166 %171 %172
        %173 = OpPhi %int %int_n1 %166 %174 %172
        %175 = OpSLessThanEqual %bool %173 %int_1
               OpLoopMerge %176 %172 None
               OpBranchConditional %175 %177 %176
        %177 = OpLabel
               OpBranch %178
        %178 = OpLabel
        %171 = OpPhi %v4float %170 %177 %179 %180
        %181 = OpPhi %int %int_n1 %177 %182 %180
        %183 = OpSLessThanEqual %bool %181 %int_1
               OpLoopMerge %184 %180 None
               OpBranchConditional %183 %180 %184
        %180 = OpLabel
        %185 = OpConvertSToF %float %181
        %186 = OpConvertSToF %float %173
        %187 = OpCompositeConstruct %v2float %185 %186
        %188 = OpFMul %v2float %187 %168
        %189 = OpFAdd %v2float %117 %188
        %190 = OpLoad %type_2d_image %InputTexture
        %191 = OpLoad %type_sampler %LinearSampler
        %192 = OpSampledImage %type_sampled_image %190 %191
        %193 = OpImageSampleExplicitLod %v4float %192 %189 Lod %float_0
        %194 = OpIAdd %int %173 %int_1
        %195 = OpIMul %int %194 %int_3
        %182 = OpIAdd %int %181 %int_1
        %196 = OpIAdd %int %195 %182
        %197 = OpAccessChain %_ptr_Function_float %SharpenKernel %196
        %198 = OpLoad %float %197
        %199 = OpVectorTimesScalar %v4float %193 %198
        %179 = OpFAdd %v4float %171 %199
               OpBranch %178
        %184 = OpLabel
               OpBranch %172
        %172 = OpLabel
        %174 = OpIAdd %int %173 %int_1
               OpBranch %169
        %176 = OpLabel
               OpBranch %165
        %167 = OpLabel
        %200 = OpIEqual %bool %123 %uint_2
               OpSelectionMerge %201 None
               OpBranchConditional %200 %202 %203
        %202 = OpLabel
        %204 = OpFDiv %v2float %42 %116
               OpBranch %205
        %205 = OpLabel
        %206 = OpPhi %v4float %40 %202 %207 %208
        %209 = OpPhi %v4float %40 %202 %210 %208
        %211 = OpPhi %int %int_n1 %202 %212 %208
        %213 = OpSLessThanEqual %bool %211 %int_1
               OpLoopMerge %214 %208 None
               OpBranchConditional %213 %215 %214
        %215 = OpLabel
               OpBranch %216
        %216 = OpLabel
        %207 = OpPhi %v4float %206 %215 %217 %218
        %210 = OpPhi %v4float %209 %215 %219 %218
        %220 = OpPhi %int %int_n1 %215 %221 %218
        %222 = OpSLessThanEqual %bool %220 %int_1
               OpLoopMerge %223 %218 None
               OpBranchConditional %222 %218 %223
        %218 = OpLabel
        %224 = OpConvertSToF %float %220
        %225 = OpConvertSToF %float %211
        %226 = OpCompositeConstruct %v2float %224 %225
        %227 = OpFMul %v2float %226 %204
        %228 = OpFAdd %v2float %117 %227
        %229 = OpLoad %type_2d_image %InputTexture
        %230 = OpLoad %type_sampler %LinearSampler
        %231 = OpSampledImage %type_sampled_image %229 %230
        %232 = OpImageSampleExplicitLod %v4float %231 %228 Lod %float_0
        %233 = OpIAdd %int %211 %int_1
        %234 = OpIMul %int %233 %int_3
        %221 = OpIAdd %int %220 %int_1
        %235 = OpIAdd %int %234 %221
        %236 = OpAccessChain %_ptr_Function_float %EdgeKernelX %235
        %237 = OpLoad %float %236
        %238 = OpVectorTimesScalar %v4float %232 %237
        %219 = OpFAdd %v4float %210 %238
        %239 = OpAccessChain %_ptr_Function_float %EdgeKernelY %235
        %240 = OpLoad %float %239
        %241 = OpVectorTimesScalar %v4float %232 %240
        %217 = OpFAdd %v4float %207 %241
               OpBranch %216
        %223 = OpLabel
               OpBranch %208
        %208 = OpLabel
        %212 = OpIAdd %int %211 %int_1
               OpBranch %205
        %214 = OpLabel
        %242 = OpFMul %v4float %209 %209
        %243 = OpFMul %v4float %206 %206
        %244 = OpFAdd %v4float %242 %243
        %245 = OpExtInst %v4float %1 Sqrt %244
               OpBranch %201
        %203 = OpLabel
        %246 = OpIEqual %bool %123 %uint_3
               OpSelectionMerge %247 None
               OpBranchConditional %246 %248 %247
        %248 = OpLabel
        %249 = OpFDiv %v2float %42 %116
               OpBranch %250
        %250 = OpLabel
        %251 = OpPhi %v4float %40 %248 %252 %253
        %254 = OpPhi %int %int_n1 %248 %255 %253
        %256 = OpSLessThanEqual %bool %254 %int_1
               OpLoopMerge %257 %253 None
               OpBranchConditional %256 %258 %257
        %258 = OpLabel
               OpBranch %259
        %259 = OpLabel
        %252 = OpPhi %v4float %251 %258 %260 %261
        %262 = OpPhi %int %int_n1 %258 %263 %261
        %264 = OpSLessThanEqual %bool %262 %int_1
               OpLoopMerge %265 %261 None
               OpBranchConditional %264 %261 %265
        %261 = OpLabel
        %266 = OpConvertSToF %float %262
        %267 = OpConvertSToF %float %254
        %268 = OpCompositeConstruct %v2float %266 %267
        %269 = OpFMul %v2float %268 %249
        %270 = OpFAdd %v2float %117 %269
        %271 = OpLoad %type_2d_image %InputTexture
        %272 = OpLoad %type_sampler %LinearSampler
        %273 = OpSampledImage %type_sampled_image %271 %272
        %274 = OpImageSampleExplicitLod %v4float %273 %270 Lod %float_0
        %275 = OpIAdd %int %254 %int_1
        %276 = OpIMul %int %275 %int_3
        %263 = OpIAdd %int %262 %int_1
        %277 = OpIAdd %int %276 %263
        %278 = OpAccessChain %_ptr_Function_float %EmbossKernel %277
        %279 = OpLoad %float %278
        %280 = OpVectorTimesScalar %v4float %274 %279
        %260 = OpFAdd %v4float %252 %280
               OpBranch %259
        %265 = OpLabel
               OpBranch %253
        %253 = OpLabel
        %255 = OpIAdd %int %254 %int_1
               OpBranch %250
        %257 = OpLabel
        %281 = OpFAdd %v4float %251 %52
               OpBranch %247
        %247 = OpLabel
        %282 = OpPhi %v4float %121 %203 %281 %257
               OpBranch %201
        %201 = OpLabel
        %283 = OpPhi %v4float %245 %214 %282 %247
               OpBranch %165
        %165 = OpLabel
        %284 = OpPhi %v4float %170 %176 %283 %201
               OpBranch %125
        %125 = OpLabel
        %285 = OpPhi %v4float %130 %136 %284 %165
        %286 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_3
        %287 = OpLoad %float %286
        %288 = OpVectorShuffle %v3float %285 %285 0 1 2
        %289 = OpVectorTimesScalar %v3float %288 %287
        %290 = OpVectorShuffle %v3float %289 %91 0 1 2
        %291 = OpFSub %v3float %290 %54
        %292 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_4
        %293 = OpLoad %float %292
        %294 = OpVectorTimesScalar %v3float %291 %293
        %295 = OpFAdd %v3float %294 %54
        %296 = OpCompositeExtract %float %295 2
        %297 = OpCompositeExtract %float %295 1
        %298 = OpCompositeConstruct %v4float %296 %297 %float_n1 %float_0_666666687
        %299 = OpCompositeConstruct %v4float %297 %296 %float_0 %float_n0_333333343
        %300 = OpExtInst %float %1 Step %296 %297
        %301 = OpCompositeConstruct %v4float %300 %300 %300 %300
        %302 = OpExtInst %v4float %1 FMix %298 %299 %301
        %303 = OpCompositeExtract %float %295 0
        %304 = OpCompositeExtract %float %302 0
        %305 = OpCompositeExtract %float %302 1
        %306 = OpCompositeExtract %float %302 3
        %307 = OpCompositeConstruct %v4float %304 %305 %306 %303
        %308 = OpCompositeExtract %float %302 2
        %309 = OpCompositeConstruct %v4float %303 %305 %308 %304
        %310 = OpExtInst %float %1 Step %304 %303
        %311 = OpCompositeConstruct %v4float %310 %310 %310 %310
        %312 = OpExtInst %v4float %1 FMix %307 %309 %311
        %313 = OpCompositeExtract %float %312 0
        %314 = OpCompositeExtract %float %312 3
        %315 = OpCompositeExtract %float %312 1
        %316 = OpExtInst %float %1 NMin %314 %315
        %317 = OpFSub %float %313 %316
        %318 = OpCompositeExtract %float %312 2
        %319 = OpFSub %float %314 %315
        %320 = OpFMul %float %float_6 %317
        %321 = OpFAdd %float %320 %float_1_00000001en10
        %322 = OpFDiv %float %319 %321
        %323 = OpFAdd %float %318 %322
        %324 = OpExtInst %float %1 FAbs %323
        %325 = OpFAdd %float %313 %float_1_00000001en10
        %326 = OpFDiv %float %317 %325
        %327 = OpCompositeConstruct %v3float %324 %326 %313
        %328 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_5
        %329 = OpLoad %float %328
        %330 = OpFMul %float %326 %329
        %331 = OpVectorShuffle %v3float %327 %327 0 0 0
        %332 = OpFAdd %v3float %331 %89
        %333 = OpExtInst %v3float %1 Fract %332
        %334 = OpVectorTimesScalar %v3float %333 %float_6
        %335 = OpFSub %v3float %334 %90
        %336 = OpExtInst %v3float %1 FAbs %335
        %337 = OpFSub %v3float %336 %65
        %338 = OpExtInst %v3float %1 FClamp %337 %64 %65
        %339 = OpCompositeConstruct %v3float %330 %330 %330
        %340 = OpExtInst %v3float %1 FMix %65 %338 %339
        %341 = OpVectorTimesScalar %v3float %340 %313
        %342 = OpVectorShuffle %v3float %341 %91 0 1 2
        %343 = OpExtInst %v3float %1 FAbs %342
        %344 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_6
        %345 = OpLoad %float %344
        %346 = OpCompositeConstruct %v3float %345 %345 %345
        %347 = OpExtInst %v3float %1 Pow %343 %346
        %348 = OpVectorShuffle %v4float %285 %347 4 5 6 3
        %349 = OpExtInst %v4float %1 FClamp %348 %40 %57
        %350 = OpLoad %type_2d_image_0 %OutputTexture
               OpImageWrite %350 %110 %349 None
               OpBranch %94
         %94 = OpLabel
               OpReturn
               OpFunctionEnd

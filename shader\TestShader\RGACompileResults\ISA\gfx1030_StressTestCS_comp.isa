_amdgpu_cs_main:
	s_getpc_b64 s[4:5]                                         // 000000000000: BE841F00
	s_mov_b32 s0, s1                                           // 000000000004: BE800301
	s_mov_b32 s1, s5                                           // 000000000008: BE810305
	v_lshl_add_u32 v9, s2, 6, v0                               // 00000000000C: D7460009 04010C02
	s_load_dwordx4 s[16:19], s[0:1], null                      // 000000000014: F4080400 FA000000
	s_mov_b64 s[4:5], exec                                     // 00000000001C: BE84047E
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s3, s[16:19], 0x4                      // 000000000024: F42000C8 FA000004
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cmpx_gt_u32_e64 s3, v9                                   // 000000000030: D4D4007E 00021203
	s_cbranch_execz _L0                                        // 000000000038: BF880284
	s_buffer_load_dword s4, s[16:19], 0xc                      // 00000000003C: F4200108 FA00000C
	s_load_dwordx4 s[12:15], s[0:1], 0x30                      // 000000000044: F4080300 FA000030
	s_waitcnt lgkmcnt(0)                                       // 00000000004C: BF8CC07F
	s_cmp_lg_u32 s4, 0                                         // 000000000050: BF078004
	s_cbranch_scc0 _L1                                         // 000000000054: BF8400F6
	s_cmp_lg_u32 s4, 1                                         // 000000000058: BF078104
	s_cbranch_scc0 _L2                                         // 00000000005C: BF8400F5
	s_cmp_lg_u32 s4, 2                                         // 000000000060: BF078204
	s_cbranch_scc0 _L3                                         // 000000000064: BF8400F4
	v_mov_b32_e32 v4, 0                                        // 000000000068: 7E080280
	v_mov_b32_e32 v3, 0                                        // 00000000006C: 7E060280
	v_mov_b32_e32 v2, 0                                        // 000000000070: 7E040280
	v_mov_b32_e32 v1, 0                                        // 000000000074: 7E020280
	s_cmp_lg_u32 s4, 3                                         // 000000000078: BF078304
	s_cbranch_scc1 _L4                                         // 00000000007C: BF8500EA
	v_lshlrev_b32_e32 v1, 4, v9                                // 000000000080: 34021284
	v_mov_b32_e32 v6, 0                                        // 000000000084: 7E0C0280
	v_mov_b32_e32 v7, 0                                        // 000000000088: 7E0E0280
	v_mov_b32_e32 v8, 0                                        // 00000000008C: 7E100280
	v_mov_b32_e32 v10, 0                                       // 000000000090: 7E140280
	buffer_load_dwordx4 v[1:4], v1, s[16:19], 0 offen          // 000000000094: E0381000 80040101
	s_buffer_load_dword s24, s[16:19], 0x8                     // 00000000009C: F4200608 FA000008
	s_waitcnt lgkmcnt(0)                                       // 0000000000A4: BF8CC07F
	s_cmp_eq_u32 s24, 0                                        // 0000000000A8: BF068018
	s_cbranch_scc1 _L5                                         // 0000000000AC: BF850098
	v_cvt_f32_u32_e32 v5, s3                                   // 0000000000B0: 7E0A0C03
	s_sub_i32 s4, 0, s3                                        // 0000000000B4: 81840380
	s_buffer_load_dword s25, s[16:19], 0x10                    // 0000000000B8: F4200648 FA000010
	s_waitcnt vmcnt(0)                                         // 0000000000C0: BF8C3F70
	v_mul_f32_e32 v8, v3, v3                                   // 0000000000C4: 10100703
	s_cmp_eq_u32 s24, 1                                        // 0000000000C8: BF068118
	v_rcp_iflag_f32_e32 v5, v5                                 // 0000000000CC: 7E0A5705
	s_mov_b32 s26, 1                                           // 0000000000D0: BE9A0381
	v_mul_f32_e32 v5, 0x4f7ffffe, v5                           // 0000000000D4: 100A0AFF 4F7FFFFE
	v_cvt_u32_f32_e32 v5, v5                                   // 0000000000DC: 7E0A0F05
	v_mul_lo_u32 v6, s4, v5                                    // 0000000000E0: D5690006 00020A04
	s_clause 0x1                                               // 0000000000E8: BFA10001
	s_load_dwordx8 s[4:11], s[0:1], null                       // 0000000000EC: F40C0100 FA000000
	s_load_dwordx4 s[20:23], s[0:1], 0x20                      // 0000000000F4: F4080500 FA000020
	v_mul_hi_u32 v6, v5, v6                                    // 0000000000FC: D56A0006 00020D05
	v_add_nc_u32_e32 v11, v5, v6                               // 000000000104: 4A160D05
	v_mul_hi_u32 v5, v9, v11                                   // 000000000108: D56A0005 00021709
	v_mul_lo_u32 v5, v5, s3                                    // 000000000110: D5690005 00000705
	v_sub_nc_u32_e32 v5, v9, v5                                // 000000000118: 4C0A0B09
	v_subrev_nc_u32_e32 v6, s3, v5                             // 00000000011C: 4E0C0A03
	v_cmp_le_u32_e32 vcc_lo, s3, v5                            // 000000000120: 7D860A03
	v_cndmask_b32_e32 v5, v5, v6, vcc_lo                       // 000000000124: 020A0D05
	v_subrev_nc_u32_e32 v6, s3, v5                             // 000000000128: 4E0C0A03
	v_cmp_le_u32_e32 vcc_lo, s3, v5                            // 00000000012C: 7D860A03
	v_cndmask_b32_e32 v5, v5, v6, vcc_lo                       // 000000000130: 020A0D05
	v_mul_f32_e32 v6, 0xbe13bb63, v4                           // 000000000134: 100C08FF BE13BB63
	v_lshlrev_b32_e32 v5, 4, v5                                // 00000000013C: 340A0A84
	v_exp_f32_e32 v6, v6                                       // 000000000140: 7E0C4B06
	buffer_load_dwordx4 v[12:15], v5, s[16:19], 0 offen        // 000000000144: E0381000 80040C05
	s_waitcnt lgkmcnt(0)                                       // 00000000014C: BF8CC07F
	v_add_f32_e32 v5, s25, v1                                  // 000000000150: 060A0219
	v_add_f32_e32 v7, s25, v2                                  // 000000000154: 060E0419
	v_mul_f32_e32 v5, 0.15915494, v5                           // 000000000158: 100A0AF8
	v_mul_f32_e32 v7, 0.15915494, v7                           // 00000000015C: 100E0EF8
	v_mul_f32_e32 v6, v8, v6                                   // 000000000160: 100C0D08
	v_mov_b32_e32 v8, 0                                        // 000000000164: 7E100280
	v_sin_f32_e32 v5, v5                                       // 000000000168: 7E0A6B05
	v_cos_f32_e32 v7, v7                                       // 00000000016C: 7E0E6D07
	v_fmac_f32_e32 v6, v5, v7                                  // 000000000170: 560C0F05
	v_fma_f32 v7, v6, 0.5, 0.5                                 // 000000000174: D54B0007 03C1E106
	image_sample_lz v[16:19], v[7:8], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000017C: F09C0F08 00A11007
	s_waitcnt vmcnt(0)                                         // 000000000184: BF8C3F70
	v_add_f32_e32 v5, v16, v12                                 // 000000000188: 060A1910
	v_add_f32_e32 v10, v17, v13                                // 00000000018C: 06141B11
	v_add_f32_e32 v12, v18, v14                                // 000000000190: 06181D12
	v_add_f32_e32 v13, v19, v15                                // 000000000194: 061A1F13
	v_fmac_f32_e32 v6, 0x3dcccccd, v5                          // 000000000198: 560C0AFF 3DCCCCCD
	v_fma_f32 v5, 0x3dcccccd, v10, -v2                         // 0000000001A0: D54B0005 840A14FF 3DCCCCCD
	v_mul_f32_e32 v8, 0x3dcccccd, v12                          // 0000000001AC: 101018FF 3DCCCCCD
	v_fma_f32 v12, 0x3dcccccd, v12, -v3                        // 0000000001B4: D54B000C 840E18FF 3DCCCCCD
	v_fma_f32 v15, 0x3dcccccd, v13, -v4                        // 0000000001C0: D54B000F 84121AFF 3DCCCCCD
	v_sub_f32_e32 v14, v6, v1                                  // 0000000001CC: 081C0306
	v_mul_f32_e32 v7, 0x3dcccccd, v10                          // 0000000001D0: 100E14FF 3DCCCCCD
	v_mul_f32_e32 v10, 0x3dcccccd, v13                         // 0000000001D8: 10141AFF 3DCCCCCD
	v_fmamk_f32 v2, v5, 0x3dcccccd, v2                         // 0000000001E0: 58040505 3DCCCCCD
	v_fmamk_f32 v3, v12, 0x3dcccccd, v3                        // 0000000001E8: 5806070C 3DCCCCCD
	v_fmamk_f32 v1, v14, 0x3dcccccd, v1                        // 0000000001F0: 5802030E 3DCCCCCD
	v_fmac_f32_e32 v4, 0x3dcccccd, v15                         // 0000000001F8: 56081EFF 3DCCCCCD
	s_cbranch_scc1 _L5                                         // 000000000200: BF850043
	v_cvt_f32_u32_e32 v5, s24                                  // 000000000204: 7E0A0C18
	v_rcp_iflag_f32_e32 v12, v5                                // 000000000208: 7E185705
	v_add_nc_u32_e32 v5, 17, v9                                // 00000000020C: 4A0A1291
_L6:
	v_mul_hi_u32 v13, v11, v5                                  // 000000000210: D56A000D 00020B0B
	v_add_f32_e32 v14, s25, v1                                 // 000000000218: 061C0219
	v_mul_f32_e32 v15, 0xbe13bb63, v4                          // 00000000021C: 101E08FF BE13BB63
	v_add_f32_e32 v16, s25, v2                                 // 000000000224: 06200419
	v_mul_f32_e32 v18, v3, v3                                  // 000000000228: 10240703
	v_cvt_f32_u32_e32 v17, s26                                 // 00000000022C: 7E220C1A
	v_mul_f32_e32 v14, 0.15915494, v14                         // 000000000230: 101C1CF8
	v_exp_f32_e32 v15, v15                                     // 000000000234: 7E1E4B0F
	v_mul_lo_u32 v19, s3, v13                                  // 000000000238: D5690013 00021A03
	v_not_b32_e32 v13, v13                                     // 000000000240: 7E1A6F0D
	v_mul_f32_e32 v16, 0.15915494, v16                         // 000000000244: 102020F8
	v_sin_f32_e32 v20, v14                                     // 000000000248: 7E286B0E
	v_mul_f32_e32 v17, v17, v12                                // 00000000024C: 10221911
	s_add_i32 s26, s26, 1                                      // 000000000250: 811A811A
	v_mad_u64_u32 v[13:14], null, s3, v13, v[5:6]              // 000000000254: D5767D0D 04161A03
	v_sub_nc_u32_e32 v14, v5, v19                              // 00000000025C: 4C1C2705
	v_cos_f32_e32 v16, v16                                     // 000000000260: 7E206D10
	v_mul_f32_e32 v21, v18, v15                                // 000000000264: 102A1F12
	v_add_nc_u32_e32 v5, 17, v5                                // 000000000268: 4A0A0A91
	s_cmp_lt_u32 s26, s24                                      // 00000000026C: BF0A181A
	v_cmp_le_u32_e32 vcc_lo, s3, v14                           // 000000000270: 7D861C03
	v_cndmask_b32_e32 v13, v14, v13, vcc_lo                    // 000000000274: 021A1B0E
	v_fmac_f32_e32 v21, v20, v16                               // 000000000278: 562A2114
	v_subrev_nc_u32_e32 v14, s3, v13                           // 00000000027C: 4E1C1A03
	v_cmp_le_u32_e32 vcc_lo, s3, v13                           // 000000000280: 7D861A03
	v_fma_f32 v16, v21, 0.5, 0.5                               // 000000000284: D54B0010 03C1E115
	v_add_f32_e32 v6, v21, v6                                  // 00000000028C: 060C0D15
	v_cndmask_b32_e32 v13, v13, v14, vcc_lo                    // 000000000290: 021A1D0D
	v_lshlrev_b32_e32 v18, 4, v13                              // 000000000294: 34241A84
	image_sample_lz v[13:16], v[16:17], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000298: F09C0F08 00A10D10
	buffer_load_dwordx4 v[17:20], v18, s[16:19], 0 offen       // 0000000002A0: E0381000 80041112
	s_waitcnt vmcnt(0)                                         // 0000000002A8: BF8C3F70
	v_add_f32_e32 v13, v13, v17                                // 0000000002AC: 061A230D
	v_add_f32_e32 v14, v14, v18                                // 0000000002B0: 061C250E
	v_add_f32_e32 v15, v15, v19                                // 0000000002B4: 061E270F
	v_add_f32_e32 v16, v16, v20                                // 0000000002B8: 06202910
	v_fmac_f32_e32 v6, 0x3dcccccd, v13                         // 0000000002BC: 560C1AFF 3DCCCCCD
	v_fmac_f32_e32 v7, 0x3dcccccd, v14                         // 0000000002C4: 560E1CFF 3DCCCCCD
	v_fmac_f32_e32 v8, 0x3dcccccd, v15                         // 0000000002CC: 56101EFF 3DCCCCCD
	v_fmac_f32_e32 v10, 0x3dcccccd, v16                        // 0000000002D4: 561420FF 3DCCCCCD
	v_sub_f32_e32 v13, v6, v1                                  // 0000000002DC: 081A0306
	v_sub_f32_e32 v14, v7, v2                                  // 0000000002E0: 081C0507
	v_sub_f32_e32 v15, v8, v3                                  // 0000000002E4: 081E0708
	v_sub_f32_e32 v16, v10, v4                                 // 0000000002E8: 0820090A
	v_fmac_f32_e32 v1, 0x3dcccccd, v13                         // 0000000002EC: 56021AFF 3DCCCCCD
	v_fmac_f32_e32 v2, 0x3dcccccd, v14                         // 0000000002F4: 56041CFF 3DCCCCCD
	v_fmac_f32_e32 v3, 0x3dcccccd, v15                         // 0000000002FC: 56061EFF 3DCCCCCD
	v_fmac_f32_e32 v4, 0x3dcccccd, v16                         // 000000000304: 560820FF 3DCCCCCD
	s_cbranch_scc1 _L6                                         // 00000000030C: BF85FFC0
_L5:
	v_mul_f32_e32 v5, v7, v7                                   // 000000000310: 100A0F07
	s_waitcnt vmcnt(0)                                         // 000000000314: BF8C3F70
	v_mul_f32_e32 v2, v2, v2                                   // 000000000318: 10040502
	s_mov_b32 s4, 0xbc46c6a5                                   // 00000000031C: BE8403FF BC46C6A5
	v_fmac_f32_e32 v5, v6, v6                                  // 000000000324: 560A0D06
	v_fmac_f32_e32 v2, v1, v1                                  // 000000000328: 56040301
	v_fmac_f32_e32 v5, v8, v8                                  // 00000000032C: 560A1108
	v_fmac_f32_e32 v2, v3, v3                                  // 000000000330: 56040703
	v_fmac_f32_e32 v5, v10, v10                                // 000000000334: 560A150A
	v_fmac_f32_e32 v2, v4, v4                                  // 000000000338: 56040904
	v_rsq_f32_e32 v1, v5                                       // 00000000033C: 7E025D05
	v_sqrt_f32_e32 v5, v2                                      // 000000000340: 7E0A6702
	v_mul_legacy_f32_e32 v2, v10, v1                           // 000000000344: 0E04030A
	v_mul_legacy_f32_e32 v8, v8, v1                            // 000000000348: 0E100308
	v_mul_legacy_f32_e32 v6, v6, v1                            // 00000000034C: 0E0C0306
	v_mul_legacy_f32_e32 v7, v7, v1                            // 000000000350: 0E0E0307
	v_mul_f32_e32 v4, v2, v5                                   // 000000000354: 10080B02
	v_mul_f32_e32 v11, v8, v5                                  // 000000000358: 10160B08
	v_mul_f32_e32 v3, 0x40a00000, v4                           // 00000000035C: 100608FF 40A00000
	v_mul_f32_e32 v11, 0x3f4bb7e4, v11                         // 000000000364: 101616FF 3F4BB7E4
	v_max_f32_e64 v2, |v3|, 1.0                                // 00000000036C: D5100102 0001E503
	v_min_f32_e64 v10, |v3|, 1.0                               // 000000000374: D50F010A 0001E503
	v_cos_f32_e32 v1, v11                                      // 00000000037C: 7E026D0B
	v_cmp_gt_f32_e64 vcc_lo, |v3|, 1.0                         // 000000000380: D404016A 0001E503
	v_sin_f32_e32 v11, v11                                     // 000000000388: 7E166B0B
	v_rcp_f32_e32 v2, v2                                       // 00000000038C: 7E045502
	v_mul_f32_e32 v2, v10, v2                                  // 000000000390: 1004050A
	v_mul_f32_e32 v10, v2, v2                                  // 000000000394: 10140502
	v_mul_f32_e32 v12, v10, v2                                 // 000000000398: 1018050A
	v_mul_f32_e32 v2, 0x3f7ffea5, v2                           // 00000000039C: 100404FF 3F7FFEA5
	v_fmaak_f32 v13, s4, v10, 0x3d5be101                       // 0000000003A4: 5A1A1404 3D5BE101
	v_mul_f32_e32 v14, v12, v10                                // 0000000003AC: 101C150C
	v_fmamk_f32 v2, v12, 0xbeaa5476, v2                        // 0000000003B0: 5804050C BEAA5476
	v_fmaak_f32 v12, v13, v10, 0xbdf0555d                      // 0000000003B8: 5A18150D BDF0555D
	v_mul_f32_e32 v10, v14, v10                                // 0000000003C0: 1014150E
	v_fmamk_f32 v13, v14, 0x3e468bc1, v2                       // 0000000003C4: 581A050E 3E468BC1
	v_mul_f32_e32 v2, v6, v5                                   // 0000000003CC: 10040B06
	v_mul_f32_e32 v14, v7, v5                                  // 0000000003D0: 101C0B07
	v_fmac_f32_e32 v13, v10, v12                               // 0000000003D4: 561A190A
	v_mul_f32_e32 v2, 0x3fcbb7e4, v2                           // 0000000003D8: 100404FF 3FCBB7E4
	v_mul_f32_e32 v10, 0x3fcbb7e4, v14                         // 0000000003E0: 10141CFF 3FCBB7E4
	v_rcp_f32_e32 v12, v1                                      // 0000000003E8: 7E185501
	v_fmaak_f32 v14, -2.0, v13, 0x3fc90fdb                     // 0000000003EC: 5A1C1AF5 3FC90FDB
	v_sin_f32_e32 v1, v2                                       // 0000000003F4: 7E026B02
	v_cos_f32_e32 v2, v10                                      // 0000000003F8: 7E046D0A
	v_cndmask_b32_e32 v10, 0, v14, vcc_lo                      // 0000000003FC: 02141C80
	v_cmp_ge_f32_e32 vcc_lo, 0, v3                             // 000000000400: 7C0C0680
	v_add_f32_e32 v10, v10, v13                                // 000000000404: 06141B0A
	v_cndmask_b32_e32 v14, 1.0, v3, vcc_lo                     // 000000000408: 021C06F2
	v_mul_f32_e32 v3, v11, v12                                 // 00000000040C: 1006190B
	v_fmac_f32_e32 v1, v6, v5                                  // 000000000410: 56020B06
	v_fmac_f32_e32 v2, v7, v5                                  // 000000000414: 56040B07
	v_cmp_le_f32_e32 vcc_lo, 0, v14                            // 000000000418: 7C061C80
	v_fmac_f32_e32 v3, v8, v5                                  // 00000000041C: 56060B08
	v_cndmask_b32_e32 v11, -1.0, v14, vcc_lo                   // 000000000420: 02161CF3
	v_fmac_f32_e32 v4, v10, v11                                // 000000000424: 5608170A
_L4:
	s_cbranch_execz _L3                                        // 000000000428: BF880003
	s_branch _L7                                               // 00000000042C: BF820099
_L1:
	s_branch _L8                                               // 000000000430: BF8200F6
_L2:
	s_branch _L9                                               // 000000000434: BF820098
_L3:
	s_load_dwordx8 s[4:11], s[0:1], null                       // 000000000438: F40C0100 FA000000
	s_buffer_load_dword s26, s[16:19], 0x8                     // 000000000440: F4200688 FA000008
	v_mov_b32_e32 v6, 0                                        // 000000000448: 7E0C0280
	v_mov_b32_e32 v4, 0                                        // 00000000044C: 7E080280
	v_mov_b32_e32 v3, 0                                        // 000000000450: 7E060280
	v_mov_b32_e32 v2, 0                                        // 000000000454: 7E040280
	s_mov_b32 s27, 0                                           // 000000000458: BE9B0380
	s_waitcnt lgkmcnt(0)                                       // 00000000045C: BF8CC07F
	v_alignbit_b32 v1, s6, s5, 30                              // 000000000460: D54E0001 02780A06
	s_bfe_u32 s21, s7, 0x4000c                                 // 000000000468: 9395FF07 0004000C
	v_readfirstlane_b32 s20, v1                                // 000000000470: 7E280501
	s_and_b32 s20, s20, 0x3fff                                 // 000000000474: 8714FF14 00003FFF
	s_add_i32 s20, s20, 1                                      // 00000000047C: 81148114
	s_lshr_b32 s20, s20, s21                                   // 000000000480: 90141514
	s_max_u32 s24, s20, 1                                      // 000000000484: 84988114
	s_bfe_u32 s20, s6, 0xe000e                                 // 000000000488: 9394FF06 000E000E
	v_cvt_f32_u32_e32 v1, s24                                  // 000000000490: 7E020C18
	s_add_i32 s20, s20, 1                                      // 000000000494: 81148114
	s_lshr_b32 s20, s20, s21                                   // 000000000498: 90141514
	s_max_u32 s25, s20, 1                                      // 00000000049C: 84998114
	v_rcp_iflag_f32_e32 v5, v1                                 // 0000000004A0: 7E0A5701
	v_mov_b32_e32 v1, 0                                        // 0000000004A4: 7E020280
	s_cmp_eq_u32 s26, 0                                        // 0000000004A8: BF06801A
	s_cbranch_scc1 _L10                                        // 0000000004AC: BF850051
	s_buffer_load_dword s28, s[16:19], 0x10                    // 0000000004B0: F4200708 FA000010
	s_load_dwordx4 s[20:23], s[0:1], 0x20                      // 0000000004B8: F4080500 FA000020
	v_cvt_f32_u32_e32 v1, s25                                  // 0000000004C0: 7E020C19
	v_cvt_f32_u32_e32 v2, s3                                   // 0000000004C4: 7E040C03
	v_mov_b32_e32 v8, 0                                        // 0000000004C8: 7E100280
	v_mov_b32_e32 v10, 1.0                                     // 0000000004CC: 7E1402F2
	v_mov_b32_e32 v11, 2.0                                     // 0000000004D0: 7E1602F4
	v_rcp_iflag_f32_e32 v6, v1                                 // 0000000004D4: 7E0C5701
	v_rcp_iflag_f32_e32 v7, v2                                 // 0000000004D8: 7E0E5702
	v_mov_b32_e32 v12, 0x40400000                              // 0000000004DC: 7E1802FF 40400000
	v_mov_b32_e32 v1, 0                                        // 0000000004E4: 7E020280
	v_mov_b32_e32 v2, 0                                        // 0000000004E8: 7E040280
	v_mov_b32_e32 v3, 0                                        // 0000000004EC: 7E060280
	v_mov_b32_e32 v4, 0                                        // 0000000004F0: 7E080280
_L11:
	v_cvt_f32_u32_e32 v13, s27                                 // 0000000004F4: 7E1A0C1B
	v_add_nc_u32_e32 v14, s27, v9                              // 0000000004F8: 4A1C121B
	s_add_i32 s27, s27, 1                                      // 0000000004FC: 811B811B
	s_cmp_lt_u32 s27, s26                                      // 000000000500: BF0A1A1B
	s_waitcnt lgkmcnt(0)                                       // 000000000504: BF8CC07F
	v_add_f32_e32 v13, s28, v13                                // 000000000508: 061A1A1C
	v_cvt_f32_u32_e32 v14, v14                                 // 00000000050C: 7E1C0D0E
	v_mul_f32_e32 v13, 0.15915494, v13                         // 000000000510: 101A1AF8
	v_mul_f32_e32 v30, v14, v7                                 // 000000000514: 103C0F0E
	v_sin_f32_e32 v13, v13                                     // 000000000518: 7E1A6B0D
	v_add_f32_e32 v17, v30, v30                                // 00000000051C: 06223D1E
	v_mul_f32_e32 v22, 4.0, v30                                // 000000000520: 102C3CF6
	v_mul_f32_e32 v23, 0x41000000, v30                         // 000000000524: 102E3CFF 41000000
	v_fma_f32 v31, v13, 0.5, 0.5                               // 00000000052C: D54B001F 03C1E10D
	image_sample_lz v[13:16], v[30:31], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000534: F09C0F08 00A10D1E
	v_add_f32_e32 v18, v31, v31                                // 00000000053C: 06243F1F
	v_mul_f32_e32 v25, 4.0, v31                                // 000000000540: 10323EF6
	image_sample_l  v[17:20], [v17, v18, v10], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000544: F0900F0A 00A11111 00000A12
	s_waitcnt vmcnt(1)                                         // 000000000550: BF8C3F71
	v_add_f32_e32 v26, v13, v1                                 // 000000000554: 0634030D
	v_mul_f32_e32 v13, 0x41000000, v31                         // 000000000558: 101A3EFF 41000000
	v_add_f32_e32 v27, v14, v2                                 // 000000000560: 0636050E
	v_add_f32_e32 v28, v15, v3                                 // 000000000564: 0638070F
	v_add_f32_e32 v29, v16, v4                                 // 000000000568: 063A0910
	s_clause 0x2                                               // 00000000056C: BFA10002
	image_sample_l  v[1:4], [v22, v25, v11], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000570: F0900F0A 00A10116 00000B19
	image_sample_l  v[13:16], [v23, v13, v12], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000057C: F0900F0A 00A10D17 00000C0D
	image_sample_d  v[21:24], [v5, v8, v8, v6, v30, v31], s[4:11], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000588: F0880F0C 00A11505 1E060808 0000001F
	s_waitcnt vmcnt(3)                                         // 000000000598: BF8C3F73
	v_add_f32_e32 v17, v26, v17                                // 00000000059C: 0622231A
	v_add_f32_e32 v18, v27, v18                                // 0000000005A0: 0624251B
	v_add_f32_e32 v19, v28, v19                                // 0000000005A4: 0626271C
	v_add_f32_e32 v20, v29, v20                                // 0000000005A8: 0628291D
	s_waitcnt vmcnt(2)                                         // 0000000005AC: BF8C3F72
	v_add_f32_e32 v1, v17, v1                                  // 0000000005B0: 06020311
	v_add_f32_e32 v2, v18, v2                                  // 0000000005B4: 06040512
	v_add_f32_e32 v3, v19, v3                                  // 0000000005B8: 06060713
	v_add_f32_e32 v4, v20, v4                                  // 0000000005BC: 06080914
	s_waitcnt vmcnt(1)                                         // 0000000005C0: BF8C3F71
	v_add_f32_e32 v1, v1, v13                                  // 0000000005C4: 06021B01
	v_add_f32_e32 v2, v2, v14                                  // 0000000005C8: 06041D02
	v_add_f32_e32 v3, v3, v15                                  // 0000000005CC: 06061F03
	v_add_f32_e32 v4, v4, v16                                  // 0000000005D0: 06082104
	s_waitcnt vmcnt(0)                                         // 0000000005D4: BF8C3F70
	v_add_f32_e32 v1, v1, v21                                  // 0000000005D8: 06022B01
	v_add_f32_e32 v2, v2, v22                                  // 0000000005DC: 06042D02
	v_add_f32_e32 v3, v3, v23                                  // 0000000005E0: 06062F03
	v_add_f32_e32 v4, v4, v24                                  // 0000000005E4: 06083104
	s_cbranch_scc1 _L11                                        // 0000000005E8: BF85FFC2
	s_mul_i32 s26, s26, 5                                      // 0000000005EC: 931A851A
	v_cvt_f32_u32_e32 v6, s26                                  // 0000000005F0: 7E0C0C1A
_L10:
	v_mul_f32_e32 v5, 0x4f7ffffe, v5                           // 0000000005F4: 100A0AFF 4F7FFFFE
	s_sub_i32 s4, 0, s24                                       // 0000000005FC: 81841880
	v_cvt_u32_f32_e32 v5, v5                                   // 000000000600: 7E0A0F05
	v_mul_lo_u32 v7, s4, v5                                    // 000000000604: D5690007 00020A04
	s_mov_b64 s[4:5], exec                                     // 00000000060C: BE84047E
	v_mul_hi_u32 v7, v5, v7                                    // 000000000610: D56A0007 00020F05
	v_add_nc_u32_e32 v5, v5, v7                                // 000000000618: 4A0A0F05
	v_mul_hi_u32 v5, v9, v5                                    // 00000000061C: D56A0005 00020B09
	v_mul_lo_u32 v7, v5, s24                                   // 000000000624: D5690007 00003105
	v_add_nc_u32_e32 v8, 1, v5                                 // 00000000062C: 4A100A81
	v_sub_nc_u32_e32 v7, v9, v7                                // 000000000630: 4C0E0F09
	v_subrev_nc_u32_e32 v10, s24, v7                           // 000000000634: 4E140E18
	v_cmp_le_u32_e32 vcc_lo, s24, v7                           // 000000000638: 7D860E18
	v_cndmask_b32_e32 v5, v5, v8, vcc_lo                       // 00000000063C: 020A1105
	v_cndmask_b32_e32 v7, v7, v10, vcc_lo                      // 000000000640: 020E1507
	v_add_nc_u32_e32 v8, 1, v5                                 // 000000000644: 4A100A81
	v_cmp_le_u32_e32 vcc_lo, s24, v7                           // 000000000648: 7D860E18
	v_cndmask_b32_e32 v8, v5, v8, vcc_lo                       // 00000000064C: 02101105
	v_cmpx_gt_u32_e64 s25, v8                                  // 000000000650: D4D4007E 00021019
	s_cbranch_execz _L12                                       // 000000000658: BF88000D
	s_load_dwordx8 s[36:43], s[0:1], 0x40                      // 00000000065C: F40C0900 FA000040
	v_rcp_f32_e32 v6, v6                                       // 000000000664: 7E0C5506
	v_mul_lo_u32 v7, v8, s24                                   // 000000000668: D5690007 00003108
	v_sub_nc_u32_e32 v7, v9, v7                                // 000000000670: 4C0E0F09
	v_mul_f32_e32 v10, v6, v1                                  // 000000000674: 10140306
	v_mul_f32_e32 v11, v6, v2                                  // 000000000678: 10160506
	v_mul_f32_e32 v12, v6, v3                                  // 00000000067C: 10180706
	v_mul_f32_e32 v13, v6, v4                                  // 000000000680: 101A0906
	s_waitcnt lgkmcnt(0)                                       // 000000000684: BF8CC07F
	image_store v[10:13], v[7:8], s[36:43] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000000688: F0201F08 00090A07
_L12:
	s_or_b64 exec, exec, s[4:5]                                // 000000000690: 88FE047E
_L7:
	s_cbranch_execnz _L13                                      // 000000000694: BF89005C
_L9:
	s_buffer_load_dword s0, s[16:19], 0x8                      // 000000000698: F4200008 FA000008
	v_mov_b32_e32 v4, 0                                        // 0000000006A0: 7E080280
	v_mov_b32_e32 v3, 0                                        // 0000000006A4: 7E060280
	v_mov_b32_e32 v2, 0                                        // 0000000006A8: 7E040280
	v_mov_b32_e32 v1, 0                                        // 0000000006AC: 7E020280
	s_mov_b32 s1, 0                                            // 0000000006B0: BE810380
	s_waitcnt lgkmcnt(0)                                       // 0000000006B4: BF8CC07F
	s_cmp_eq_u32 s0, 0                                         // 0000000006B8: BF068000
	s_cbranch_scc1 _L13                                        // 0000000006BC: BF850052
	v_cvt_f32_u32_e32 v1, s3                                   // 0000000006C0: 7E020C03
	s_sub_i32 s4, 0, s3                                        // 0000000006C4: 81840380
	v_mul_lo_u32 v0, 0x19660d, v0                              // 0000000006C8: D5690000 000200FF 0019660D
	s_mul_i32 s2, s2, 0x6598340                                // 0000000006D4: 9302FF02 06598340
	v_rcp_iflag_f32_e32 v1, v1                                 // 0000000006DC: 7E025701
	v_add3_u32 v0, s2, v0, 0x3c6ef35f                          // 0000000006E0: D76D0000 03FE0002 3C6EF35F
	s_mov_b32 s2, 0x41c64e6d                                   // 0000000006EC: BE8203FF 41C64E6D
	v_mul_f32_e32 v1, 0x4f7ffffe, v1                           // 0000000006F4: 100202FF 4F7FFFFE
	v_cvt_u32_f32_e32 v4, v1                                   // 0000000006FC: 7E080F01
	v_mul_lo_u32 v1, s4, v4                                    // 000000000700: D5690001 00020804
	s_buffer_load_dword s4, s[16:19], 0x10                     // 000000000708: F4200108 FA000010
	v_mul_hi_u32 v5, v4, v1                                    // 000000000710: D56A0005 00020304
	v_mov_b32_e32 v1, 0                                        // 000000000718: 7E020280
	v_mov_b32_e32 v2, v1                                       // 00000000071C: 7E040301
	v_mov_b32_e32 v3, v1                                       // 000000000720: 7E060301
	v_add_nc_u32_e32 v5, v4, v5                                // 000000000724: 4A0A0B04
	v_mov_b32_e32 v4, v1                                       // 000000000728: 7E080301
_L14:
	v_mul_hi_u32 v6, v5, v0                                    // 00000000072C: D56A0006 00020105
	v_mul_lo_u32 v8, s3, v6                                    // 000000000734: D5690008 00020C03
	v_not_b32_e32 v6, v6                                       // 00000000073C: 7E0C6F06
	v_mad_u64_u32 v[6:7], null, s3, v6, v[0:1]                 // 000000000740: D5767D06 04020C03
	v_sub_nc_u32_e32 v7, v0, v8                                // 000000000748: 4C0E1100
	v_add_nc_u32_e32 v0, 1, v0                                 // 00000000074C: 4A000081
	v_cmp_le_u32_e32 vcc_lo, s3, v7                            // 000000000750: 7D860E03
	v_cndmask_b32_e32 v6, v7, v6, vcc_lo                       // 000000000754: 020C0D07
	v_subrev_nc_u32_e32 v7, s3, v6                             // 000000000758: 4E0E0C03
	v_cmp_le_u32_e32 vcc_lo, s3, v6                            // 00000000075C: 7D860C03
	v_cndmask_b32_e32 v6, v6, v7, vcc_lo                       // 000000000760: 020C0F06
	v_lshlrev_b32_e32 v7, 4, v6                                // 000000000764: 340E0C84
	buffer_load_dwordx4 v[10:13], v7, s[16:19], 0 offen        // 000000000768: E0381000 80040A07
	v_mad_u64_u32 v[6:7], null, v6, s2, 0x3039                 // 000000000770: D5767D06 03FC0506 00003039
	v_cvt_f32_u32_e32 v7, s1                                   // 00000000077C: 7E0E0C01
	s_add_i32 s1, s1, 1                                        // 000000000780: 81018101
	s_cmp_ge_u32 s1, s0                                        // 000000000784: BF090001
	s_waitcnt lgkmcnt(0)                                       // 000000000788: BF8CC07F
	v_add_f32_e32 v7, s4, v7                                   // 00000000078C: 060E0E04
	v_mul_hi_u32 v8, v6, v5                                    // 000000000790: D56A0008 00020B06
	v_mul_f32_e32 v7, 0.15915494, v7                           // 000000000798: 100E0EF8
	v_mul_lo_u32 v8, v8, s3                                    // 00000000079C: D5690008 00000708
	v_sin_f32_e32 v7, v7                                       // 0000000007A4: 7E0E6B07
	v_sub_nc_u32_e32 v6, v6, v8                                // 0000000007A8: 4C0C1106
	v_subrev_nc_u32_e32 v8, s3, v6                             // 0000000007AC: 4E100C03
	v_cmp_le_u32_e32 vcc_lo, s3, v6                            // 0000000007B0: 7D860C03
	v_cndmask_b32_e32 v6, v6, v8, vcc_lo                       // 0000000007B4: 020C1106
	v_subrev_nc_u32_e32 v8, s3, v6                             // 0000000007B8: 4E100C03
	v_cmp_le_u32_e32 vcc_lo, s3, v6                            // 0000000007BC: 7D860C03
	v_cndmask_b32_e32 v6, v6, v8, vcc_lo                       // 0000000007C0: 020C1106
	v_lshlrev_b32_e32 v6, 4, v6                                // 0000000007C4: 340C0C84
	s_waitcnt vmcnt(0)                                         // 0000000007C8: BF8C3F70
	v_fmac_f32_e32 v1, v7, v10                                 // 0000000007CC: 56021507
	v_fmac_f32_e32 v2, v7, v11                                 // 0000000007D0: 56041707
	v_fmac_f32_e32 v3, v7, v12                                 // 0000000007D4: 56061907
	v_fmac_f32_e32 v4, v7, v13                                 // 0000000007D8: 56081B07
	v_mul_f32_e32 v10, 0x3dcccccd, v1                          // 0000000007DC: 101402FF 3DCCCCCD
	v_mul_f32_e32 v11, 0x3dcccccd, v2                          // 0000000007E4: 101604FF 3DCCCCCD
	v_mul_f32_e32 v12, 0x3dcccccd, v3                          // 0000000007EC: 101806FF 3DCCCCCD
	v_mul_f32_e32 v13, 0x3dcccccd, v4                          // 0000000007F4: 101A08FF 3DCCCCCD
	buffer_store_dwordx4 v[10:13], v6, s[12:15], 0 offen       // 0000000007FC: E0781000 80030A06
	s_cbranch_scc0 _L14                                        // 000000000804: BF84FFC9
_L13:
	s_cbranch_execnz _L15                                      // 000000000808: BF89008D
_L8:
	s_buffer_load_dword s0, s[16:19], 0x8                      // 00000000080C: F4200008 FA000008
	v_mov_b32_e32 v4, 0                                        // 000000000814: 7E080280
	v_mov_b32_e32 v3, 0                                        // 000000000818: 7E060280
	v_mov_b32_e32 v2, 0                                        // 00000000081C: 7E040280
	v_mov_b32_e32 v1, 0                                        // 000000000820: 7E020280
	s_mov_b32 s1, 0                                            // 000000000824: BE810380
	s_waitcnt lgkmcnt(0)                                       // 000000000828: BF8CC07F
	s_cmp_eq_u32 s0, 0                                         // 00000000082C: BF068000
	s_cbranch_scc1 _L15                                        // 000000000830: BF850083
	v_lshlrev_b32_e32 v0, 4, v9                                // 000000000834: 34001284
	v_mov_b32_e32 v1, 0                                        // 000000000838: 7E020280
	s_mov_b32 s3, 0xbc46c6a5                                   // 00000000083C: BE8303FF BC46C6A5
	s_buffer_load_dword s2, s[16:19], 0x10                     // 000000000844: F4200088 FA000010
	buffer_load_dwordx4 v[5:8], v0, s[16:19], 0 offen          // 00000000084C: E0381000 80040500
	v_mov_b32_e32 v2, v1                                       // 000000000854: 7E040301
	v_mov_b32_e32 v3, v1                                       // 000000000858: 7E060301
	v_mov_b32_e32 v4, v1                                       // 00000000085C: 7E080301
_L16:
	v_cvt_f32_u32_e32 v0, s1                                   // 000000000860: 7E000C01
	s_waitcnt vmcnt(0)                                         // 000000000864: BF8C3F70
	v_sqrt_f32_e64 v12, |v7|                                   // 000000000868: D5B3010C 00000107
	v_mul_f32_e32 v13, v7, v7                                  // 000000000870: 101A0F07
	v_mul_f32_e32 v15, 0xbe13bb63, v7                          // 000000000874: 101E0EFF BE13BB63
	v_mul_f32_e32 v16, v5, v6                                  // 00000000087C: 10200D05
	s_waitcnt lgkmcnt(0)                                       // 000000000880: BF8CC07F
	v_fma_f32 v0, 0x3c23d70a, v0, s2                           // 000000000884: D54B0000 000A00FF 3C23D70A
	v_mul_f32_e32 v10, 0x3e13bb63, v5                          // 000000000890: 10140AFF 3E13BB63
	v_add_f32_e64 v14, |v8|, 1.0                               // 000000000898: D503010E 0001E508
	v_exp_f32_e32 v15, v15                                     // 0000000008A0: 7E1E4B0F
	v_add_f32_e64 v16, |v16|, 1.0                              // 0000000008A4: D5030110 0001E510
	v_mul_f32_e32 v17, v0, v8                                  // 0000000008AC: 10221100
	v_mul_f32_e32 v19, v0, v5                                  // 0000000008B0: 10260B00
	v_mul_f32_e32 v20, v0, v6                                  // 0000000008B4: 10280D00
	v_mul_f32_e32 v21, v0, v7                                  // 0000000008B8: 102A0F00
	v_mul_f32_e32 v12, v13, v12                                // 0000000008BC: 1018190D
	v_max_f32_e64 v18, |v17|, 1.0                              // 0000000008C0: D5100112 0001E511
	v_min_f32_e64 v23, |v17|, 1.0                              // 0000000008C8: D50F0117 0001E511
	v_cmp_ge_f32_e32 vcc_lo, 0, v17                            // 0000000008D0: 7C0C2280
	v_add_f32_e32 v13, v0, v5                                  // 0000000008D4: 061A0B00
	v_add_f32_e32 v22, v0, v6                                  // 0000000008D8: 062C0D00
	v_rcp_f32_e32 v18, v18                                     // 0000000008DC: 7E245512
	v_add_f32_e32 v0, v0, v8                                   // 0000000008E0: 06001100
	v_cndmask_b32_e32 v25, 1.0, v17, vcc_lo                    // 0000000008E4: 023222F2
	v_mul_f32_e32 v21, 0.15915494, v21                         // 0000000008E8: 102A2AF8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 0000000008EC: 102C2CF8
	v_mul_f32_e32 v13, 0.15915494, v13                         // 0000000008F0: 101A1AF8
	v_mul_f32_e32 v0, 0.15915494, v0                           // 0000000008F4: 100000F8
	v_cmp_le_f32_e32 vcc_lo, 0, v25                            // 0000000008F8: 7C063280
	v_add_f32_e64 v11, |v6|, 1.0                               // 0000000008FC: D503010B 0001E506
	v_exp_f32_e32 v10, v10                                     // 000000000904: 7E144B0A
	v_mul_f32_e32 v18, v23, v18                                // 000000000908: 10242517
	v_sin_f32_e32 v0, v0                                       // 00000000090C: 7E006B00
	v_sqrt_f32_e32 v14, v14                                    // 000000000910: 7E1C670E
	v_log_f32_e32 v16, v16                                     // 000000000914: 7E204F10
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000918: 102626F8
	v_mul_f32_e32 v23, v18, v18                                // 00000000091C: 102E2512
	v_mul_f32_e32 v24, 0x3f7ffea5, v18                         // 000000000920: 103024FF 3F7FFEA5
	v_mul_f32_e32 v20, 0.15915494, v20                         // 000000000928: 102828F8
	v_sin_f32_e32 v13, v13                                     // 00000000092C: 7E1A6B0D
	v_log_f32_e32 v11, v11                                     // 000000000930: 7E164F0B
	v_mul_f32_e32 v18, v23, v18                                // 000000000934: 10242517
	v_fmaak_f32 v26, s3, v23, 0x3d5be101                       // 000000000938: 5A342E03 3D5BE101
	v_sin_f32_e32 v19, v19                                     // 000000000940: 7E266B13
	v_cos_f32_e32 v20, v20                                     // 000000000944: 7E286D14
	v_mul_f32_e32 v10, 0x3f317218, v10                         // 000000000948: 101414FF 3F317218
	v_mul_f32_e32 v27, v18, v23                                // 000000000950: 10362F12
	v_fmac_f32_e32 v24, 0xbeaa5476, v18                        // 000000000954: 563024FF BEAA5476
	v_fmaak_f32 v18, v26, v23, 0xbdf0555d                      // 00000000095C: 5A242F1A BDF0555D
	v_sin_f32_e32 v26, v21                                     // 000000000964: 7E346B15
	v_fmac_f32_e32 v4, v12, v14                                // 000000000968: 56081D0C
	v_mul_f32_e32 v23, v27, v23                                // 00000000096C: 102E2F1B
	v_fmac_f32_e32 v24, 0x3e468bc1, v27                        // 000000000970: 563036FF 3E468BC1
	v_mul_f32_e32 v12, 0x3f317218, v16                         // 000000000978: 101820FF 3F317218
	v_fmac_f32_e32 v3, v10, v11                                // 000000000980: 5606170A
	v_fmac_f32_e32 v1, v19, v20                                // 000000000984: 56022913
	v_mul_f32_e32 v5, 0x3f8147ae, v5                           // 000000000988: 100A0AFF 3F8147AE
	v_fmac_f32_e32 v24, v23, v18                               // 000000000990: 56302517
	v_cos_f32_e32 v18, v21                                     // 000000000994: 7E246D15
	v_cos_f32_e32 v21, v22                                     // 000000000998: 7E2A6D16
	v_cndmask_b32_e32 v23, -1.0, v25, vcc_lo                   // 00000000099C: 022E32F3
	v_cmp_gt_f32_e64 vcc_lo, |v17|, 1.0                        // 0000000009A0: D404016A 0001E511
	v_fmaak_f32 v22, -2.0, v24, 0x3fc90fdb                     // 0000000009A8: 5A2C30F5 3FC90FDB
	v_mul_f32_e32 v6, 0x3f8147ae, v6                           // 0000000009B0: 100C0CFF 3F8147AE
	v_mul_f32_e32 v7, 0x3f8147ae, v7                           // 0000000009B8: 100E0EFF 3F8147AE
	v_mul_f32_e32 v8, 0x3f8147ae, v8                           // 0000000009C0: 101010FF 3F8147AE
	s_add_i32 s1, s1, 1                                        // 0000000009C8: 81018101
	v_cndmask_b32_e32 v17, 0, v22, vcc_lo                      // 0000000009CC: 02222C80
	v_mul_f32_e32 v22, v23, v26                                // 0000000009D0: 102C3517
	v_rcp_f32_e32 v18, v18                                     // 0000000009D4: 7E245512
	v_mul_f32_e64 v23, |v0|, |v0|                              // 0000000009D8: D5080317 00020100
	v_mul_f32_e32 v15, v21, v15                                // 0000000009E0: 101E1F15
	v_add_f32_e32 v17, v17, v24                                // 0000000009E4: 06223111
	s_cmp_ge_u32 s1, s0                                        // 0000000009E8: BF090001
	v_mul_f32_e64 v0, |v0|, v23                                // 0000000009EC: D5080100 00022F00
	v_mul_f32_e32 v14, v15, v13                                // 0000000009F4: 101C1B0F
	v_mul_f32_e32 v16, v22, v17                                // 0000000009F8: 10202316
	v_fmac_f32_e32 v1, v15, v13                                // 0000000009FC: 56021B0F
	v_mul_f32_e32 v10, v12, v0                                 // 000000000A00: 1014010C
	v_fma_f32 v11, v12, v0, v14                                // 000000000A04: D54B000B 043A010C
	v_fmac_f32_e32 v2, v16, v18                                // 000000000A0C: 56042510
	v_fmac_f32_e32 v5, 0x3c23d70a, v1                          // 000000000A10: 560A02FF 3C23D70A
	v_fmac_f32_e32 v3, v10, v14                                // 000000000A18: 56061D0A
	v_add_f32_e32 v4, v4, v11                                  // 000000000A1C: 06081704
	v_fmac_f32_e32 v2, v12, v0                                 // 000000000A20: 5604010C
	v_fmac_f32_e32 v7, 0x3c23d70a, v3                          // 000000000A24: 560E06FF 3C23D70A
	v_fmac_f32_e32 v8, 0x3c23d70a, v4                          // 000000000A2C: 561008FF 3C23D70A
	v_fmac_f32_e32 v6, 0x3c23d70a, v2                          // 000000000A34: 560C04FF 3C23D70A
	s_cbranch_scc0 _L16                                        // 000000000A3C: BF84FF88
_L15:
	v_lshlrev_b32_e32 v0, 4, v9                                // 000000000A40: 34001284
	buffer_store_dwordx4 v[1:4], v0, s[12:15], 0 offen         // 000000000A44: E0781000 80030100
_L0:
	s_endpgm                                                   // 000000000A4C: BF810000

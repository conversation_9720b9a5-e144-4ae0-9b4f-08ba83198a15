; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 175
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "DiffuseColor"
               OpMemberName %type_Material 1 "SpecularColor"
               OpMemberName %type_Material 2 "SpecularPower"
               OpMemberName %type_Material 3 "AmbientColor"
               OpMemberName %type_Material 4 "ShadowBias"
               OpMemberName %type_Material 5 "ShadowStrength"
               OpMemberName %type_Material 6 "PCFSamples"
               OpName %Material "Material"
               OpName %type_2d_image "type.2d.image"
               OpName %DiffuseTexture "DiffuseTexture"
               OpName %ShadowMap "ShadowMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %ShadowSampler "ShadowSampler"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %DiffuseTexture DescriptorSet 0
               OpDecorate %DiffuseTexture Binding 0
               OpDecorate %ShadowMap DescriptorSet 0
               OpDecorate %ShadowMap Binding 1
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %ShadowSampler DescriptorSet 0
               OpDecorate %ShadowSampler Binding 1
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 16
               OpMemberDecorate %type_Material 2 Offset 32
               OpMemberDecorate %type_Material 3 Offset 36
               OpMemberDecorate %type_Material 4 Offset 48
               OpMemberDecorate %type_Material 5 Offset 52
               OpMemberDecorate %type_Material 6 Offset 56
               OpDecorate %type_Material Block
        %int = OpTypeInt 32 1
      %int_2 = OpConstant %int 2
      %int_6 = OpConstant %int 6
      %int_3 = OpConstant %int 3
      %int_5 = OpConstant %int 5
      %int_4 = OpConstant %int 4
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %float_0 = OpConstant %float 0
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
  %float_0_5 = OpConstant %float 0.5
    %v2float = OpTypeVector %float 2
         %31 = OpConstantComposite %v2float %float_0_5 %float_0_5
       %uint = OpTypeInt 32 0
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
    %v4float = OpTypeVector %float 4
    %v3float = OpTypeVector %float 3
%type_Material = OpTypeStruct %v4float %v4float %float %v3float %float %float %int
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %45 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_ptr_Uniform_int = OpTypePointer Uniform %int
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
%DiffuseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
  %ShadowMap = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%ShadowSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
     %uint_0 = OpConstant %uint 0
%float_0_00048828125 = OpConstant %float 0.00048828125
         %52 = OpConstantComposite %v2float %float_0_00048828125 %float_0_00048828125
     %int_n2 = OpConstant %int -2
       %main = OpFunction %void None %45
         %54 = OpLabel
         %55 = OpLoad %v3float %in_var_TEXCOORD1
         %56 = OpLoad %v2float %in_var_TEXCOORD2
         %57 = OpLoad %v4float %in_var_TEXCOORD4
         %58 = OpLoad %v3float %in_var_TEXCOORD5
         %59 = OpLoad %v3float %in_var_TEXCOORD6
         %60 = OpExtInst %v3float %1 Normalize %55
         %61 = OpExtInst %v3float %1 Normalize %58
         %62 = OpExtInst %v3float %1 Normalize %59
         %63 = OpLoad %type_2d_image %DiffuseTexture
         %64 = OpLoad %type_sampler %LinearSampler
         %65 = OpSampledImage %type_sampled_image %63 %64
         %66 = OpImageSampleImplicitLod %v4float %65 %56 None
         %67 = OpAccessChain %_ptr_Uniform_float %Material %int_4
         %68 = OpLoad %float %67
               OpSelectionMerge %69 None
               OpSwitch %uint_0 %70
         %70 = OpLabel
         %71 = OpVectorShuffle %v3float %57 %57 0 1 2
         %72 = OpCompositeExtract %float %57 3
         %73 = OpCompositeConstruct %v3float %72 %72 %72
         %74 = OpFDiv %v3float %71 %73
         %75 = OpVectorShuffle %v2float %74 %74 0 1
         %76 = OpVectorTimesScalar %v2float %75 %float_0_5
         %77 = OpFAdd %v2float %76 %31
         %78 = OpVectorShuffle %v3float %74 %77 3 4 2
         %79 = OpCompositeExtract %float %77 1
         %80 = OpFSub %float %float_1 %79
         %81 = OpCompositeInsert %v3float %80 %78 1
         %82 = OpCompositeExtract %float %77 0
         %83 = OpFOrdLessThan %bool %82 %float_0
         %84 = OpLogicalNot %bool %83
               OpSelectionMerge %85 None
               OpBranchConditional %84 %86 %85
         %86 = OpLabel
         %87 = OpFOrdGreaterThan %bool %82 %float_1
               OpBranch %85
         %85 = OpLabel
         %88 = OpPhi %bool %true %70 %87 %86
         %89 = OpLogicalNot %bool %88
               OpSelectionMerge %90 None
               OpBranchConditional %89 %91 %90
         %91 = OpLabel
         %92 = OpFOrdLessThan %bool %80 %float_0
               OpBranch %90
         %90 = OpLabel
         %93 = OpPhi %bool %true %85 %92 %91
         %94 = OpLogicalNot %bool %93
               OpSelectionMerge %95 None
               OpBranchConditional %94 %96 %95
         %96 = OpLabel
         %97 = OpFOrdGreaterThan %bool %80 %float_1
               OpBranch %95
         %95 = OpLabel
         %98 = OpPhi %bool %true %90 %97 %96
               OpSelectionMerge %99 None
               OpBranchConditional %98 %100 %99
        %100 = OpLabel
               OpBranch %69
         %99 = OpLabel
        %101 = OpCompositeExtract %float %74 2
        %102 = OpAccessChain %_ptr_Uniform_int %Material %int_6
        %103 = OpLoad %int %102
        %104 = OpSDiv %int %103 %int_2
        %105 = OpSDiv %int %103 %int_n2
               OpBranch %106
        %106 = OpLabel
        %107 = OpPhi %float %float_0 %99 %108 %109
        %110 = OpPhi %int %105 %99 %111 %109
        %112 = OpSLessThanEqual %bool %110 %104
               OpLoopMerge %113 %109 None
               OpBranchConditional %112 %114 %113
        %114 = OpLabel
               OpBranch %115
        %115 = OpLabel
        %108 = OpPhi %float %107 %114 %116 %117
        %118 = OpPhi %int %105 %114 %119 %117
        %120 = OpSLessThanEqual %bool %118 %104
               OpLoopMerge %121 %117 None
               OpBranchConditional %120 %117 %121
        %117 = OpLabel
        %122 = OpConvertSToF %float %110
        %123 = OpConvertSToF %float %118
        %124 = OpCompositeConstruct %v2float %122 %123
        %125 = OpFMul %v2float %124 %52
        %126 = OpLoad %type_2d_image %ShadowMap
        %127 = OpLoad %type_sampler %ShadowSampler
        %128 = OpVectorShuffle %v2float %81 %81 0 1
        %129 = OpFAdd %v2float %128 %125
        %130 = OpFSub %float %101 %68
        %131 = OpSampledImage %type_sampled_image %126 %127
        %132 = OpImageSampleDrefExplicitLod %float %131 %129 %130 Lod %float_0
        %116 = OpFAdd %float %108 %132
        %119 = OpIAdd %int %118 %int_1
               OpBranch %115
        %121 = OpLabel
               OpBranch %109
        %109 = OpLabel
        %111 = OpIAdd %int %110 %int_1
               OpBranch %106
        %113 = OpLabel
        %133 = OpIAdd %int %103 %103
        %134 = OpIAdd %int %133 %int_1
        %135 = OpConvertSToF %float %134
        %136 = OpFDiv %float %107 %135
               OpBranch %69
         %69 = OpLabel
        %137 = OpPhi %float %float_0 %100 %136 %113
        %138 = OpAccessChain %_ptr_Uniform_float %Material %int_5
        %139 = OpLoad %float %138
        %140 = OpExtInst %float %1 FMix %139 %float_1 %137
        %141 = OpAccessChain %_ptr_Uniform_v3float %Material %int_3
        %142 = OpLoad %v3float %141
        %143 = OpVectorShuffle %v3float %66 %66 0 1 2
        %144 = OpFMul %v3float %142 %143
        %145 = OpDot %float %60 %61
        %146 = OpExtInst %float %1 NMax %float_0 %145
        %147 = OpAccessChain %_ptr_Uniform_v4float %Material %int_0
        %148 = OpLoad %v4float %147
        %149 = OpVectorShuffle %v3float %148 %148 0 1 2
        %150 = OpFMul %v3float %149 %143
        %151 = OpVectorTimesScalar %v3float %150 %146
        %152 = OpVectorTimesScalar %v3float %151 %140
        %153 = OpFAdd %v3float %61 %62
        %154 = OpExtInst %v3float %1 Normalize %153
        %155 = OpDot %float %60 %154
        %156 = OpExtInst %float %1 NMax %float_0 %155
        %157 = OpAccessChain %_ptr_Uniform_v4float %Material %int_1
        %158 = OpLoad %v4float %157
        %159 = OpVectorShuffle %v3float %158 %158 0 1 2
        %160 = OpAccessChain %_ptr_Uniform_float %Material %int_2
        %161 = OpLoad %float %160
        %162 = OpExtInst %float %1 Pow %156 %161
        %163 = OpVectorTimesScalar %v3float %159 %162
        %164 = OpVectorTimesScalar %v3float %163 %140
        %165 = OpFAdd %v3float %144 %152
        %166 = OpFAdd %v3float %165 %164
        %167 = OpCompositeExtract %float %66 3
        %168 = OpAccessChain %_ptr_Uniform_float %Material %int_0 %int_3
        %169 = OpLoad %float %168
        %170 = OpFMul %float %167 %169
        %171 = OpCompositeExtract %float %166 0
        %172 = OpCompositeExtract %float %166 1
        %173 = OpCompositeExtract %float %166 2
        %174 = OpCompositeConstruct %v4float %171 %172 %173 %170
               OpStore %out_var_SV_TARGET %174
               OpReturn
               OpFunctionEnd

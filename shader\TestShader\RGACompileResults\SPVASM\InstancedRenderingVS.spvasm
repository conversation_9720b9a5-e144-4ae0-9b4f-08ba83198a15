; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 148
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TEXCOORD0 %in_var_COLOR0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 1 "CameraPosition"
               OpMemberName %type_PerFrame 2 "Time"
               OpMemberName %type_PerFrame 3 "WindDirection"
               OpMemberName %type_PerFrame 4 "WindStrength"
               OpName %PerFrame "PerFrame"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TEXCOORD0 Location 2
               OpDecorate %in_var_COLOR0 Location 3
               OpDecorate %in_var_TEXCOORD1 Location 4
               OpDecorate %in_var_TEXCOORD2 Location 5
               OpDecorate %in_var_TEXCOORD3 Location 6
               OpDecorate %in_var_TEXCOORD4 Location 7
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 2 Offset 76
               OpMemberDecorate %type_PerFrame 3 Offset 80
               OpMemberDecorate %type_PerFrame 4 Offset 92
               OpDecorate %type_PerFrame Block
        %int = OpTypeInt 32 1
      %int_4 = OpConstant %int 4
      %int_3 = OpConstant %int 3
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %float_2 = OpConstant %float 2
    %float_3 = OpConstant %float 3
%float_0_100000001 = OpConstant %float 0.100000001
  %float_0_5 = OpConstant %float 0.5
%float_0_300000012 = OpConstant %float 0.300000012
    %v3float = OpTypeVector %float 3
         %35 = OpConstantComposite %v3float %float_0_5 %float_0_300000012 %float_0_100000001
    %float_0 = OpConstant %float 0
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %v3float %float %v3float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %49 = OpTypeFunction %void
       %bool = OpTypeBool
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v4float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_float Output
       %main = OpFunction %void None %49
         %54 = OpLabel
         %55 = OpLoad %v3float %in_var_POSITION
         %56 = OpLoad %v3float %in_var_NORMAL
         %57 = OpLoad %v2float %in_var_TEXCOORD0
         %58 = OpLoad %v4float %in_var_COLOR0
         %59 = OpLoad %v4float %in_var_TEXCOORD1
         %60 = OpLoad %v4float %in_var_TEXCOORD2
         %61 = OpLoad %v4float %in_var_TEXCOORD3
         %62 = OpLoad %v4float %in_var_TEXCOORD4
         %63 = OpVectorShuffle %v3float %59 %59 0 1 2
         %64 = OpCompositeExtract %float %59 3
         %65 = OpCompositeExtract %float %62 0
         %66 = OpCompositeExtract %float %62 1
         %67 = OpCompositeExtract %float %62 2
         %68 = OpCompositeExtract %float %62 3
         %69 = OpFOrdLessThan %bool %67 %float_1
               OpSelectionMerge %70 None
               OpBranchConditional %69 %71 %72
         %71 = OpLabel
         %73 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_2
         %74 = OpLoad %float %73
         %75 = OpFMul %float %74 %float_2
         %76 = OpFAdd %float %75 %65
         %77 = OpCompositeExtract %float %59 0
         %78 = OpFMul %float %77 %float_0_100000001
         %79 = OpFAdd %float %76 %78
         %80 = OpExtInst %float %1 Sin %79
         %81 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_4
         %82 = OpLoad %float %81
         %83 = OpFMul %float %80 %82
         %84 = OpCompositeExtract %float %55 1
         %85 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_3
         %86 = OpLoad %v3float %85
         %87 = OpVectorTimesScalar %v3float %86 %83
         %88 = OpVectorTimesScalar %v3float %87 %84
         %89 = OpVectorTimesScalar %v3float %88 %float_0_100000001
         %90 = OpFAdd %v3float %55 %89
         %91 = OpFMul %float %68 %float_0_100000001
         %92 = OpExtInst %float %1 FClamp %91 %float_0 %float_1
         %93 = OpFMul %float %92 %66
         %94 = OpVectorTimesScalar %v3float %90 %93
               OpBranch %70
         %72 = OpLabel
         %95 = OpFOrdLessThan %bool %67 %float_2
               OpSelectionMerge %96 None
               OpBranchConditional %95 %97 %98
         %97 = OpLabel
               OpBranch %96
         %98 = OpLabel
         %99 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_2
        %100 = OpLoad %float %99
        %101 = OpFMul %float %100 %float_3
        %102 = OpFAdd %float %101 %65
        %103 = OpExtInst %float %1 Sin %102
        %104 = OpFMul %float %103 %float_0_100000001
        %105 = OpCompositeExtract %float %55 1
        %106 = OpFAdd %float %105 %104
        %107 = OpCompositeInsert %v3float %106 %55 1
               OpBranch %96
         %96 = OpLabel
        %108 = OpPhi %v3float %55 %97 %107 %98
               OpBranch %70
         %70 = OpLabel
        %109 = OpPhi %v3float %94 %71 %108 %96
        %110 = OpVectorTimesScalar %v3float %109 %64
        %111 = OpVectorShuffle %v3float %60 %60 0 1 2
        %112 = OpExtInst %v3float %1 Cross %111 %110
        %113 = OpExtInst %v3float %1 Cross %111 %112
        %114 = OpCompositeExtract %float %60 3
        %115 = OpVectorTimesScalar %v3float %112 %114
        %116 = OpFAdd %v3float %115 %113
        %117 = OpVectorTimesScalar %v3float %116 %float_2
        %118 = OpFAdd %v3float %110 %117
        %119 = OpExtInst %v3float %1 Cross %111 %56
        %120 = OpExtInst %v3float %1 Cross %111 %119
        %121 = OpVectorTimesScalar %v3float %119 %114
        %122 = OpFAdd %v3float %121 %120
        %123 = OpVectorTimesScalar %v3float %122 %float_2
        %124 = OpFAdd %v3float %56 %123
        %125 = OpFAdd %v3float %118 %63
        %126 = OpCompositeExtract %float %125 0
        %127 = OpCompositeExtract %float %125 1
        %128 = OpCompositeExtract %float %125 2
        %129 = OpCompositeConstruct %v4float %126 %127 %128 %float_1
        %130 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_0
        %131 = OpLoad %mat4v4float %130
        %132 = OpMatrixTimesVector %v4float %131 %129
        %133 = OpExtInst %v3float %1 Normalize %124
        %134 = OpFMul %v4float %58 %61
        %135 = OpFOrdLessThan %bool %66 %float_0_5
               OpSelectionMerge %136 None
               OpBranchConditional %135 %137 %136
        %137 = OpLabel
        %138 = OpVectorShuffle %v3float %134 %134 0 1 2
        %139 = OpFMul %float %66 %float_2
        %140 = OpCompositeConstruct %v3float %139 %139 %139
        %141 = OpExtInst %v3float %1 FMix %35 %138 %140
        %142 = OpVectorShuffle %v4float %134 %141 4 5 6 3
               OpBranch %136
        %136 = OpLabel
        %143 = OpPhi %v4float %134 %70 %142 %137
        %144 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_1
        %145 = OpLoad %v3float %144
        %146 = OpFSub %v3float %145 %125
        %147 = OpExtInst %v3float %1 Normalize %146
               OpStore %gl_Position %132
               OpStore %out_var_TEXCOORD0 %125
               OpStore %out_var_TEXCOORD1 %133
               OpStore %out_var_TEXCOORD2 %57
               OpStore %out_var_TEXCOORD3 %143
               OpStore %out_var_TEXCOORD4 %147
               OpStore %out_var_TEXCOORD5 %67
               OpStore %out_var_TEXCOORD6 %66
               OpReturn
               OpFunctionEnd

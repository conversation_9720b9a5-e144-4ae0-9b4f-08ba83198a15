; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 495
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_RWStructuredBuffer_MeshVertex "type.RWStructuredBuffer.MeshVertex"
               OpName %MeshVertex "MeshVertex"
               OpMemberName %MeshVertex 0 "Position"
               OpMemberName %MeshVertex 1 "Normal"
               OpMemberName %MeshVertex 2 "TexCoord"
               OpMemberName %MeshVertex 3 "Color"
               OpName %VertexBuffer "VertexBuffer"
               OpName %type_RWStructuredBuffer_uint "type.RWStructuredBuffer.uint"
               OpName %IndexBuffer "IndexBuffer"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %VertexBuffer DescriptorSet 0
               OpDecorate %VertexBuffer Binding 0
               OpDecorate %IndexBuffer DescriptorSet 0
               OpDecorate %IndexBuffer Binding 1
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
               OpMemberDecorate %MeshVertex 0 Offset 0
               OpMemberDecorate %MeshVertex 1 Offset 16
               OpMemberDecorate %MeshVertex 2 Offset 32
               OpMemberDecorate %MeshVertex 3 Offset 48
               OpDecorate %_runtimearr_MeshVertex ArrayStride 64
               OpMemberDecorate %type_RWStructuredBuffer_MeshVertex 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_MeshVertex BufferBlock
               OpDecorate %_runtimearr_uint ArrayStride 4
               OpMemberDecorate %type_RWStructuredBuffer_uint 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_uint BufferBlock
       %uint = OpTypeInt 32 0
    %uint_64 = OpConstant %uint 64
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
     %uint_1 = OpConstant %uint 1
      %float = OpTypeFloat 32
  %float_100 = OpConstant %float 100
   %float_50 = OpConstant %float 50
    %float_0 = OpConstant %float 0
%float_0_00999999978 = OpConstant %float 0.00999999978
      %int_3 = OpConstant %int 3
%float_0_100000001 = OpConstant %float 0.100000001
   %float_10 = OpConstant %float 10
%float_0_0500000007 = OpConstant %float 0.0500000007
    %float_2 = OpConstant %float 2
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
         %32 = OpConstantComposite %v3float %float_0 %float_1 %float_0
     %uint_0 = OpConstant %uint 0
      %false = OpConstantFalse %bool
  %float_0_5 = OpConstant %float 0.5
     %uint_6 = OpConstant %uint 6
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
     %uint_4 = OpConstant %uint 4
     %uint_5 = OpConstant %uint 5
    %float_3 = OpConstant %float 3
   %float_57 = OpConstant %float 57
  %float_113 = OpConstant %float 113
   %float_58 = OpConstant %float 58
  %float_114 = OpConstant %float 114
  %float_170 = OpConstant %float 170
  %float_171 = OpConstant %float 171
%float_43758_5469 = OpConstant %float 43758.5469
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
    %v4float = OpTypeVector %float 4
 %MeshVertex = OpTypeStruct %v3float %v3float %v2float %v4float
%_runtimearr_MeshVertex = OpTypeRuntimeArray %MeshVertex
%type_RWStructuredBuffer_MeshVertex = OpTypeStruct %_runtimearr_MeshVertex
%_ptr_Uniform_type_RWStructuredBuffer_MeshVertex = OpTypePointer Uniform %type_RWStructuredBuffer_MeshVertex
%_runtimearr_uint = OpTypeRuntimeArray %uint
%type_RWStructuredBuffer_uint = OpTypeStruct %_runtimearr_uint
%_ptr_Uniform_type_RWStructuredBuffer_uint = OpTypePointer Uniform %type_RWStructuredBuffer_uint
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %57 = OpTypeFunction %void
     %v2uint = OpTypeVector %uint 2
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_MeshVertex = OpTypePointer Uniform %MeshVertex
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%VertexBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_MeshVertex Uniform
%IndexBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_uint Uniform
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
    %uint_63 = OpConstant %uint 63
%float_n0_00999999978 = OpConstant %float -0.00999999978
         %64 = OpConstantComposite %v3float %float_n0_00999999978 %float_0 %float_0
         %65 = OpConstantComposite %v3float %float_0_00999999978 %float_0 %float_0
         %66 = OpConstantComposite %v3float %float_0 %float_0 %float_n0_00999999978
         %67 = OpConstantComposite %v3float %float_0 %float_0 %float_0_00999999978
%float_0_0158730168 = OpConstant %float 0.0158730168
         %69 = OpConstantComposite %v2float %float_0_0158730168 %float_0_0158730168
       %main = OpFunction %void None %57
         %70 = OpLabel
         %71 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %72 None
               OpSwitch %uint_0 %73
         %73 = OpLabel
         %74 = OpCompositeExtract %uint %71 0
         %75 = OpUGreaterThanEqual %bool %74 %uint_64
         %76 = OpLogicalNot %bool %75
               OpSelectionMerge %77 None
               OpBranchConditional %76 %78 %77
         %78 = OpLabel
         %79 = OpCompositeExtract %uint %71 1
         %80 = OpUGreaterThanEqual %bool %79 %uint_64
               OpBranch %77
         %77 = OpLabel
         %81 = OpPhi %bool %true %73 %80 %78
               OpSelectionMerge %82 None
               OpBranchConditional %81 %83 %82
         %83 = OpLabel
               OpBranch %72
         %82 = OpLabel
         %84 = OpCompositeExtract %uint %71 1
         %85 = OpIMul %uint %84 %uint_64
         %86 = OpIAdd %uint %85 %74
         %87 = OpVectorShuffle %v2uint %71 %71 0 1
         %88 = OpConvertUToF %v2float %87
         %89 = OpFMul %v2float %88 %69
         %90 = OpCompositeExtract %float %89 0
         %91 = OpFMul %float %90 %float_100
         %92 = OpFSub %float %91 %float_50
         %93 = OpCompositeExtract %float %89 1
         %94 = OpFMul %float %93 %float_100
         %95 = OpFSub %float %94 %float_50
         %96 = OpCompositeConstruct %v3float %92 %float_0 %95
         %97 = OpVectorTimesScalar %v3float %96 %float_0_00999999978
         %98 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_3
         %99 = OpLoad %float %98
        %100 = OpFMul %float %99 %float_0_100000001
        %101 = OpCompositeConstruct %v3float %100 %100 %100
        %102 = OpFAdd %v3float %97 %101
        %103 = OpExtInst %v3float %1 Floor %102
        %104 = OpExtInst %v3float %1 Fract %102
        %105 = OpFMul %v3float %104 %104
        %106 = OpVectorTimesScalar %v3float %105 %float_3
        %107 = OpVectorTimesScalar %v3float %104 %float_2
        %108 = OpFSub %v3float %106 %107
        %109 = OpCompositeExtract %float %103 0
        %110 = OpCompositeExtract %float %103 1
        %111 = OpFMul %float %110 %float_57
        %112 = OpFAdd %float %109 %111
        %113 = OpCompositeExtract %float %103 2
        %114 = OpFMul %float %float_113 %113
        %115 = OpFAdd %float %112 %114
        %116 = OpExtInst %float %1 Sin %115
        %117 = OpFMul %float %116 %float_43758_5469
        %118 = OpExtInst %float %1 Fract %117
        %119 = OpFAdd %float %115 %float_1
        %120 = OpExtInst %float %1 Sin %119
        %121 = OpFMul %float %120 %float_43758_5469
        %122 = OpExtInst %float %1 Fract %121
        %123 = OpCompositeExtract %float %108 0
        %124 = OpExtInst %float %1 FMix %118 %122 %123
        %125 = OpFAdd %float %115 %float_57
        %126 = OpExtInst %float %1 Sin %125
        %127 = OpFMul %float %126 %float_43758_5469
        %128 = OpExtInst %float %1 Fract %127
        %129 = OpFAdd %float %115 %float_58
        %130 = OpExtInst %float %1 Sin %129
        %131 = OpFMul %float %130 %float_43758_5469
        %132 = OpExtInst %float %1 Fract %131
        %133 = OpExtInst %float %1 FMix %128 %132 %123
        %134 = OpCompositeExtract %float %108 1
        %135 = OpExtInst %float %1 FMix %124 %133 %134
        %136 = OpFAdd %float %115 %float_113
        %137 = OpExtInst %float %1 Sin %136
        %138 = OpFMul %float %137 %float_43758_5469
        %139 = OpExtInst %float %1 Fract %138
        %140 = OpFAdd %float %115 %float_114
        %141 = OpExtInst %float %1 Sin %140
        %142 = OpFMul %float %141 %float_43758_5469
        %143 = OpExtInst %float %1 Fract %142
        %144 = OpExtInst %float %1 FMix %139 %143 %123
        %145 = OpFAdd %float %115 %float_170
        %146 = OpExtInst %float %1 Sin %145
        %147 = OpFMul %float %146 %float_43758_5469
        %148 = OpExtInst %float %1 Fract %147
        %149 = OpFAdd %float %115 %float_171
        %150 = OpExtInst %float %1 Sin %149
        %151 = OpFMul %float %150 %float_43758_5469
        %152 = OpExtInst %float %1 Fract %151
        %153 = OpExtInst %float %1 FMix %148 %152 %123
        %154 = OpExtInst %float %1 FMix %144 %153 %134
        %155 = OpCompositeExtract %float %108 2
        %156 = OpExtInst %float %1 FMix %135 %154 %155
        %157 = OpFMul %float %156 %float_10
        %158 = OpVectorTimesScalar %v3float %96 %float_0_0500000007
        %159 = OpFMul %float %99 %float_0_0500000007
        %160 = OpCompositeConstruct %v3float %159 %159 %159
        %161 = OpFAdd %v3float %158 %160
        %162 = OpExtInst %v3float %1 Floor %161
        %163 = OpExtInst %v3float %1 Fract %161
        %164 = OpFMul %v3float %163 %163
        %165 = OpVectorTimesScalar %v3float %164 %float_3
        %166 = OpVectorTimesScalar %v3float %163 %float_2
        %167 = OpFSub %v3float %165 %166
        %168 = OpCompositeExtract %float %162 0
        %169 = OpCompositeExtract %float %162 1
        %170 = OpFMul %float %169 %float_57
        %171 = OpFAdd %float %168 %170
        %172 = OpCompositeExtract %float %162 2
        %173 = OpFMul %float %float_113 %172
        %174 = OpFAdd %float %171 %173
        %175 = OpExtInst %float %1 Sin %174
        %176 = OpFMul %float %175 %float_43758_5469
        %177 = OpExtInst %float %1 Fract %176
        %178 = OpFAdd %float %174 %float_1
        %179 = OpExtInst %float %1 Sin %178
        %180 = OpFMul %float %179 %float_43758_5469
        %181 = OpExtInst %float %1 Fract %180
        %182 = OpCompositeExtract %float %167 0
        %183 = OpExtInst %float %1 FMix %177 %181 %182
        %184 = OpFAdd %float %174 %float_57
        %185 = OpExtInst %float %1 Sin %184
        %186 = OpFMul %float %185 %float_43758_5469
        %187 = OpExtInst %float %1 Fract %186
        %188 = OpFAdd %float %174 %float_58
        %189 = OpExtInst %float %1 Sin %188
        %190 = OpFMul %float %189 %float_43758_5469
        %191 = OpExtInst %float %1 Fract %190
        %192 = OpExtInst %float %1 FMix %187 %191 %182
        %193 = OpCompositeExtract %float %167 1
        %194 = OpExtInst %float %1 FMix %183 %192 %193
        %195 = OpFAdd %float %174 %float_113
        %196 = OpExtInst %float %1 Sin %195
        %197 = OpFMul %float %196 %float_43758_5469
        %198 = OpExtInst %float %1 Fract %197
        %199 = OpFAdd %float %174 %float_114
        %200 = OpExtInst %float %1 Sin %199
        %201 = OpFMul %float %200 %float_43758_5469
        %202 = OpExtInst %float %1 Fract %201
        %203 = OpExtInst %float %1 FMix %198 %202 %182
        %204 = OpFAdd %float %174 %float_170
        %205 = OpExtInst %float %1 Sin %204
        %206 = OpFMul %float %205 %float_43758_5469
        %207 = OpExtInst %float %1 Fract %206
        %208 = OpFAdd %float %174 %float_171
        %209 = OpExtInst %float %1 Sin %208
        %210 = OpFMul %float %209 %float_43758_5469
        %211 = OpExtInst %float %1 Fract %210
        %212 = OpExtInst %float %1 FMix %207 %211 %182
        %213 = OpExtInst %float %1 FMix %203 %212 %193
        %214 = OpCompositeExtract %float %167 2
        %215 = OpExtInst %float %1 FMix %194 %213 %214
        %216 = OpFMul %float %215 %float_2
        %217 = OpFAdd %float %157 %216
        %218 = OpCompositeInsert %v3float %217 %96 1
        %219 = OpUGreaterThan %bool %74 %uint_0
               OpSelectionMerge %220 None
               OpBranchConditional %219 %221 %220
        %221 = OpLabel
        %222 = OpULessThan %bool %74 %uint_63
               OpBranch %220
        %220 = OpLabel
        %223 = OpPhi %bool %false %82 %222 %221
               OpSelectionMerge %224 None
               OpBranchConditional %223 %225 %224
        %225 = OpLabel
        %226 = OpUGreaterThan %bool %84 %uint_0
               OpBranch %224
        %224 = OpLabel
        %227 = OpPhi %bool %false %220 %226 %225
               OpSelectionMerge %228 None
               OpBranchConditional %227 %229 %228
        %229 = OpLabel
        %230 = OpULessThan %bool %84 %uint_63
               OpBranch %228
        %228 = OpLabel
        %231 = OpPhi %bool %false %224 %230 %229
               OpSelectionMerge %232 None
               OpBranchConditional %231 %233 %232
        %233 = OpLabel
        %234 = OpFAdd %v3float %218 %64
        %235 = OpFAdd %v3float %234 %101
        %236 = OpExtInst %v3float %1 Floor %235
        %237 = OpExtInst %v3float %1 Fract %235
        %238 = OpFMul %v3float %237 %237
        %239 = OpVectorTimesScalar %v3float %238 %float_3
        %240 = OpVectorTimesScalar %v3float %237 %float_2
        %241 = OpFSub %v3float %239 %240
        %242 = OpCompositeExtract %float %236 0
        %243 = OpCompositeExtract %float %236 1
        %244 = OpFMul %float %243 %float_57
        %245 = OpFAdd %float %242 %244
        %246 = OpCompositeExtract %float %236 2
        %247 = OpFMul %float %float_113 %246
        %248 = OpFAdd %float %245 %247
        %249 = OpExtInst %float %1 Sin %248
        %250 = OpFMul %float %249 %float_43758_5469
        %251 = OpExtInst %float %1 Fract %250
        %252 = OpFAdd %float %248 %float_1
        %253 = OpExtInst %float %1 Sin %252
        %254 = OpFMul %float %253 %float_43758_5469
        %255 = OpExtInst %float %1 Fract %254
        %256 = OpCompositeExtract %float %241 0
        %257 = OpExtInst %float %1 FMix %251 %255 %256
        %258 = OpFAdd %float %248 %float_57
        %259 = OpExtInst %float %1 Sin %258
        %260 = OpFMul %float %259 %float_43758_5469
        %261 = OpExtInst %float %1 Fract %260
        %262 = OpFAdd %float %248 %float_58
        %263 = OpExtInst %float %1 Sin %262
        %264 = OpFMul %float %263 %float_43758_5469
        %265 = OpExtInst %float %1 Fract %264
        %266 = OpExtInst %float %1 FMix %261 %265 %256
        %267 = OpCompositeExtract %float %241 1
        %268 = OpExtInst %float %1 FMix %257 %266 %267
        %269 = OpFAdd %float %248 %float_113
        %270 = OpExtInst %float %1 Sin %269
        %271 = OpFMul %float %270 %float_43758_5469
        %272 = OpExtInst %float %1 Fract %271
        %273 = OpFAdd %float %248 %float_114
        %274 = OpExtInst %float %1 Sin %273
        %275 = OpFMul %float %274 %float_43758_5469
        %276 = OpExtInst %float %1 Fract %275
        %277 = OpExtInst %float %1 FMix %272 %276 %256
        %278 = OpFAdd %float %248 %float_170
        %279 = OpExtInst %float %1 Sin %278
        %280 = OpFMul %float %279 %float_43758_5469
        %281 = OpExtInst %float %1 Fract %280
        %282 = OpFAdd %float %248 %float_171
        %283 = OpExtInst %float %1 Sin %282
        %284 = OpFMul %float %283 %float_43758_5469
        %285 = OpExtInst %float %1 Fract %284
        %286 = OpExtInst %float %1 FMix %281 %285 %256
        %287 = OpExtInst %float %1 FMix %277 %286 %267
        %288 = OpCompositeExtract %float %241 2
        %289 = OpExtInst %float %1 FMix %268 %287 %288
        %290 = OpFMul %float %289 %float_10
        %291 = OpFAdd %v3float %218 %65
        %292 = OpFAdd %v3float %291 %101
        %293 = OpExtInst %v3float %1 Floor %292
        %294 = OpExtInst %v3float %1 Fract %292
        %295 = OpFMul %v3float %294 %294
        %296 = OpVectorTimesScalar %v3float %295 %float_3
        %297 = OpVectorTimesScalar %v3float %294 %float_2
        %298 = OpFSub %v3float %296 %297
        %299 = OpCompositeExtract %float %293 0
        %300 = OpCompositeExtract %float %293 1
        %301 = OpFMul %float %300 %float_57
        %302 = OpFAdd %float %299 %301
        %303 = OpCompositeExtract %float %293 2
        %304 = OpFMul %float %float_113 %303
        %305 = OpFAdd %float %302 %304
        %306 = OpExtInst %float %1 Sin %305
        %307 = OpFMul %float %306 %float_43758_5469
        %308 = OpExtInst %float %1 Fract %307
        %309 = OpFAdd %float %305 %float_1
        %310 = OpExtInst %float %1 Sin %309
        %311 = OpFMul %float %310 %float_43758_5469
        %312 = OpExtInst %float %1 Fract %311
        %313 = OpCompositeExtract %float %298 0
        %314 = OpExtInst %float %1 FMix %308 %312 %313
        %315 = OpFAdd %float %305 %float_57
        %316 = OpExtInst %float %1 Sin %315
        %317 = OpFMul %float %316 %float_43758_5469
        %318 = OpExtInst %float %1 Fract %317
        %319 = OpFAdd %float %305 %float_58
        %320 = OpExtInst %float %1 Sin %319
        %321 = OpFMul %float %320 %float_43758_5469
        %322 = OpExtInst %float %1 Fract %321
        %323 = OpExtInst %float %1 FMix %318 %322 %313
        %324 = OpCompositeExtract %float %298 1
        %325 = OpExtInst %float %1 FMix %314 %323 %324
        %326 = OpFAdd %float %305 %float_113
        %327 = OpExtInst %float %1 Sin %326
        %328 = OpFMul %float %327 %float_43758_5469
        %329 = OpExtInst %float %1 Fract %328
        %330 = OpFAdd %float %305 %float_114
        %331 = OpExtInst %float %1 Sin %330
        %332 = OpFMul %float %331 %float_43758_5469
        %333 = OpExtInst %float %1 Fract %332
        %334 = OpExtInst %float %1 FMix %329 %333 %313
        %335 = OpFAdd %float %305 %float_170
        %336 = OpExtInst %float %1 Sin %335
        %337 = OpFMul %float %336 %float_43758_5469
        %338 = OpExtInst %float %1 Fract %337
        %339 = OpFAdd %float %305 %float_171
        %340 = OpExtInst %float %1 Sin %339
        %341 = OpFMul %float %340 %float_43758_5469
        %342 = OpExtInst %float %1 Fract %341
        %343 = OpExtInst %float %1 FMix %338 %342 %313
        %344 = OpExtInst %float %1 FMix %334 %343 %324
        %345 = OpCompositeExtract %float %298 2
        %346 = OpExtInst %float %1 FMix %325 %344 %345
        %347 = OpFMul %float %346 %float_10
        %348 = OpFAdd %v3float %218 %66
        %349 = OpFAdd %v3float %348 %101
        %350 = OpExtInst %v3float %1 Floor %349
        %351 = OpExtInst %v3float %1 Fract %349
        %352 = OpFMul %v3float %351 %351
        %353 = OpVectorTimesScalar %v3float %352 %float_3
        %354 = OpVectorTimesScalar %v3float %351 %float_2
        %355 = OpFSub %v3float %353 %354
        %356 = OpCompositeExtract %float %350 0
        %357 = OpCompositeExtract %float %350 1
        %358 = OpFMul %float %357 %float_57
        %359 = OpFAdd %float %356 %358
        %360 = OpCompositeExtract %float %350 2
        %361 = OpFMul %float %float_113 %360
        %362 = OpFAdd %float %359 %361
        %363 = OpExtInst %float %1 Sin %362
        %364 = OpFMul %float %363 %float_43758_5469
        %365 = OpExtInst %float %1 Fract %364
        %366 = OpFAdd %float %362 %float_1
        %367 = OpExtInst %float %1 Sin %366
        %368 = OpFMul %float %367 %float_43758_5469
        %369 = OpExtInst %float %1 Fract %368
        %370 = OpCompositeExtract %float %355 0
        %371 = OpExtInst %float %1 FMix %365 %369 %370
        %372 = OpFAdd %float %362 %float_57
        %373 = OpExtInst %float %1 Sin %372
        %374 = OpFMul %float %373 %float_43758_5469
        %375 = OpExtInst %float %1 Fract %374
        %376 = OpFAdd %float %362 %float_58
        %377 = OpExtInst %float %1 Sin %376
        %378 = OpFMul %float %377 %float_43758_5469
        %379 = OpExtInst %float %1 Fract %378
        %380 = OpExtInst %float %1 FMix %375 %379 %370
        %381 = OpCompositeExtract %float %355 1
        %382 = OpExtInst %float %1 FMix %371 %380 %381
        %383 = OpFAdd %float %362 %float_113
        %384 = OpExtInst %float %1 Sin %383
        %385 = OpFMul %float %384 %float_43758_5469
        %386 = OpExtInst %float %1 Fract %385
        %387 = OpFAdd %float %362 %float_114
        %388 = OpExtInst %float %1 Sin %387
        %389 = OpFMul %float %388 %float_43758_5469
        %390 = OpExtInst %float %1 Fract %389
        %391 = OpExtInst %float %1 FMix %386 %390 %370
        %392 = OpFAdd %float %362 %float_170
        %393 = OpExtInst %float %1 Sin %392
        %394 = OpFMul %float %393 %float_43758_5469
        %395 = OpExtInst %float %1 Fract %394
        %396 = OpFAdd %float %362 %float_171
        %397 = OpExtInst %float %1 Sin %396
        %398 = OpFMul %float %397 %float_43758_5469
        %399 = OpExtInst %float %1 Fract %398
        %400 = OpExtInst %float %1 FMix %395 %399 %370
        %401 = OpExtInst %float %1 FMix %391 %400 %381
        %402 = OpCompositeExtract %float %355 2
        %403 = OpExtInst %float %1 FMix %382 %401 %402
        %404 = OpFMul %float %403 %float_10
        %405 = OpFAdd %v3float %218 %67
        %406 = OpFAdd %v3float %405 %101
        %407 = OpExtInst %v3float %1 Floor %406
        %408 = OpExtInst %v3float %1 Fract %406
        %409 = OpFMul %v3float %408 %408
        %410 = OpVectorTimesScalar %v3float %409 %float_3
        %411 = OpVectorTimesScalar %v3float %408 %float_2
        %412 = OpFSub %v3float %410 %411
        %413 = OpCompositeExtract %float %407 0
        %414 = OpCompositeExtract %float %407 1
        %415 = OpFMul %float %414 %float_57
        %416 = OpFAdd %float %413 %415
        %417 = OpCompositeExtract %float %407 2
        %418 = OpFMul %float %float_113 %417
        %419 = OpFAdd %float %416 %418
        %420 = OpExtInst %float %1 Sin %419
        %421 = OpFMul %float %420 %float_43758_5469
        %422 = OpExtInst %float %1 Fract %421
        %423 = OpFAdd %float %419 %float_1
        %424 = OpExtInst %float %1 Sin %423
        %425 = OpFMul %float %424 %float_43758_5469
        %426 = OpExtInst %float %1 Fract %425
        %427 = OpCompositeExtract %float %412 0
        %428 = OpExtInst %float %1 FMix %422 %426 %427
        %429 = OpFAdd %float %419 %float_57
        %430 = OpExtInst %float %1 Sin %429
        %431 = OpFMul %float %430 %float_43758_5469
        %432 = OpExtInst %float %1 Fract %431
        %433 = OpFAdd %float %419 %float_58
        %434 = OpExtInst %float %1 Sin %433
        %435 = OpFMul %float %434 %float_43758_5469
        %436 = OpExtInst %float %1 Fract %435
        %437 = OpExtInst %float %1 FMix %432 %436 %427
        %438 = OpCompositeExtract %float %412 1
        %439 = OpExtInst %float %1 FMix %428 %437 %438
        %440 = OpFAdd %float %419 %float_113
        %441 = OpExtInst %float %1 Sin %440
        %442 = OpFMul %float %441 %float_43758_5469
        %443 = OpExtInst %float %1 Fract %442
        %444 = OpFAdd %float %419 %float_114
        %445 = OpExtInst %float %1 Sin %444
        %446 = OpFMul %float %445 %float_43758_5469
        %447 = OpExtInst %float %1 Fract %446
        %448 = OpExtInst %float %1 FMix %443 %447 %427
        %449 = OpFAdd %float %419 %float_170
        %450 = OpExtInst %float %1 Sin %449
        %451 = OpFMul %float %450 %float_43758_5469
        %452 = OpExtInst %float %1 Fract %451
        %453 = OpFAdd %float %419 %float_171
        %454 = OpExtInst %float %1 Sin %453
        %455 = OpFMul %float %454 %float_43758_5469
        %456 = OpExtInst %float %1 Fract %455
        %457 = OpExtInst %float %1 FMix %452 %456 %427
        %458 = OpExtInst %float %1 FMix %448 %457 %438
        %459 = OpCompositeExtract %float %412 2
        %460 = OpExtInst %float %1 FMix %439 %458 %459
        %461 = OpFMul %float %460 %float_10
        %462 = OpFSub %float %290 %347
        %463 = OpFSub %float %404 %461
        %464 = OpCompositeConstruct %v3float %462 %float_2 %463
        %465 = OpExtInst %v3float %1 Normalize %464
               OpBranch %232
        %232 = OpLabel
        %466 = OpPhi %v3float %32 %228 %465 %233
        %467 = OpCompositeConstruct %v4float %90 %93 %float_0_5 %float_1
        %468 = OpAccessChain %_ptr_Uniform_MeshVertex %VertexBuffer %int_0 %86
        %469 = OpCompositeConstruct %MeshVertex %218 %466 %89 %467
               OpStore %468 %469
        %470 = OpULessThan %bool %74 %uint_63
               OpSelectionMerge %471 None
               OpBranchConditional %470 %472 %471
        %472 = OpLabel
        %473 = OpULessThan %bool %84 %uint_63
               OpBranch %471
        %471 = OpLabel
        %474 = OpPhi %bool %false %232 %473 %472
               OpSelectionMerge %475 None
               OpBranchConditional %474 %476 %475
        %476 = OpLabel
        %477 = OpISub %uint %85 %uint_1
        %478 = OpIMul %uint %74 %uint_6
        %479 = OpIAdd %uint %477 %478
        %480 = OpIAdd %uint %86 %uint_1
        %481 = OpIAdd %uint %84 %uint_64
        %482 = OpIAdd %uint %481 %74
        %483 = OpIAdd %uint %482 %uint_1
        %484 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %479
               OpStore %484 %86
        %485 = OpIAdd %uint %479 %uint_1
        %486 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %485
               OpStore %486 %482
        %487 = OpIAdd %uint %479 %uint_2
        %488 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %487
               OpStore %488 %480
        %489 = OpIAdd %uint %479 %uint_3
        %490 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %489
               OpStore %490 %480
        %491 = OpIAdd %uint %479 %uint_4
        %492 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %491
               OpStore %492 %482
        %493 = OpIAdd %uint %479 %uint_5
        %494 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %493
               OpStore %494 %483
               OpBranch %475
        %475 = OpLabel
               OpBranch %72
         %72 = OpLabel
               OpReturn
               OpFunctionEnd

_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v3, s3, 5, v1                                // 000000000040: D76F0003 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v3                            // 000000000048: 7D880601
	s_and_saveexec_b32 s34, vcc_lo                             // 00000000004C: BEA23C6A
	s_cbranch_execz _L1                                        // 000000000050: BF88005D
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v1, s0, v5                                // 000000000058: 4A020A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s5, s3                                           // 000000000060: BE850303
	s_load_dwordx8 s[8:15], s[10:11], null                     // 000000000064: F40C0205 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xyz v[8:10], v1, s[8:11], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80020801
	tbuffer_load_format_xy v[1:2], v1, s[12:15], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000078: EA012000 80030101
	s_load_dwordx4 s[24:27], s[4:5], null                      // 000000000080: F4080602 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000088: BF8CC07F
	s_clause 0x7                                               // 00000000008C: BFA10007
	s_buffer_load_dwordx8 s[0:7], s[24:27], null               // 000000000090: F42C000C FA000000
	s_buffer_load_dwordx8 s[8:15], s[24:27], 0x20              // 000000000098: F42C020C FA000020
	s_buffer_load_dword s35, s[24:27], 0x108                   // 0000000000A0: F42008CC FA000108
	s_buffer_load_dwordx2 s[52:53], s[24:27], 0x100            // 0000000000A8: F4240D0C FA000100
	s_buffer_load_dwordx8 s[44:51], s[24:27], 0x40             // 0000000000B0: F42C0B0C FA000040
	s_buffer_load_dwordx8 s[36:43], s[24:27], 0x60             // 0000000000B8: F42C090C FA000060
	s_buffer_load_dwordx8 s[16:23], s[24:27], 0x80             // 0000000000C0: F42C040C FA000080
	s_buffer_load_dwordx8 s[24:31], s[24:27], 0xa0             // 0000000000C8: F42C060C FA0000A0
	s_waitcnt vmcnt(1) lgkmcnt(0)                              // 0000000000D0: BF8C0071
	v_fma_f32 v4, s0, v8, s3                                   // 0000000000D4: D54B0004 000E1000
	v_fma_f32 v6, s4, v8, s7                                   // 0000000000DC: D54B0006 001E1004
	v_fma_f32 v7, s8, v8, s11                                  // 0000000000E4: D54B0007 002E1008
	v_fma_f32 v5, s12, v8, s15                                 // 0000000000EC: D54B0005 003E100C
	v_mov_b32_e32 v8, s35                                      // 0000000000F4: 7E100223
	v_fmac_f32_e32 v4, s1, v9                                  // 0000000000F8: 56081201
	v_fmac_f32_e32 v6, s5, v9                                  // 0000000000FC: 560C1205
	v_fmac_f32_e32 v7, s9, v9                                  // 000000000100: 560E1209
	v_fmac_f32_e32 v5, s13, v9                                 // 000000000104: 560A120D
	v_mov_b32_e32 v9, s53                                      // 000000000108: 7E120235
	v_fmac_f32_e32 v4, s2, v10                                 // 00000000010C: 56081402
	v_fmac_f32_e32 v6, s6, v10                                 // 000000000110: 560C1406
	v_fmac_f32_e32 v7, s10, v10                                // 000000000114: 560E140A
	v_fmac_f32_e32 v5, s14, v10                                // 000000000118: 560A140E
	v_mul_f32_e32 v13, s44, v4                                 // 00000000011C: 101A082C
	v_mul_f32_e32 v14, s48, v4                                 // 000000000120: 101C0830
	v_subrev_f32_e32 v18, s53, v6                              // 000000000124: 0A240C35
	v_mul_f32_e32 v15, s36, v4                                 // 000000000128: 101E0824
	v_subrev_f32_e32 v17, s52, v4                              // 00000000012C: 0A220834
	v_fmac_f32_e32 v13, s45, v6                                // 000000000130: 561A0C2D
	v_fmac_f32_e32 v14, s49, v6                                // 000000000134: 561C0C31
	v_mul_f32_e32 v20, v18, v18                                // 000000000138: 10282512
	v_mul_f32_e32 v16, s40, v4                                 // 00000000013C: 10200828
	v_subrev_f32_e32 v19, s35, v7                              // 000000000140: 0A260E23
	v_fmac_f32_e32 v13, s46, v7                                // 000000000144: 561A0E2E
	v_fmac_f32_e32 v15, s37, v6                                // 000000000148: 561E0C25
	v_fmac_f32_e32 v14, s50, v7                                // 00000000014C: 561C0E32
	v_fmac_f32_e32 v20, v17, v17                               // 000000000150: 56282311
	v_fmac_f32_e32 v16, s41, v6                                // 000000000154: 56200C29
	v_fmac_f32_e32 v13, s47, v5                                // 000000000158: 561A0A2F
	v_fmac_f32_e32 v15, s38, v7                                // 00000000015C: 561E0E26
	v_fmac_f32_e32 v14, s51, v5                                // 000000000160: 561C0A33
	v_fmac_f32_e32 v20, v19, v19                               // 000000000164: 56282713
	v_fmac_f32_e32 v16, s42, v7                                // 000000000168: 56200E2A
	v_mul_f32_e32 v10, s16, v13                                // 00000000016C: 10141A10
	v_mul_f32_e32 v11, s20, v13                                // 000000000170: 10161A14
	v_mul_f32_e32 v12, s24, v13                                // 000000000174: 10181A18
	v_mul_f32_e32 v13, s28, v13                                // 000000000178: 101A1A1C
	v_fmac_f32_e32 v15, s39, v5                                // 00000000017C: 561E0A27
	v_rsq_f32_e32 v20, v20                                     // 000000000180: 7E285D14
	v_fmac_f32_e32 v10, s17, v14                               // 000000000184: 56141C11
	v_fmac_f32_e32 v11, s21, v14                               // 000000000188: 56161C15
	v_fmac_f32_e32 v12, s25, v14                               // 00000000018C: 56181C19
	v_fmac_f32_e32 v13, s29, v14                               // 000000000190: 561A1C1D
	v_fmac_f32_e32 v16, s43, v5                                // 000000000194: 56200A2B
	v_fmac_f32_e32 v10, s18, v15                               // 000000000198: 56141E12
	v_fmac_f32_e32 v11, s22, v15                               // 00000000019C: 56161E16
	v_fmac_f32_e32 v12, s26, v15                               // 0000000001A0: 56181E1A
	v_fmac_f32_e32 v13, s30, v15                               // 0000000001A4: 561A1E1E
	v_mul_legacy_f32_e32 v14, v17, v20                         // 0000000001A8: 0E1C2911
	v_mul_legacy_f32_e32 v15, v18, v20                         // 0000000001AC: 0E1E2912
	v_mul_legacy_f32_e32 v17, v19, v20                         // 0000000001B0: 0E222913
	v_fmac_f32_e32 v10, s19, v16                               // 0000000001B4: 56142013
	v_fmac_f32_e32 v11, s23, v16                               // 0000000001B8: 56162017
	v_fmac_f32_e32 v12, s27, v16                               // 0000000001BC: 5618201B
	v_fmac_f32_e32 v13, s31, v16                               // 0000000001C0: 561A201F
	v_mov_b32_e32 v16, s52                                     // 0000000001C4: 7E200234
_L1:
	s_or_b32 exec_lo, exec_lo, s34                             // 0000000001C8: 887E227E
	s_mov_b32 s1, exec_lo                                      // 0000000001CC: BE81037E
	v_cmpx_gt_u32_e64 s33, v3                                  // 0000000001D0: D4D4007E 00020621
	s_cbranch_execz _L2                                        // 0000000001D8: BF880002
	exp prim v0, off, off, off done                            // 0000000001DC: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000001E4: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000001E8: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000001EC: BE803C6A
	s_cbranch_execz _L3                                        // 0000000001F0: BF88000B
	exp pos0 v10, v11, v12, v13 done                           // 0000000001F4: F80008CF 0D0C0B0A
	exp param3 v16, v9, v8, off                                // 0000000001FC: F8000237 00080910
	exp param1 v4, v6, v7, off                                 // 000000000204: F8000217 00070604
	exp param2 v14, v15, v17, off                              // 00000000020C: F8000227 00110F0E
	s_waitcnt vmcnt(0)                                         // 000000000214: BF8C3F70
	exp param0 v1, v2, off, off                                // 000000000218: F8000203 00000201
_L3:
	s_endpgm                                                   // 000000000220: BF810000

@echo off
setlocal

echo ========================================
echo HLSL Optimizing Converter Build Script
echo ========================================

REM Parse command line arguments
set BUILD_TYPE=Debug
set CLEAN_BUILD=0
set TARGET=all

:parse_args
if "%1"=="" goto end_parse
if /i "%1"=="clean" set CLEAN_BUILD=1
if /i "%1"=="release" set BUILD_TYPE=Release
if /i "%1"=="debug" set BUILD_TYPE=Debug
if /i "%1"=="hlsloptconv" set TARGET=hlsloptconv
if /i "%1"=="four" set TARGET=four
if /i "%1"=="help" goto show_help
if /i "%1"=="-h" goto show_help
if /i "%1"=="--help" goto show_help
shift
goto parse_args
:end_parse

REM Check if CMake is available
cmake --version >nul
if errorlevel 1 (
    echo WARNING: CMake not found. Using legacy build system.
    echo For best experience, install CMake from: https://cmake.org/download/
    echo.
    goto legacy_build
)

REM Set build directory
set BUILD_DIR=build

REM Clean build if requested
if %CLEAN_BUILD%==1 (
    echo Cleaning build directory...
    if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
)

REM Create build directory
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

REM Configure with CMake
echo.
echo Configuring project with CMake...
echo Build Type: %BUILD_TYPE%
echo Target: %TARGET%
echo.

cd "%BUILD_DIR%"
cmake .. -DCMAKE_BUILD_TYPE=%BUILD_TYPE%
if errorlevel 1 (
    echo ERROR: CMake configuration failed
    cd ..
    exit /b 1
)

REM Build the project
echo.
echo Building project...
if "%TARGET%"=="all" (
    cmake --build . --config %BUILD_TYPE%
) else (
    cmake --build . --config %BUILD_TYPE% --target %TARGET%
)

if errorlevel 1 (
    echo ERROR: Build failed
    cd ..
    exit /b 1
)

cd ..

REM Copy executables to bin directory
if not exist "bin" mkdir "bin"
if exist "%BUILD_DIR%\Debug\hlsloptconv.exe" copy "%BUILD_DIR%\Debug\hlsloptconv.exe" "bin\" >nul
if exist "%BUILD_DIR%\Release\hlsloptconv.exe" copy "%BUILD_DIR%\Release\hlsloptconv.exe" "bin\" >nul
if exist "%BUILD_DIR%\Debug\four.exe" copy "%BUILD_DIR%\Debug\four.exe" "bin\" >nul
if exist "%BUILD_DIR%\Release\four.exe" copy "%BUILD_DIR%\Release\four.exe" "bin\" >nul

REM Show results
echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Output directory: bin\
echo.
if exist "bin\hlsloptconv.exe" (
    echo * hlsloptconv.exe - Command line shader optimizer
)
if exist "bin\four.exe" (
    echo * four.exe - Graphics API demonstration
)
echo.
echo Usage examples:
echo   bin\hlsloptconv.exe -f glsl_450 -s vertex -o output.glsl input.hlsl
if exist "bin\four.exe" (
    echo   bin\four.exe
)
echo.
goto end

:show_help
echo.
echo HLSL Optimizing Converter Build Script
echo.
echo Usage: build.bat [options] [target]
echo.
echo Options:
echo   clean          Clean build directory before building
echo   debug          Build in Debug mode (default)
echo   release        Build in Release mode
echo   help, -h       Show this help message
echo.
echo Targets:
echo   (none)         Build all targets (default)
echo   hlsloptconv    Build only hlsloptconv.exe
echo   four           Build only four.exe (Windows graphics demo)
echo.
echo Examples:
echo   build.bat                    # Build all in Debug mode
echo   build.bat release            # Build all in Release mode
echo   build.bat clean debug        # Clean build in Debug mode
echo   build.bat hlsloptconv        # Build only command line tool
echo   build.bat four release       # Build only demo in Release mode
echo.
goto end

:legacy_build
echo.
echo Using legacy build system...
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" x64 >nul
if errorlevel 1 (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvarsall.bat" x64 >nul
    if errorlevel 1 (
        echo ERROR: Visual Studio environment not found.
        echo Please install Visual Studio with C++ development tools.
        exit /b 1
    )
)

REM Create directories
if not exist obj mkdir obj
if not exist bin mkdir bin

REM Compile source files
echo Compiling source files...
cl /nologo /c /Foobj/common.obj src/common.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

cl /nologo /c /Foobj/hlslparser.obj src/hlslparser.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

cl /nologo /c /Foobj/compiler.obj src/compiler.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

cl /nologo /c /Foobj/optimizer.obj src/optimizer.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

cl /nologo /c /Foobj/generator.obj src/generator.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

cl /nologo /c /Foobj/fileutils.obj src/fileutils.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

cl /nologo /c /Foobj/cli.obj src/tools/cli.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

REM Link hlsloptconv.exe
echo Linking hlsloptconv.exe...
link /nologo /out:bin/hlsloptconv.exe obj/common.obj obj/hlslparser.obj obj/compiler.obj obj/optimizer.obj obj/generator.obj obj/fileutils.obj obj/cli.obj /DEBUG user32.lib
if errorlevel 1 goto build_error

REM Build four.exe if requested or by default
if "%TARGET%"=="hlsloptconv" goto build_complete

echo Compiling four.cpp...
cl /nologo /c /Foobj/four.obj src/tools/four.cpp /MDd /GR- /D_DEBUG /Zi /Isrc
if errorlevel 1 goto build_error

echo Linking four.exe...
link /nologo /out:bin/four.exe obj/common.obj obj/hlslparser.obj obj/compiler.obj obj/optimizer.obj obj/generator.obj obj/fileutils.obj obj/four.obj /DEBUG user32.lib gdi32.lib msimg32.lib d3d9.lib d3d11.lib d3dcompiler.lib OpenGL32.lib
if errorlevel 1 goto build_error

:build_complete
echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Output directory: bin\
echo.
if exist bin\hlsloptconv.exe echo * hlsloptconv.exe - Command line shader optimizer
if exist bin\four.exe echo * four.exe - Graphics API demonstration
echo.
echo Usage examples:
echo   bin\hlsloptconv.exe -f glsl_450 -s vertex -o output.glsl input.hlsl
if exist bin\four.exe echo   bin\four.exe
echo.
goto end

:build_error
echo.
echo ERROR: Build failed!
echo Please check the error messages above.
exit /b 1

:end

_amdgpu_ps_main:
	s_mov_b64 s[44:45], exec                                   // 000000000000: BEAC047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s46, s1                                          // 000000000008: BEAE0301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s47, s1                                          // 000000000014: BEAF0301
	v_interp_p1_f32_e32 v27, v0, attr3.x                       // 000000000018: C86C0C00
	s_load_dwordx4 s[36:39], s[46:47], null                    // 00000000001C: F4080917 FA000000
	v_interp_p1_f32_e32 v28, v0, attr3.y                       // 000000000024: C8700D00
	v_interp_p1_f32_e32 v7, v0, attr2.y                        // 000000000028: C81C0900
	v_interp_p1_f32_e32 v6, v0, attr2.x                        // 00000000002C: C8180800
	v_interp_p2_f32_e32 v27, v1, attr3.x                       // 000000000030: C86D0C01
	v_interp_p1_f32_e32 v8, v0, attr2.z                        // 000000000034: C8200A00
	v_interp_p2_f32_e32 v28, v1, attr3.y                       // 000000000038: C8710D01
	v_interp_p2_f32_e32 v7, v1, attr2.y                        // 00000000003C: C81D0901
	v_interp_p1_f32_e32 v10, v0, attr1.y                       // 000000000040: C8280500
	v_interp_p2_f32_e32 v6, v1, attr2.x                        // 000000000044: C8190801
	v_interp_p1_f32_e32 v9, v0, attr1.x                        // 000000000048: C8240400
	v_interp_p1_f32_e32 v14, v0, attr0.y                       // 00000000004C: C8380100
	v_mul_f32_e32 v16, v7, v7                                  // 000000000050: 10200F07
	v_interp_p2_f32_e32 v8, v1, attr2.z                        // 000000000054: C8210A01
	v_interp_p2_f32_e32 v10, v1, attr1.y                       // 000000000058: C8290501
	v_interp_p1_f32_e32 v11, v0, attr1.z                       // 00000000005C: C82C0600
	v_interp_p1_f32_e32 v13, v0, attr0.x                       // 000000000060: C8340000
	v_fmac_f32_e32 v16, v6, v6                                 // 000000000064: 56200D06
	v_interp_p2_f32_e32 v9, v1, attr1.x                        // 000000000068: C8250401
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	s_buffer_load_dwordx2 s[34:35], s[36:39], 0x150            // 000000000070: F4240892 FA000150
	s_clause 0x1                                               // 000000000078: BFA10001
	s_load_dwordx4 s[40:43], s[46:47], 0x20                    // 00000000007C: F4080A17 FA000020
	s_load_dwordx16 s[0:15], s[46:47], 0x30                    // 000000000084: F4100017 FA000030
	v_interp_p2_f32_e32 v14, v1, attr0.y                       // 00000000008C: C8390101
	v_mul_f32_e32 v18, v10, v10                                // 000000000090: 1024150A
	v_fmac_f32_e32 v16, v8, v8                                 // 000000000094: 56201108
	v_interp_p1_f32_e32 v15, v0, attr0.z                       // 000000000098: C83C0200
	v_interp_p2_f32_e32 v11, v1, attr1.z                       // 00000000009C: C82D0601
	v_interp_p2_f32_e32 v13, v1, attr0.x                       // 0000000000A0: C8350001
	v_mul_f32_e32 v19, v14, v14                                // 0000000000A4: 10261D0E
	v_fmac_f32_e32 v18, v9, v9                                 // 0000000000A8: 56241309
	v_rsq_f32_e32 v21, v16                                     // 0000000000AC: 7E2A5D10
	v_interp_p2_f32_e32 v15, v1, attr0.z                       // 0000000000B0: C83D0201
	v_cmp_neq_f32_e32 vcc_lo, 0, v16                           // 0000000000B4: 7C1A2080
	v_fmac_f32_e32 v19, v13, v13                               // 0000000000B8: 56261B0D
	v_fmac_f32_e32 v18, v11, v11                               // 0000000000BC: 5624170B
	v_interp_p1_f32_e32 v23, v0, attr4.y                       // 0000000000C0: C85C1100
	v_interp_p1_f32_e32 v20, v0, attr4.x                       // 0000000000C4: C8501000
	v_interp_p1_f32_e32 v25, v0, attr4.z                       // 0000000000C8: C8641200
	v_fmac_f32_e32 v19, v15, v15                               // 0000000000CC: 56261F0F
	s_waitcnt lgkmcnt(0)                                       // 0000000000D0: BF8CC07F
	v_mul_f32_e64 v2, 0x3da2f983, s34                          // 0000000000D4: D5080002 000044FF 3DA2F983
	v_rsq_f32_e32 v24, v18                                     // 0000000000E0: 7E305D12
	v_cndmask_b32_e32 v16, 0, v21, vcc_lo                      // 0000000000E4: 02202A80
	v_interp_p2_f32_e32 v23, v1, attr4.y                       // 0000000000E8: C85D1101
	v_rsq_f32_e32 v26, v19                                     // 0000000000EC: 7E345D13
	v_sin_f32_e32 v2, v2                                       // 0000000000F0: 7E046B02
	v_interp_p2_f32_e32 v20, v1, attr4.x                       // 0000000000F4: C8511001
	v_mul_f32_e32 v7, v16, v7                                  // 0000000000F8: 100E0F10
	v_mul_f32_e32 v6, v16, v6                                  // 0000000000FC: 100C0D10
	v_mul_f32_e32 v8, v16, v8                                  // 000000000100: 10101110
	v_interp_p2_f32_e32 v25, v1, attr4.z                       // 000000000104: C8651201
	s_load_dwordx16 s[16:31], s[46:47], 0x70                   // 000000000108: F4100417 FA000070
	v_fmac_f32_e32 v27, 0x3c23d70a, v2                         // 000000000110: 563604FF 3C23D70A
	v_fmac_f32_e32 v28, 0x3c23d70a, v2                         // 000000000118: 563804FF 3C23D70A
	image_sample v[2:4], v[27:28], s[0:7], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000120: F0800708 0140021B
	s_load_dwordx8 s[0:7], s[46:47], null                      // 000000000128: F40C0017 FA000000
	s_waitcnt vmcnt(0)                                         // 000000000130: BF8C3F70
	v_fma_f32 v3, v3, 2.0, -1.0                                // 000000000134: D54B0003 03CDE903
	v_fma_f32 v2, v2, 2.0, -1.0                                // 00000000013C: D54B0002 03CDE902
	v_fma_f32 v4, v4, 2.0, -1.0                                // 000000000144: D54B0004 03CDE904
	v_mul_f32_e32 v17, v3, v3                                  // 00000000014C: 10220703
	v_fmac_f32_e32 v17, v2, v2                                 // 000000000150: 56220502
	v_fmac_f32_e32 v17, v4, v4                                 // 000000000154: 56220904
	v_rsq_f32_e32 v22, v17                                     // 000000000158: 7E2C5D11
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 00000000015C: 7C1A2280
	v_cndmask_b32_e32 v17, 0, v22, vcc_lo                      // 000000000160: 02222C80
	v_cmp_neq_f32_e32 vcc_lo, 0, v18                           // 000000000164: 7C1A2480
	v_mul_f32_e32 v3, v17, v3                                  // 000000000168: 10060711
	v_cndmask_b32_e32 v18, 0, v24, vcc_lo                      // 00000000016C: 02243080
	v_cmp_neq_f32_e32 vcc_lo, 0, v19                           // 000000000170: 7C1A2680
	v_mul_f32_e32 v2, v17, v2                                  // 000000000174: 10040511
	v_mul_f32_e32 v4, v17, v4                                  // 000000000178: 10080911
	v_mul_f32_e32 v7, v3, v7                                   // 00000000017C: 100E0F03
	v_mul_f32_e32 v10, v18, v10                                // 000000000180: 10141512
	v_cndmask_b32_e32 v19, 0, v26, vcc_lo                      // 000000000184: 02263480
	v_mul_f32_e32 v9, v18, v9                                  // 000000000188: 10121312
	v_mul_f32_e32 v6, v3, v6                                   // 00000000018C: 100C0D03
	v_mul_f32_e32 v3, v3, v8                                   // 000000000190: 10061103
	v_fmac_f32_e32 v7, v2, v10                                 // 000000000194: 560E1502
	v_mul_f32_e32 v14, v19, v14                                // 000000000198: 101C1D13
	v_mul_f32_e32 v10, v18, v11                                // 00000000019C: 10141712
	v_mul_f32_e32 v11, v19, v13                                // 0000000001A0: 10161B13
	v_fmac_f32_e32 v6, v2, v9                                  // 0000000001A4: 560C1302
	v_mul_f32_e32 v8, v23, v23                                 // 0000000001A8: 10102F17
	v_fmac_f32_e32 v7, v4, v14                                 // 0000000001AC: 560E1D04
	v_mul_f32_e32 v9, v19, v15                                 // 0000000001B0: 10121F13
	v_fmac_f32_e32 v3, v2, v10                                 // 0000000001B4: 56061502
	v_fmac_f32_e32 v6, v4, v11                                 // 0000000001B8: 560C1704
	v_fmac_f32_e32 v8, v20, v20                                // 0000000001BC: 56102914
	v_mul_f32_e32 v2, v7, v7                                   // 0000000001C0: 10040F07
	v_fmac_f32_e32 v3, v4, v9                                  // 0000000001C4: 56061304
	v_fmac_f32_e32 v8, v25, v25                                // 0000000001C8: 56103319
	v_fmac_f32_e32 v2, v6, v6                                  // 0000000001CC: 56040D06
	v_rsq_f32_e32 v4, v8                                       // 0000000001D0: 7E085D08
	v_fmac_f32_e32 v2, v3, v3                                  // 0000000001D4: 56040703
	v_cmp_neq_f32_e32 vcc_lo, 0, v8                            // 0000000001D8: 7C1A1080
	v_rsq_f32_e32 v9, v2                                       // 0000000001DC: 7E125D02
	v_cndmask_b32_e32 v4, 0, v4, vcc_lo                        // 0000000001E0: 02080880
	v_cmp_neq_f32_e32 vcc_lo, 0, v2                            // 0000000001E4: 7C1A0480
	v_mul_f32_e32 v15, v4, v20                                 // 0000000001E8: 101E2904
	v_cndmask_b32_e32 v2, 0, v9, vcc_lo                        // 0000000001EC: 02041280
	v_mul_f32_e32 v17, v4, v23                                 // 0000000001F0: 10222F04
	v_mul_f32_e32 v20, v4, v25                                 // 0000000001F4: 10283304
	v_mul_f32_e32 v16, v2, v6                                  // 0000000001F8: 10200D02
	v_mul_f32_e32 v18, v2, v7                                  // 0000000001FC: 10240F02
	v_mul_f32_e32 v21, v2, v3                                  // 000000000200: 102A0702
	s_waitcnt lgkmcnt(0)                                       // 000000000204: BF8CC07F
	image_sample v[6:8], v[27:28], s[16:23], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000208: F0800708 0144061B
	v_mul_f32_e32 v19, v15, v16                                // 000000000210: 1026210F
	v_fma_f32 v2, v18, -v17, -v19                              // 000000000214: D54B0002 C44E2312
	v_fma_f32 v13, -v20, v21, v2 mul:2                         // 00000000021C: D54B000D 2C0A2B14
	image_sample v[9:11], v[27:28], s[8:15], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000224: F0800708 0142091B
	image_sample v[2:5], v[27:28], s[0:7], s[40:43] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000022C: F0800F08 0140021B
	v_fma_f32 v12, v16, v13, v15                               // 000000000234: D54B000C 043E1B10
	v_fma_f32 v14, -v18, v13, -v17                             // 00000000023C: D54B000E A4461B12
	v_fma_f32 v13, v21, v13, v20                               // 000000000244: D54B000D 04521B15
	v_cubema_f32 v22, -v12, v14, -v13                          // 00000000024C: D5470016 A4361D0C
	v_cubeid_f32 v23, -v12, v14, -v13                          // 000000000254: D5440017 A4361D0C
	v_cubesc_f32 v24, -v12, v14, -v13                          // 00000000025C: D5450018 A4361D0C
	v_cubetc_f32 v12, -v12, v14, -v13                          // 000000000264: D546000C A4361D0C
	v_rcp_f32_e64 v22, |v22|                                   // 00000000026C: D5AA0116 00000116
	v_rndne_f32_e32 v25, v23                                   // 000000000274: 7E324717
	v_fmaak_f32 v23, v22, v24, 0x3fc00000                      // 000000000278: 5A2E3116 3FC00000
	v_fmaak_f32 v24, v22, v12, 0x3fc00000                      // 000000000280: 5A301916 3FC00000
	s_and_b64 exec, exec, s[44:45]                             // 000000000288: 87FE2C7E
	image_sample v[12:14], v[23:25], s[24:31], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_CUBE// 00000000028C: F0800718 01460C17
	v_interp_p1_f32_e32 v22, v0, attr5.y                       // 000000000294: C8581500
	v_interp_p1_f32_e32 v23, v0, attr5.x                       // 000000000298: C85C1400
	v_interp_p1_f32_e32 v0, v0, attr5.z                        // 00000000029C: C8001600
	v_fmac_f32_e32 v19, v18, v17                               // 0000000002A0: 56262312
	s_buffer_load_dwordx8 s[0:7], s[36:39], 0x110              // 0000000002A4: F42C0012 FA000110
	v_interp_p2_f32_e32 v22, v1, attr5.y                       // 0000000002AC: C8591501
	v_interp_p2_f32_e32 v23, v1, attr5.x                       // 0000000002B0: C85D1401
	v_interp_p2_f32_e32 v0, v1, attr5.z                        // 0000000002B4: C8011601
	v_fmac_f32_e32 v19, v20, v21                               // 0000000002B8: 56262B14
	s_buffer_load_dwordx4 s[8:11], s[36:39], 0x130             // 0000000002BC: F4280212 FA000130
	s_waitcnt lgkmcnt(0)                                       // 0000000002C4: BF8CC07F
	s_buffer_load_dword s3, s[36:39], 0x12c                    // 0000000002C8: F42000D2 FA00012C
	v_mul_f32_e32 v24, v22, v22                                // 0000000002D0: 10302D16
	s_waitcnt vmcnt(1)                                         // 0000000002D4: BF8C3F71
	v_mul_f32_e32 v4, 0x3ecccccd, v4                           // 0000000002D8: 100808FF 3ECCCCCD
	v_mul_f32_e32 v3, 0x3f19999a, v3                           // 0000000002E0: 100606FF 3F19999A
	v_mul_f32_e32 v2, 0x3f4ccccd, v2                           // 0000000002E8: 100404FF 3F4CCCCD
	v_mul_f32_e32 v7, 0x3d4ccccd, v7                           // 0000000002F0: 100E0EFF 3D4CCCCD
	v_fmac_f32_e32 v24, v23, v23                               // 0000000002F8: 56302F17
	v_mul_f32_e32 v6, 0x3dcccccd, v6                           // 0000000002FC: 100C0CFF 3DCCCCCD
	v_fmac_f32_e32 v24, v0, v0                                 // 000000000304: 56300100
	v_mul_f32_e32 v4, s6, v4                                   // 000000000308: 10080806
	v_mul_f32_e32 v3, s5, v3                                   // 00000000030C: 10060605
	v_rsq_f32_e32 v1, v24                                      // 000000000310: 7E025D18
	v_cmp_neq_f32_e32 vcc_lo, 0, v24                           // 000000000314: 7C1A3080
	v_mul_f32_e32 v2, s4, v2                                   // 000000000318: 10040404
	v_mul_f32_e32 v11, s10, v11                                // 00000000031C: 1016160A
	v_mul_f32_e32 v10, s9, v10                                 // 000000000320: 10141409
	v_mul_f32_e32 v9, s8, v9                                   // 000000000324: 10121208
	v_mul_f32_e32 v6, s4, v6                                   // 000000000328: 100C0C04
	v_mul_f32_e32 v7, s5, v7                                   // 00000000032C: 100E0E05
	v_cndmask_b32_e32 v1, 0, v1, vcc_lo                        // 000000000330: 02020280
	v_fmac_f32_e32 v17, v1, v22                                // 000000000334: 56222D01
	v_fmac_f32_e32 v15, v1, v23                                // 000000000338: 561E2F01
	v_fmac_f32_e32 v20, v1, v0                                 // 00000000033C: 56280101
	v_mul_f32_e32 v0, v1, v0                                   // 000000000340: 10000101
	v_mul_f32_e32 v24, v17, v17                                // 000000000344: 10302311
	v_fmac_f32_e32 v24, v15, v15                               // 000000000348: 56301F0F
	v_fmac_f32_e32 v24, v20, v20                               // 00000000034C: 56302914
	v_rsq_f32_e32 v25, v24                                     // 000000000350: 7E325D18
	v_cmp_neq_f32_e32 vcc_lo, 0, v24                           // 000000000354: 7C1A3080
	v_cndmask_b32_e32 v24, 0, v25, vcc_lo                      // 000000000358: 02303280
	v_mul_f32_e32 v17, v24, v17                                // 00000000035C: 10222318
	v_mul_f32_e32 v15, v24, v15                                // 000000000360: 101E1F18
	v_mul_f32_e32 v20, v24, v20                                // 000000000364: 10282918
	v_mul_f32_e32 v17, v18, v17                                // 000000000368: 10222312
	v_fmac_f32_e32 v17, v16, v15                               // 00000000036C: 56221F10
	v_mul_f32_e32 v15, v1, v23                                 // 000000000370: 101E2F01
	v_fmac_f32_e32 v17, v21, v20                               // 000000000374: 56222915
	v_mul_f32_e32 v20, v1, v22                                 // 000000000378: 10282D01
	v_mul_f32_e32 v1, 0, v8                                    // 00000000037C: 10021080
	v_max_f32_e32 v8, 0, v17                                   // 000000000380: 20102280
	v_mul_f32_e32 v17, v18, v20                                // 000000000384: 10222912
	v_sub_f32_e32 v18, 1.0, v19                                // 000000000388: 082426F2
	v_mul_f32_e32 v1, s6, v1                                   // 00000000038C: 10020206
	v_log_f32_e32 v8, v8                                       // 000000000390: 7E104F08
	v_fmac_f32_e32 v17, v16, v15                               // 000000000394: 56221F10
	v_mul_f32_e32 v15, v18, v18                                // 000000000398: 101E2512
	v_fmac_f32_e32 v17, v21, v0                                // 00000000039C: 56220115
	v_mul_legacy_f32_e32 v0, s35, v8                           // 0000000003A0: 0E001023
	v_mul_f32_e32 v8, v15, v15                                 // 0000000003A4: 10101F0F
	v_max_f32_e32 v15, 0, v17                                  // 0000000003A8: 201E2280
	v_exp_f32_e32 v0, v0                                       // 0000000003AC: 7E004B00
	v_mul_f32_e32 v8, v18, v8                                  // 0000000003B0: 10101112
	v_mul_f32_e32 v4, v4, v15                                  // 0000000003B4: 10081F04
	v_mul_f32_e32 v3, v3, v15                                  // 0000000003B8: 10061F03
	v_mul_f32_e32 v2, v2, v15                                  // 0000000003BC: 10041F02
	v_mul_f32_e32 v8, 0x3e99999a, v8                           // 0000000003C0: 101010FF 3E99999A
	v_fmac_f32_e32 v4, v0, v11                                 // 0000000003C8: 56081700
	v_fmac_f32_e32 v3, v0, v10                                 // 0000000003CC: 56061500
	v_fmac_f32_e32 v2, v0, v9                                  // 0000000003D0: 56041300
	s_waitcnt vmcnt(0)                                         // 0000000003D4: BF8C3F70
	v_fmac_f32_e32 v6, v8, v12                                 // 0000000003D8: 560C1908
	v_fmac_f32_e32 v7, v8, v13                                 // 0000000003DC: 560E1B08
	v_fmac_f32_e32 v1, v8, v14                                 // 0000000003E0: 56021D08
	v_fmac_f32_e32 v6, s0, v2                                  // 0000000003E4: 560C0400
	v_fmac_f32_e32 v7, s1, v3                                  // 0000000003E8: 560E0601
	v_fmac_f32_e32 v1, s2, v4                                  // 0000000003EC: 56020802
	v_add_f32_e32 v0, 0x40400000, v6                           // 0000000003F0: 06000CFF 40400000
	v_add_f32_e32 v2, 0x4051eb86, v7                           // 0000000003F8: 06040EFF 4051EB86
	v_add_f32_e32 v3, 0x42180000, v1                           // 000000000400: 060602FF 42180000
	v_add_f32_e32 v4, 2.0, v6                                  // 000000000408: 06080CF4
	v_add_f32_e32 v6, 0x4011eb86, v7                           // 00000000040C: 060C0EFF 4011EB86
	v_rcp_f32_e32 v0, v0                                       // 000000000414: 7E005500
	v_rcp_f32_e32 v2, v2                                       // 000000000418: 7E045502
	v_rcp_f32_e32 v3, v3                                       // 00000000041C: 7E065503
	v_add_f32_e32 v1, 0x42140000, v1                           // 000000000420: 060202FF 42140000
	v_mul_f32_e32 v0, v4, v0                                   // 000000000428: 10000104
	v_mul_f32_e32 v2, v6, v2                                   // 00000000042C: 10040506
	v_mul_f32_e32 v1, v1, v3                                   // 000000000430: 10020701
	s_waitcnt lgkmcnt(0)                                       // 000000000434: BF8CC07F
	v_mul_f32_e32 v3, s3, v5                                   // 000000000438: 10060A03
	v_log_f32_e32 v0, v0                                       // 00000000043C: 7E004F00
	v_log_f32_e32 v2, v2                                       // 000000000440: 7E044F02
	v_log_f32_e32 v1, v1                                       // 000000000444: 7E024F01
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 000000000448: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 000000000450: 0E0404FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 000000000458: 0E0202FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 000000000460: 7E004B00
	v_exp_f32_e32 v2, v2                                       // 000000000464: 7E044B02
	v_exp_f32_e32 v1, v1                                       // 000000000468: 7E024B01
	v_mul_f32_e32 v0, 0x3f6d61a0, v0                           // 00000000046C: 100000FF 3F6D61A0
	v_mul_f32_e32 v2, 0x3f6d61a0, v2                           // 000000000474: 100404FF 3F6D61A0
	v_mul_f32_e32 v1, 0x3f6d61a0, v1                           // 00000000047C: 100202FF 3F6D61A0
	exp mrt0 v0, v2, v1, v3 done vm                            // 000000000484: F800180F 03010200
	s_endpgm                                                   // 00000000048C: BF810000

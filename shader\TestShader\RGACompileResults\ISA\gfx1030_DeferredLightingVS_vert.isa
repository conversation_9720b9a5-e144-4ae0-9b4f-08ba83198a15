_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s4, s2, 0x90016                                  // 00000000000C: 9384FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s9                                           // 00000000001C: BE800309
	s_cmp_lg_u32 s3, 0                                         // 000000000020: BF078003
	s_cbranch_scc1 _L0                                         // 000000000024: BF850003
	s_lshl_b32 s2, s4, 12                                      // 000000000028: 8F028C04
	s_or_b32 m0, s2, s1                                        // 00000000002C: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000030: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000034: D7650001 000100C1
	v_lshl_or_b32 v6, s3, 5, v1                                // 00000000003C: D76F0006 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v6                            // 000000000044: 7D880C01
	s_and_saveexec_b32 s2, vcc_lo                              // 000000000048: BE823C6A
	s_cbranch_execz _L1                                        // 00000000004C: BF88000A
	s_getpc_b64 s[6:7]                                         // 000000000050: BE861F00
	v_add_nc_u32_e32 v1, s10, v5                               // 000000000054: 4A020A0A
	s_mov_b32 s1, s7                                           // 000000000058: BE810307
	s_load_dwordx8 s[12:19], s[0:1], null                      // 00000000005C: F40C0300 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000064: BF8CC07F
	tbuffer_load_format_xy v[4:5], v1, s[16:19], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000068: EA012000 80040401
	tbuffer_load_format_xyz v[1:3], v1, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80030101
_L1:
	s_or_b32 exec_lo, exec_lo, s2                              // 000000000078: 887E027E
	s_mov_b32 s1, exec_lo                                      // 00000000007C: BE81037E
	v_cmpx_gt_u32_e64 s4, v6                                   // 000000000080: D4D4007E 00020C04
	s_cbranch_execz _L2                                        // 000000000088: BF880002
	exp prim v0, off, off, off done                            // 00000000008C: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 000000000094: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 000000000098: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 00000000009C: BE803C6A
	s_cbranch_execz _L3                                        // 0000000000A0: BF880006
	v_mov_b32_e32 v0, 1.0                                      // 0000000000A4: 7E0002F2
	s_waitcnt vmcnt(0)                                         // 0000000000A8: BF8C3F70
	exp pos0 v1, v2, v3, v0 done                               // 0000000000AC: F80008CF 00030201
	exp param0 v4, v5, off, off                                // 0000000000B4: F8000203 00000504
_L3:
	s_endpgm                                                   // 0000000000BC: BF810000
